part of 'custom_notification_stream_bloc.dart';

@freezed
class CustomNotificationStreamEvent with _$CustomNotificationStreamEvent {
  // Event representing a request to load the custom notification stream
  const factory CustomNotificationStreamEvent.loadCustomNotificationStream() =
      LoadCustomNotificationStream;
  // Event representing a load of notifications
  const factory CustomNotificationStreamEvent.CustomNotificationStreamLoaded(
          Do<NotificationFailure, NotificationModel?> failureOrNotification) =
      CustomNotificationStreamLoaded;
}
