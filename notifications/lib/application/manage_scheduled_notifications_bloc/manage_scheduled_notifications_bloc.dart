import 'package:bloc/bloc.dart';
import 'package:doso/doso.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/core/unit.dart';
import 'package:notifications/domain/facade/scheduled_notifications_facade.dart';
import 'package:injectable/injectable.dart';
import '../../domain/failure/notification_failure.dart';
import '../../domain/model/notification_model.dart';
import 'package:timezone/timezone.dart' as tz;

part 'manage_scheduled_notifications_event.dart';
part 'manage_scheduled_notifications_state.dart';
part 'manage_scheduled_notifications_bloc.freezed.dart';

@injectable
class ManageScheduledNotificationsBloc extends Bloc<
    ManageScheduledNotificationsEvent, ManageScheduledNotificationsState> {
  final ScheduledNotificationsFacade _notificationRepository;

  ManageScheduledNotificationsBloc(this._notificationRepository)
      : super(ManageScheduledNotificationsState.initial()) {
    on<ManageScheduledNotificationsEvent>((event, emit) => event.map(
          scheduleNotificationsWeekly: (e) => _onScheduleNotifications(e, emit),
          scheduleNotificationsDaily: (e) =>
              _onScheduleNotificationsDaily(e, emit),
          scheduleNotificationsMonthly: (e) =>
              _onScheduleNotificationsMonthly(e, emit),
          scheduleSingleNotification: (e) =>
              _onScheduleSingleNotification(e, emit),
          disableNotification: (e) => _onCancelNotification(e, emit),
        ));
  }

  Future<void> _onScheduleNotifications(_ScheduleNotifications event,
      Emitter<ManageScheduledNotificationsState> emit) async {
    emit(state.copyWith(isLoading: true, failureOrSuccessOption: null));

    print(
        'Scheduling notifications for group ID: ${event.notificationGroupId}');
    final result = await _notificationRepository.scheduleNotificationsWeekly(
      daysToBeNotified: event.daystoBeNotified,
      timeOfDay: event.timeofDay,
      body: event.body,
      notificationGroupId: event.notificationGroupId,
    );

    emit(state.copyWith(
      isLoading: false,
      failureOrSuccessOption: result,
    ));
  }

  Future<void> _onScheduleNotificationsDaily(_ScheduleNotificationsDaily event,
      Emitter<ManageScheduledNotificationsState> emit) async {
    emit(state.copyWith(isLoading: true, failureOrSuccessOption: null));

    print(
        'Scheduling notifications for group ID: ${event.notificationGroupId}');
    final result = await _notificationRepository.scheduleNotificationsDaily(
      timeOfDay: event.timeofDay,
      body: event.body,
      notificationGroupId: event.notificationGroupId,
    );

    emit(state.copyWith(
      isLoading: false,
      failureOrSuccessOption: result,
    ));
  }

  Future<void> _onScheduleNotificationsMonthly(
      _ScheduleNotificationsMonthly event,
      Emitter<ManageScheduledNotificationsState> emit) async {
    emit(state.copyWith(isLoading: true, failureOrSuccessOption: null));

    print(
        'Scheduling notifications for group ID: ${event.notificationGroupId}');
    final result = await _notificationRepository.scheduleNotificationsMonthly(
      dateTime: event.dateTime,
      body: event.body,
      notificationGroupId: event.notificationGroupId,
    );

    emit(state.copyWith(
      isLoading: false,
      failureOrSuccessOption: result,
    ));
  }

  Future<void> _onCancelNotification(_CancelNotification event,
      Emitter<ManageScheduledNotificationsState> emit) async {
    emit(state.copyWith(isLoading: true, failureOrSuccessOption: null));

    final result = await _notificationRepository
        .disableNotificationGroup(event.notificationGroupId);

    emit(state.copyWith(
      isLoading: false,
      failureOrSuccessOption: result,
    ));
  }

  Future<void> _onScheduleSingleNotification(_ScheduleSingleNotification event,
      Emitter<ManageScheduledNotificationsState> emit) async {
    emit(state.copyWith(isLoading: true, failureOrSuccessOption: null));

    print(
        'Scheduling single notification for group ID: ${event.notificationId}');
    final result = await _notificationRepository.scheduleSingleNotification(
      body: event.body,
      dateTime: event.dateTime,
      notificationId: event.notificationId,
      notificationType: '${event.notificationType}',
      payload: event.payload,
      title: event.notificationType,
      isForeground: event.isForeground,
    );

    emit(state.copyWith(
      isLoading: false,
      failureOrSuccessOption: result,
    ));
  }
}
