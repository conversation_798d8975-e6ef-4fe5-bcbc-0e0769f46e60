part of 'manage_scheduled_notifications_bloc.dart';

@freezed
class ManageScheduledNotificationsState
    with _$ManageScheduledNotificationsState {
  const factory ManageScheduledNotificationsState({
    required bool isLoading,
    // Use nullable Do<TFailure, TValue> to represent an optional result
    required Do<NotificationFailure, Unit>? failureOrSuccessOption,
    required List<NotificationModel> scheduledNotifications,
  }) = _ManageScheduledNotificationsState;

  factory ManageScheduledNotificationsState.initial() =>
      ManageScheduledNotificationsState(
        isLoading: false,
        failureOrSuccessOption: null,
        scheduledNotifications: [],
      );
}
