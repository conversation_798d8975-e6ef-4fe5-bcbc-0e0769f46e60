// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_scheduled_notifications_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$ManageScheduledNotificationsEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> daystoBeNotified,
            List<String> timeofDay, String body, String notificationGroupId)
        scheduleNotificationsWeekly,
    required TResult Function(
            List<String> timeofDay, String body, String notificationGroupId)
        scheduleNotificationsDaily,
    required TResult Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)
        scheduleNotificationsMonthly,
    required TResult Function(String notificationGroupId) disableNotification,
    required TResult Function(
            String body,
            tz.TZDateTime dateTime,
            String notificationId,
            String notificationType,
            String? payload,
            bool isForeground)
        scheduleSingleNotification,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> daystoBeNotified, List<String> timeofDay,
            String body, String notificationGroupId)?
        scheduleNotificationsWeekly,
    TResult? Function(
            List<String> timeofDay, String body, String notificationGroupId)?
        scheduleNotificationsDaily,
    TResult? Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)?
        scheduleNotificationsMonthly,
    TResult? Function(String notificationGroupId)? disableNotification,
    TResult? Function(
            String body,
            tz.TZDateTime dateTime,
            String notificationId,
            String notificationType,
            String? payload,
            bool isForeground)?
        scheduleSingleNotification,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> daystoBeNotified, List<String> timeofDay,
            String body, String notificationGroupId)?
        scheduleNotificationsWeekly,
    TResult Function(
            List<String> timeofDay, String body, String notificationGroupId)?
        scheduleNotificationsDaily,
    TResult Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)?
        scheduleNotificationsMonthly,
    TResult Function(String notificationGroupId)? disableNotification,
    TResult Function(String body, tz.TZDateTime dateTime, String notificationId,
            String notificationType, String? payload, bool isForeground)?
        scheduleSingleNotification,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ScheduleNotifications value)
        scheduleNotificationsWeekly,
    required TResult Function(_ScheduleNotificationsDaily value)
        scheduleNotificationsDaily,
    required TResult Function(_ScheduleNotificationsMonthly value)
        scheduleNotificationsMonthly,
    required TResult Function(_CancelNotification value) disableNotification,
    required TResult Function(_ScheduleSingleNotification value)
        scheduleSingleNotification,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ScheduleNotifications value)?
        scheduleNotificationsWeekly,
    TResult? Function(_ScheduleNotificationsDaily value)?
        scheduleNotificationsDaily,
    TResult? Function(_ScheduleNotificationsMonthly value)?
        scheduleNotificationsMonthly,
    TResult? Function(_CancelNotification value)? disableNotification,
    TResult? Function(_ScheduleSingleNotification value)?
        scheduleSingleNotification,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ScheduleNotifications value)? scheduleNotificationsWeekly,
    TResult Function(_ScheduleNotificationsDaily value)?
        scheduleNotificationsDaily,
    TResult Function(_ScheduleNotificationsMonthly value)?
        scheduleNotificationsMonthly,
    TResult Function(_CancelNotification value)? disableNotification,
    TResult Function(_ScheduleSingleNotification value)?
        scheduleSingleNotification,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManageScheduledNotificationsEventCopyWith<$Res> {
  factory $ManageScheduledNotificationsEventCopyWith(
          ManageScheduledNotificationsEvent value,
          $Res Function(ManageScheduledNotificationsEvent) then) =
      _$ManageScheduledNotificationsEventCopyWithImpl<$Res,
          ManageScheduledNotificationsEvent>;
}

/// @nodoc
class _$ManageScheduledNotificationsEventCopyWithImpl<$Res,
        $Val extends ManageScheduledNotificationsEvent>
    implements $ManageScheduledNotificationsEventCopyWith<$Res> {
  _$ManageScheduledNotificationsEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$ScheduleNotificationsImplCopyWith<$Res> {
  factory _$$ScheduleNotificationsImplCopyWith(
          _$ScheduleNotificationsImpl value,
          $Res Function(_$ScheduleNotificationsImpl) then) =
      __$$ScheduleNotificationsImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {List<String> daystoBeNotified,
      List<String> timeofDay,
      String body,
      String notificationGroupId});
}

/// @nodoc
class __$$ScheduleNotificationsImplCopyWithImpl<$Res>
    extends _$ManageScheduledNotificationsEventCopyWithImpl<$Res,
        _$ScheduleNotificationsImpl>
    implements _$$ScheduleNotificationsImplCopyWith<$Res> {
  __$$ScheduleNotificationsImplCopyWithImpl(_$ScheduleNotificationsImpl _value,
      $Res Function(_$ScheduleNotificationsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? daystoBeNotified = null,
    Object? timeofDay = null,
    Object? body = null,
    Object? notificationGroupId = null,
  }) {
    return _then(_$ScheduleNotificationsImpl(
      daystoBeNotified: null == daystoBeNotified
          ? _value._daystoBeNotified
          : daystoBeNotified // ignore: cast_nullable_to_non_nullable
              as List<String>,
      timeofDay: null == timeofDay
          ? _value._timeofDay
          : timeofDay // ignore: cast_nullable_to_non_nullable
              as List<String>,
      body: null == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      notificationGroupId: null == notificationGroupId
          ? _value.notificationGroupId
          : notificationGroupId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ScheduleNotificationsImpl implements _ScheduleNotifications {
  const _$ScheduleNotificationsImpl(
      {required final List<String> daystoBeNotified,
      required final List<String> timeofDay,
      required this.body,
      required this.notificationGroupId})
      : _daystoBeNotified = daystoBeNotified,
        _timeofDay = timeofDay;

  final List<String> _daystoBeNotified;
  @override
  List<String> get daystoBeNotified {
    if (_daystoBeNotified is EqualUnmodifiableListView)
      return _daystoBeNotified;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_daystoBeNotified);
  }

  final List<String> _timeofDay;
  @override
  List<String> get timeofDay {
    if (_timeofDay is EqualUnmodifiableListView) return _timeofDay;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_timeofDay);
  }

  @override
  final String body;
  @override
  final String notificationGroupId;

  @override
  String toString() {
    return 'ManageScheduledNotificationsEvent.scheduleNotificationsWeekly(daystoBeNotified: $daystoBeNotified, timeofDay: $timeofDay, body: $body, notificationGroupId: $notificationGroupId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ScheduleNotificationsImpl &&
            const DeepCollectionEquality()
                .equals(other._daystoBeNotified, _daystoBeNotified) &&
            const DeepCollectionEquality()
                .equals(other._timeofDay, _timeofDay) &&
            (identical(other.body, body) || other.body == body) &&
            (identical(other.notificationGroupId, notificationGroupId) ||
                other.notificationGroupId == notificationGroupId));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_daystoBeNotified),
      const DeepCollectionEquality().hash(_timeofDay),
      body,
      notificationGroupId);

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ScheduleNotificationsImplCopyWith<_$ScheduleNotificationsImpl>
      get copyWith => __$$ScheduleNotificationsImplCopyWithImpl<
          _$ScheduleNotificationsImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> daystoBeNotified,
            List<String> timeofDay, String body, String notificationGroupId)
        scheduleNotificationsWeekly,
    required TResult Function(
            List<String> timeofDay, String body, String notificationGroupId)
        scheduleNotificationsDaily,
    required TResult Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)
        scheduleNotificationsMonthly,
    required TResult Function(String notificationGroupId) disableNotification,
    required TResult Function(
            String body,
            tz.TZDateTime dateTime,
            String notificationId,
            String notificationType,
            String? payload,
            bool isForeground)
        scheduleSingleNotification,
  }) {
    return scheduleNotificationsWeekly(
        daystoBeNotified, timeofDay, body, notificationGroupId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> daystoBeNotified, List<String> timeofDay,
            String body, String notificationGroupId)?
        scheduleNotificationsWeekly,
    TResult? Function(
            List<String> timeofDay, String body, String notificationGroupId)?
        scheduleNotificationsDaily,
    TResult? Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)?
        scheduleNotificationsMonthly,
    TResult? Function(String notificationGroupId)? disableNotification,
    TResult? Function(
            String body,
            tz.TZDateTime dateTime,
            String notificationId,
            String notificationType,
            String? payload,
            bool isForeground)?
        scheduleSingleNotification,
  }) {
    return scheduleNotificationsWeekly?.call(
        daystoBeNotified, timeofDay, body, notificationGroupId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> daystoBeNotified, List<String> timeofDay,
            String body, String notificationGroupId)?
        scheduleNotificationsWeekly,
    TResult Function(
            List<String> timeofDay, String body, String notificationGroupId)?
        scheduleNotificationsDaily,
    TResult Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)?
        scheduleNotificationsMonthly,
    TResult Function(String notificationGroupId)? disableNotification,
    TResult Function(String body, tz.TZDateTime dateTime, String notificationId,
            String notificationType, String? payload, bool isForeground)?
        scheduleSingleNotification,
    required TResult orElse(),
  }) {
    if (scheduleNotificationsWeekly != null) {
      return scheduleNotificationsWeekly(
          daystoBeNotified, timeofDay, body, notificationGroupId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ScheduleNotifications value)
        scheduleNotificationsWeekly,
    required TResult Function(_ScheduleNotificationsDaily value)
        scheduleNotificationsDaily,
    required TResult Function(_ScheduleNotificationsMonthly value)
        scheduleNotificationsMonthly,
    required TResult Function(_CancelNotification value) disableNotification,
    required TResult Function(_ScheduleSingleNotification value)
        scheduleSingleNotification,
  }) {
    return scheduleNotificationsWeekly(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ScheduleNotifications value)?
        scheduleNotificationsWeekly,
    TResult? Function(_ScheduleNotificationsDaily value)?
        scheduleNotificationsDaily,
    TResult? Function(_ScheduleNotificationsMonthly value)?
        scheduleNotificationsMonthly,
    TResult? Function(_CancelNotification value)? disableNotification,
    TResult? Function(_ScheduleSingleNotification value)?
        scheduleSingleNotification,
  }) {
    return scheduleNotificationsWeekly?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ScheduleNotifications value)? scheduleNotificationsWeekly,
    TResult Function(_ScheduleNotificationsDaily value)?
        scheduleNotificationsDaily,
    TResult Function(_ScheduleNotificationsMonthly value)?
        scheduleNotificationsMonthly,
    TResult Function(_CancelNotification value)? disableNotification,
    TResult Function(_ScheduleSingleNotification value)?
        scheduleSingleNotification,
    required TResult orElse(),
  }) {
    if (scheduleNotificationsWeekly != null) {
      return scheduleNotificationsWeekly(this);
    }
    return orElse();
  }
}

abstract class _ScheduleNotifications
    implements ManageScheduledNotificationsEvent {
  const factory _ScheduleNotifications(
      {required final List<String> daystoBeNotified,
      required final List<String> timeofDay,
      required final String body,
      required final String notificationGroupId}) = _$ScheduleNotificationsImpl;

  List<String> get daystoBeNotified;
  List<String> get timeofDay;
  String get body;
  String get notificationGroupId;

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ScheduleNotificationsImplCopyWith<_$ScheduleNotificationsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ScheduleNotificationsDailyImplCopyWith<$Res> {
  factory _$$ScheduleNotificationsDailyImplCopyWith(
          _$ScheduleNotificationsDailyImpl value,
          $Res Function(_$ScheduleNotificationsDailyImpl) then) =
      __$$ScheduleNotificationsDailyImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<String> timeofDay, String body, String notificationGroupId});
}

/// @nodoc
class __$$ScheduleNotificationsDailyImplCopyWithImpl<$Res>
    extends _$ManageScheduledNotificationsEventCopyWithImpl<$Res,
        _$ScheduleNotificationsDailyImpl>
    implements _$$ScheduleNotificationsDailyImplCopyWith<$Res> {
  __$$ScheduleNotificationsDailyImplCopyWithImpl(
      _$ScheduleNotificationsDailyImpl _value,
      $Res Function(_$ScheduleNotificationsDailyImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? timeofDay = null,
    Object? body = null,
    Object? notificationGroupId = null,
  }) {
    return _then(_$ScheduleNotificationsDailyImpl(
      timeofDay: null == timeofDay
          ? _value._timeofDay
          : timeofDay // ignore: cast_nullable_to_non_nullable
              as List<String>,
      body: null == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      notificationGroupId: null == notificationGroupId
          ? _value.notificationGroupId
          : notificationGroupId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ScheduleNotificationsDailyImpl implements _ScheduleNotificationsDaily {
  const _$ScheduleNotificationsDailyImpl(
      {required final List<String> timeofDay,
      required this.body,
      required this.notificationGroupId})
      : _timeofDay = timeofDay;

  final List<String> _timeofDay;
  @override
  List<String> get timeofDay {
    if (_timeofDay is EqualUnmodifiableListView) return _timeofDay;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_timeofDay);
  }

  @override
  final String body;
  @override
  final String notificationGroupId;

  @override
  String toString() {
    return 'ManageScheduledNotificationsEvent.scheduleNotificationsDaily(timeofDay: $timeofDay, body: $body, notificationGroupId: $notificationGroupId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ScheduleNotificationsDailyImpl &&
            const DeepCollectionEquality()
                .equals(other._timeofDay, _timeofDay) &&
            (identical(other.body, body) || other.body == body) &&
            (identical(other.notificationGroupId, notificationGroupId) ||
                other.notificationGroupId == notificationGroupId));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_timeofDay),
      body,
      notificationGroupId);

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ScheduleNotificationsDailyImplCopyWith<_$ScheduleNotificationsDailyImpl>
      get copyWith => __$$ScheduleNotificationsDailyImplCopyWithImpl<
          _$ScheduleNotificationsDailyImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> daystoBeNotified,
            List<String> timeofDay, String body, String notificationGroupId)
        scheduleNotificationsWeekly,
    required TResult Function(
            List<String> timeofDay, String body, String notificationGroupId)
        scheduleNotificationsDaily,
    required TResult Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)
        scheduleNotificationsMonthly,
    required TResult Function(String notificationGroupId) disableNotification,
    required TResult Function(
            String body,
            tz.TZDateTime dateTime,
            String notificationId,
            String notificationType,
            String? payload,
            bool isForeground)
        scheduleSingleNotification,
  }) {
    return scheduleNotificationsDaily(timeofDay, body, notificationGroupId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> daystoBeNotified, List<String> timeofDay,
            String body, String notificationGroupId)?
        scheduleNotificationsWeekly,
    TResult? Function(
            List<String> timeofDay, String body, String notificationGroupId)?
        scheduleNotificationsDaily,
    TResult? Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)?
        scheduleNotificationsMonthly,
    TResult? Function(String notificationGroupId)? disableNotification,
    TResult? Function(
            String body,
            tz.TZDateTime dateTime,
            String notificationId,
            String notificationType,
            String? payload,
            bool isForeground)?
        scheduleSingleNotification,
  }) {
    return scheduleNotificationsDaily?.call(
        timeofDay, body, notificationGroupId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> daystoBeNotified, List<String> timeofDay,
            String body, String notificationGroupId)?
        scheduleNotificationsWeekly,
    TResult Function(
            List<String> timeofDay, String body, String notificationGroupId)?
        scheduleNotificationsDaily,
    TResult Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)?
        scheduleNotificationsMonthly,
    TResult Function(String notificationGroupId)? disableNotification,
    TResult Function(String body, tz.TZDateTime dateTime, String notificationId,
            String notificationType, String? payload, bool isForeground)?
        scheduleSingleNotification,
    required TResult orElse(),
  }) {
    if (scheduleNotificationsDaily != null) {
      return scheduleNotificationsDaily(timeofDay, body, notificationGroupId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ScheduleNotifications value)
        scheduleNotificationsWeekly,
    required TResult Function(_ScheduleNotificationsDaily value)
        scheduleNotificationsDaily,
    required TResult Function(_ScheduleNotificationsMonthly value)
        scheduleNotificationsMonthly,
    required TResult Function(_CancelNotification value) disableNotification,
    required TResult Function(_ScheduleSingleNotification value)
        scheduleSingleNotification,
  }) {
    return scheduleNotificationsDaily(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ScheduleNotifications value)?
        scheduleNotificationsWeekly,
    TResult? Function(_ScheduleNotificationsDaily value)?
        scheduleNotificationsDaily,
    TResult? Function(_ScheduleNotificationsMonthly value)?
        scheduleNotificationsMonthly,
    TResult? Function(_CancelNotification value)? disableNotification,
    TResult? Function(_ScheduleSingleNotification value)?
        scheduleSingleNotification,
  }) {
    return scheduleNotificationsDaily?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ScheduleNotifications value)? scheduleNotificationsWeekly,
    TResult Function(_ScheduleNotificationsDaily value)?
        scheduleNotificationsDaily,
    TResult Function(_ScheduleNotificationsMonthly value)?
        scheduleNotificationsMonthly,
    TResult Function(_CancelNotification value)? disableNotification,
    TResult Function(_ScheduleSingleNotification value)?
        scheduleSingleNotification,
    required TResult orElse(),
  }) {
    if (scheduleNotificationsDaily != null) {
      return scheduleNotificationsDaily(this);
    }
    return orElse();
  }
}

abstract class _ScheduleNotificationsDaily
    implements ManageScheduledNotificationsEvent {
  const factory _ScheduleNotificationsDaily(
          {required final List<String> timeofDay,
          required final String body,
          required final String notificationGroupId}) =
      _$ScheduleNotificationsDailyImpl;

  List<String> get timeofDay;
  String get body;
  String get notificationGroupId;

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ScheduleNotificationsDailyImplCopyWith<_$ScheduleNotificationsDailyImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ScheduleNotificationsMonthlyImplCopyWith<$Res> {
  factory _$$ScheduleNotificationsMonthlyImplCopyWith(
          _$ScheduleNotificationsMonthlyImpl value,
          $Res Function(_$ScheduleNotificationsMonthlyImpl) then) =
      __$$ScheduleNotificationsMonthlyImplCopyWithImpl<$Res>;
  @useResult
  $Res call({tz.TZDateTime dateTime, String body, String notificationGroupId});
}

/// @nodoc
class __$$ScheduleNotificationsMonthlyImplCopyWithImpl<$Res>
    extends _$ManageScheduledNotificationsEventCopyWithImpl<$Res,
        _$ScheduleNotificationsMonthlyImpl>
    implements _$$ScheduleNotificationsMonthlyImplCopyWith<$Res> {
  __$$ScheduleNotificationsMonthlyImplCopyWithImpl(
      _$ScheduleNotificationsMonthlyImpl _value,
      $Res Function(_$ScheduleNotificationsMonthlyImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? dateTime = null,
    Object? body = null,
    Object? notificationGroupId = null,
  }) {
    return _then(_$ScheduleNotificationsMonthlyImpl(
      dateTime: null == dateTime
          ? _value.dateTime
          : dateTime // ignore: cast_nullable_to_non_nullable
              as tz.TZDateTime,
      body: null == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      notificationGroupId: null == notificationGroupId
          ? _value.notificationGroupId
          : notificationGroupId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$ScheduleNotificationsMonthlyImpl
    implements _ScheduleNotificationsMonthly {
  const _$ScheduleNotificationsMonthlyImpl(
      {required this.dateTime,
      required this.body,
      required this.notificationGroupId});

  @override
  final tz.TZDateTime dateTime;
  @override
  final String body;
  @override
  final String notificationGroupId;

  @override
  String toString() {
    return 'ManageScheduledNotificationsEvent.scheduleNotificationsMonthly(dateTime: $dateTime, body: $body, notificationGroupId: $notificationGroupId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ScheduleNotificationsMonthlyImpl &&
            (identical(other.dateTime, dateTime) ||
                other.dateTime == dateTime) &&
            (identical(other.body, body) || other.body == body) &&
            (identical(other.notificationGroupId, notificationGroupId) ||
                other.notificationGroupId == notificationGroupId));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, dateTime, body, notificationGroupId);

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ScheduleNotificationsMonthlyImplCopyWith<
          _$ScheduleNotificationsMonthlyImpl>
      get copyWith => __$$ScheduleNotificationsMonthlyImplCopyWithImpl<
          _$ScheduleNotificationsMonthlyImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> daystoBeNotified,
            List<String> timeofDay, String body, String notificationGroupId)
        scheduleNotificationsWeekly,
    required TResult Function(
            List<String> timeofDay, String body, String notificationGroupId)
        scheduleNotificationsDaily,
    required TResult Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)
        scheduleNotificationsMonthly,
    required TResult Function(String notificationGroupId) disableNotification,
    required TResult Function(
            String body,
            tz.TZDateTime dateTime,
            String notificationId,
            String notificationType,
            String? payload,
            bool isForeground)
        scheduleSingleNotification,
  }) {
    return scheduleNotificationsMonthly(dateTime, body, notificationGroupId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> daystoBeNotified, List<String> timeofDay,
            String body, String notificationGroupId)?
        scheduleNotificationsWeekly,
    TResult? Function(
            List<String> timeofDay, String body, String notificationGroupId)?
        scheduleNotificationsDaily,
    TResult? Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)?
        scheduleNotificationsMonthly,
    TResult? Function(String notificationGroupId)? disableNotification,
    TResult? Function(
            String body,
            tz.TZDateTime dateTime,
            String notificationId,
            String notificationType,
            String? payload,
            bool isForeground)?
        scheduleSingleNotification,
  }) {
    return scheduleNotificationsMonthly?.call(
        dateTime, body, notificationGroupId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> daystoBeNotified, List<String> timeofDay,
            String body, String notificationGroupId)?
        scheduleNotificationsWeekly,
    TResult Function(
            List<String> timeofDay, String body, String notificationGroupId)?
        scheduleNotificationsDaily,
    TResult Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)?
        scheduleNotificationsMonthly,
    TResult Function(String notificationGroupId)? disableNotification,
    TResult Function(String body, tz.TZDateTime dateTime, String notificationId,
            String notificationType, String? payload, bool isForeground)?
        scheduleSingleNotification,
    required TResult orElse(),
  }) {
    if (scheduleNotificationsMonthly != null) {
      return scheduleNotificationsMonthly(dateTime, body, notificationGroupId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ScheduleNotifications value)
        scheduleNotificationsWeekly,
    required TResult Function(_ScheduleNotificationsDaily value)
        scheduleNotificationsDaily,
    required TResult Function(_ScheduleNotificationsMonthly value)
        scheduleNotificationsMonthly,
    required TResult Function(_CancelNotification value) disableNotification,
    required TResult Function(_ScheduleSingleNotification value)
        scheduleSingleNotification,
  }) {
    return scheduleNotificationsMonthly(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ScheduleNotifications value)?
        scheduleNotificationsWeekly,
    TResult? Function(_ScheduleNotificationsDaily value)?
        scheduleNotificationsDaily,
    TResult? Function(_ScheduleNotificationsMonthly value)?
        scheduleNotificationsMonthly,
    TResult? Function(_CancelNotification value)? disableNotification,
    TResult? Function(_ScheduleSingleNotification value)?
        scheduleSingleNotification,
  }) {
    return scheduleNotificationsMonthly?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ScheduleNotifications value)? scheduleNotificationsWeekly,
    TResult Function(_ScheduleNotificationsDaily value)?
        scheduleNotificationsDaily,
    TResult Function(_ScheduleNotificationsMonthly value)?
        scheduleNotificationsMonthly,
    TResult Function(_CancelNotification value)? disableNotification,
    TResult Function(_ScheduleSingleNotification value)?
        scheduleSingleNotification,
    required TResult orElse(),
  }) {
    if (scheduleNotificationsMonthly != null) {
      return scheduleNotificationsMonthly(this);
    }
    return orElse();
  }
}

abstract class _ScheduleNotificationsMonthly
    implements ManageScheduledNotificationsEvent {
  const factory _ScheduleNotificationsMonthly(
          {required final tz.TZDateTime dateTime,
          required final String body,
          required final String notificationGroupId}) =
      _$ScheduleNotificationsMonthlyImpl;

  tz.TZDateTime get dateTime;
  String get body;
  String get notificationGroupId;

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ScheduleNotificationsMonthlyImplCopyWith<
          _$ScheduleNotificationsMonthlyImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CancelNotificationImplCopyWith<$Res> {
  factory _$$CancelNotificationImplCopyWith(_$CancelNotificationImpl value,
          $Res Function(_$CancelNotificationImpl) then) =
      __$$CancelNotificationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String notificationGroupId});
}

/// @nodoc
class __$$CancelNotificationImplCopyWithImpl<$Res>
    extends _$ManageScheduledNotificationsEventCopyWithImpl<$Res,
        _$CancelNotificationImpl>
    implements _$$CancelNotificationImplCopyWith<$Res> {
  __$$CancelNotificationImplCopyWithImpl(_$CancelNotificationImpl _value,
      $Res Function(_$CancelNotificationImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? notificationGroupId = null,
  }) {
    return _then(_$CancelNotificationImpl(
      notificationGroupId: null == notificationGroupId
          ? _value.notificationGroupId
          : notificationGroupId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$CancelNotificationImpl implements _CancelNotification {
  const _$CancelNotificationImpl({required this.notificationGroupId});

  @override
  final String notificationGroupId;

  @override
  String toString() {
    return 'ManageScheduledNotificationsEvent.disableNotification(notificationGroupId: $notificationGroupId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CancelNotificationImpl &&
            (identical(other.notificationGroupId, notificationGroupId) ||
                other.notificationGroupId == notificationGroupId));
  }

  @override
  int get hashCode => Object.hash(runtimeType, notificationGroupId);

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CancelNotificationImplCopyWith<_$CancelNotificationImpl> get copyWith =>
      __$$CancelNotificationImplCopyWithImpl<_$CancelNotificationImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> daystoBeNotified,
            List<String> timeofDay, String body, String notificationGroupId)
        scheduleNotificationsWeekly,
    required TResult Function(
            List<String> timeofDay, String body, String notificationGroupId)
        scheduleNotificationsDaily,
    required TResult Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)
        scheduleNotificationsMonthly,
    required TResult Function(String notificationGroupId) disableNotification,
    required TResult Function(
            String body,
            tz.TZDateTime dateTime,
            String notificationId,
            String notificationType,
            String? payload,
            bool isForeground)
        scheduleSingleNotification,
  }) {
    return disableNotification(notificationGroupId);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> daystoBeNotified, List<String> timeofDay,
            String body, String notificationGroupId)?
        scheduleNotificationsWeekly,
    TResult? Function(
            List<String> timeofDay, String body, String notificationGroupId)?
        scheduleNotificationsDaily,
    TResult? Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)?
        scheduleNotificationsMonthly,
    TResult? Function(String notificationGroupId)? disableNotification,
    TResult? Function(
            String body,
            tz.TZDateTime dateTime,
            String notificationId,
            String notificationType,
            String? payload,
            bool isForeground)?
        scheduleSingleNotification,
  }) {
    return disableNotification?.call(notificationGroupId);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> daystoBeNotified, List<String> timeofDay,
            String body, String notificationGroupId)?
        scheduleNotificationsWeekly,
    TResult Function(
            List<String> timeofDay, String body, String notificationGroupId)?
        scheduleNotificationsDaily,
    TResult Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)?
        scheduleNotificationsMonthly,
    TResult Function(String notificationGroupId)? disableNotification,
    TResult Function(String body, tz.TZDateTime dateTime, String notificationId,
            String notificationType, String? payload, bool isForeground)?
        scheduleSingleNotification,
    required TResult orElse(),
  }) {
    if (disableNotification != null) {
      return disableNotification(notificationGroupId);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ScheduleNotifications value)
        scheduleNotificationsWeekly,
    required TResult Function(_ScheduleNotificationsDaily value)
        scheduleNotificationsDaily,
    required TResult Function(_ScheduleNotificationsMonthly value)
        scheduleNotificationsMonthly,
    required TResult Function(_CancelNotification value) disableNotification,
    required TResult Function(_ScheduleSingleNotification value)
        scheduleSingleNotification,
  }) {
    return disableNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ScheduleNotifications value)?
        scheduleNotificationsWeekly,
    TResult? Function(_ScheduleNotificationsDaily value)?
        scheduleNotificationsDaily,
    TResult? Function(_ScheduleNotificationsMonthly value)?
        scheduleNotificationsMonthly,
    TResult? Function(_CancelNotification value)? disableNotification,
    TResult? Function(_ScheduleSingleNotification value)?
        scheduleSingleNotification,
  }) {
    return disableNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ScheduleNotifications value)? scheduleNotificationsWeekly,
    TResult Function(_ScheduleNotificationsDaily value)?
        scheduleNotificationsDaily,
    TResult Function(_ScheduleNotificationsMonthly value)?
        scheduleNotificationsMonthly,
    TResult Function(_CancelNotification value)? disableNotification,
    TResult Function(_ScheduleSingleNotification value)?
        scheduleSingleNotification,
    required TResult orElse(),
  }) {
    if (disableNotification != null) {
      return disableNotification(this);
    }
    return orElse();
  }
}

abstract class _CancelNotification
    implements ManageScheduledNotificationsEvent {
  const factory _CancelNotification(
      {required final String notificationGroupId}) = _$CancelNotificationImpl;

  String get notificationGroupId;

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CancelNotificationImplCopyWith<_$CancelNotificationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ScheduleSingleNotificationImplCopyWith<$Res> {
  factory _$$ScheduleSingleNotificationImplCopyWith(
          _$ScheduleSingleNotificationImpl value,
          $Res Function(_$ScheduleSingleNotificationImpl) then) =
      __$$ScheduleSingleNotificationImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {String body,
      tz.TZDateTime dateTime,
      String notificationId,
      String notificationType,
      String? payload,
      bool isForeground});
}

/// @nodoc
class __$$ScheduleSingleNotificationImplCopyWithImpl<$Res>
    extends _$ManageScheduledNotificationsEventCopyWithImpl<$Res,
        _$ScheduleSingleNotificationImpl>
    implements _$$ScheduleSingleNotificationImplCopyWith<$Res> {
  __$$ScheduleSingleNotificationImplCopyWithImpl(
      _$ScheduleSingleNotificationImpl _value,
      $Res Function(_$ScheduleSingleNotificationImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? body = null,
    Object? dateTime = null,
    Object? notificationId = null,
    Object? notificationType = null,
    Object? payload = freezed,
    Object? isForeground = null,
  }) {
    return _then(_$ScheduleSingleNotificationImpl(
      body: null == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String,
      dateTime: null == dateTime
          ? _value.dateTime
          : dateTime // ignore: cast_nullable_to_non_nullable
              as tz.TZDateTime,
      notificationId: null == notificationId
          ? _value.notificationId
          : notificationId // ignore: cast_nullable_to_non_nullable
              as String,
      notificationType: null == notificationType
          ? _value.notificationType
          : notificationType // ignore: cast_nullable_to_non_nullable
              as String,
      payload: freezed == payload
          ? _value.payload
          : payload // ignore: cast_nullable_to_non_nullable
              as String?,
      isForeground: null == isForeground
          ? _value.isForeground
          : isForeground // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$ScheduleSingleNotificationImpl implements _ScheduleSingleNotification {
  const _$ScheduleSingleNotificationImpl(
      {required this.body,
      required this.dateTime,
      required this.notificationId,
      required this.notificationType,
      required this.payload,
      required this.isForeground});

  @override
  final String body;
  @override
  final tz.TZDateTime dateTime;
  @override
  final String notificationId;
  @override
  final String notificationType;
  @override
  final String? payload;
  @override
  final bool isForeground;

  @override
  String toString() {
    return 'ManageScheduledNotificationsEvent.scheduleSingleNotification(body: $body, dateTime: $dateTime, notificationId: $notificationId, notificationType: $notificationType, payload: $payload, isForeground: $isForeground)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ScheduleSingleNotificationImpl &&
            (identical(other.body, body) || other.body == body) &&
            (identical(other.dateTime, dateTime) ||
                other.dateTime == dateTime) &&
            (identical(other.notificationId, notificationId) ||
                other.notificationId == notificationId) &&
            (identical(other.notificationType, notificationType) ||
                other.notificationType == notificationType) &&
            (identical(other.payload, payload) || other.payload == payload) &&
            (identical(other.isForeground, isForeground) ||
                other.isForeground == isForeground));
  }

  @override
  int get hashCode => Object.hash(runtimeType, body, dateTime, notificationId,
      notificationType, payload, isForeground);

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ScheduleSingleNotificationImplCopyWith<_$ScheduleSingleNotificationImpl>
      get copyWith => __$$ScheduleSingleNotificationImplCopyWithImpl<
          _$ScheduleSingleNotificationImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(List<String> daystoBeNotified,
            List<String> timeofDay, String body, String notificationGroupId)
        scheduleNotificationsWeekly,
    required TResult Function(
            List<String> timeofDay, String body, String notificationGroupId)
        scheduleNotificationsDaily,
    required TResult Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)
        scheduleNotificationsMonthly,
    required TResult Function(String notificationGroupId) disableNotification,
    required TResult Function(
            String body,
            tz.TZDateTime dateTime,
            String notificationId,
            String notificationType,
            String? payload,
            bool isForeground)
        scheduleSingleNotification,
  }) {
    return scheduleSingleNotification(body, dateTime, notificationId,
        notificationType, payload, isForeground);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(List<String> daystoBeNotified, List<String> timeofDay,
            String body, String notificationGroupId)?
        scheduleNotificationsWeekly,
    TResult? Function(
            List<String> timeofDay, String body, String notificationGroupId)?
        scheduleNotificationsDaily,
    TResult? Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)?
        scheduleNotificationsMonthly,
    TResult? Function(String notificationGroupId)? disableNotification,
    TResult? Function(
            String body,
            tz.TZDateTime dateTime,
            String notificationId,
            String notificationType,
            String? payload,
            bool isForeground)?
        scheduleSingleNotification,
  }) {
    return scheduleSingleNotification?.call(body, dateTime, notificationId,
        notificationType, payload, isForeground);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(List<String> daystoBeNotified, List<String> timeofDay,
            String body, String notificationGroupId)?
        scheduleNotificationsWeekly,
    TResult Function(
            List<String> timeofDay, String body, String notificationGroupId)?
        scheduleNotificationsDaily,
    TResult Function(
            tz.TZDateTime dateTime, String body, String notificationGroupId)?
        scheduleNotificationsMonthly,
    TResult Function(String notificationGroupId)? disableNotification,
    TResult Function(String body, tz.TZDateTime dateTime, String notificationId,
            String notificationType, String? payload, bool isForeground)?
        scheduleSingleNotification,
    required TResult orElse(),
  }) {
    if (scheduleSingleNotification != null) {
      return scheduleSingleNotification(body, dateTime, notificationId,
          notificationType, payload, isForeground);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_ScheduleNotifications value)
        scheduleNotificationsWeekly,
    required TResult Function(_ScheduleNotificationsDaily value)
        scheduleNotificationsDaily,
    required TResult Function(_ScheduleNotificationsMonthly value)
        scheduleNotificationsMonthly,
    required TResult Function(_CancelNotification value) disableNotification,
    required TResult Function(_ScheduleSingleNotification value)
        scheduleSingleNotification,
  }) {
    return scheduleSingleNotification(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_ScheduleNotifications value)?
        scheduleNotificationsWeekly,
    TResult? Function(_ScheduleNotificationsDaily value)?
        scheduleNotificationsDaily,
    TResult? Function(_ScheduleNotificationsMonthly value)?
        scheduleNotificationsMonthly,
    TResult? Function(_CancelNotification value)? disableNotification,
    TResult? Function(_ScheduleSingleNotification value)?
        scheduleSingleNotification,
  }) {
    return scheduleSingleNotification?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_ScheduleNotifications value)? scheduleNotificationsWeekly,
    TResult Function(_ScheduleNotificationsDaily value)?
        scheduleNotificationsDaily,
    TResult Function(_ScheduleNotificationsMonthly value)?
        scheduleNotificationsMonthly,
    TResult Function(_CancelNotification value)? disableNotification,
    TResult Function(_ScheduleSingleNotification value)?
        scheduleSingleNotification,
    required TResult orElse(),
  }) {
    if (scheduleSingleNotification != null) {
      return scheduleSingleNotification(this);
    }
    return orElse();
  }
}

abstract class _ScheduleSingleNotification
    implements ManageScheduledNotificationsEvent {
  const factory _ScheduleSingleNotification(
      {required final String body,
      required final tz.TZDateTime dateTime,
      required final String notificationId,
      required final String notificationType,
      required final String? payload,
      required final bool isForeground}) = _$ScheduleSingleNotificationImpl;

  String get body;
  tz.TZDateTime get dateTime;
  String get notificationId;
  String get notificationType;
  String? get payload;
  bool get isForeground;

  /// Create a copy of ManageScheduledNotificationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ScheduleSingleNotificationImplCopyWith<_$ScheduleSingleNotificationImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$ManageScheduledNotificationsState {
  bool get isLoading => throw _privateConstructorUsedError;
  dynamic get failureOrSuccessOption => throw _privateConstructorUsedError;
  List<NotificationModel> get scheduledNotifications =>
      throw _privateConstructorUsedError;

  /// Create a copy of ManageScheduledNotificationsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ManageScheduledNotificationsStateCopyWith<ManageScheduledNotificationsState>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ManageScheduledNotificationsStateCopyWith<$Res> {
  factory $ManageScheduledNotificationsStateCopyWith(
          ManageScheduledNotificationsState value,
          $Res Function(ManageScheduledNotificationsState) then) =
      _$ManageScheduledNotificationsStateCopyWithImpl<$Res,
          ManageScheduledNotificationsState>;
  @useResult
  $Res call(
      {bool isLoading,
      dynamic failureOrSuccessOption,
      List<NotificationModel> scheduledNotifications});
}

/// @nodoc
class _$ManageScheduledNotificationsStateCopyWithImpl<$Res,
        $Val extends ManageScheduledNotificationsState>
    implements $ManageScheduledNotificationsStateCopyWith<$Res> {
  _$ManageScheduledNotificationsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ManageScheduledNotificationsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? failureOrSuccessOption = freezed,
    Object? scheduledNotifications = null,
  }) {
    return _then(_value.copyWith(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessOption: freezed == failureOrSuccessOption
          ? _value.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as dynamic,
      scheduledNotifications: null == scheduledNotifications
          ? _value.scheduledNotifications
          : scheduledNotifications // ignore: cast_nullable_to_non_nullable
              as List<NotificationModel>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ManageScheduledNotificationsStateImplCopyWith<$Res>
    implements $ManageScheduledNotificationsStateCopyWith<$Res> {
  factory _$$ManageScheduledNotificationsStateImplCopyWith(
          _$ManageScheduledNotificationsStateImpl value,
          $Res Function(_$ManageScheduledNotificationsStateImpl) then) =
      __$$ManageScheduledNotificationsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isLoading,
      dynamic failureOrSuccessOption,
      List<NotificationModel> scheduledNotifications});
}

/// @nodoc
class __$$ManageScheduledNotificationsStateImplCopyWithImpl<$Res>
    extends _$ManageScheduledNotificationsStateCopyWithImpl<$Res,
        _$ManageScheduledNotificationsStateImpl>
    implements _$$ManageScheduledNotificationsStateImplCopyWith<$Res> {
  __$$ManageScheduledNotificationsStateImplCopyWithImpl(
      _$ManageScheduledNotificationsStateImpl _value,
      $Res Function(_$ManageScheduledNotificationsStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of ManageScheduledNotificationsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isLoading = null,
    Object? failureOrSuccessOption = freezed,
    Object? scheduledNotifications = null,
  }) {
    return _then(_$ManageScheduledNotificationsStateImpl(
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessOption: freezed == failureOrSuccessOption
          ? _value.failureOrSuccessOption!
          : failureOrSuccessOption,
      scheduledNotifications: null == scheduledNotifications
          ? _value._scheduledNotifications
          : scheduledNotifications // ignore: cast_nullable_to_non_nullable
              as List<NotificationModel>,
    ));
  }
}

/// @nodoc

class _$ManageScheduledNotificationsStateImpl
    implements _ManageScheduledNotificationsState {
  const _$ManageScheduledNotificationsStateImpl(
      {required this.isLoading,
      required this.failureOrSuccessOption,
      required final List<NotificationModel> scheduledNotifications})
      : _scheduledNotifications = scheduledNotifications;

  @override
  final bool isLoading;
  @override
  final dynamic failureOrSuccessOption;
  final List<NotificationModel> _scheduledNotifications;
  @override
  List<NotificationModel> get scheduledNotifications {
    if (_scheduledNotifications is EqualUnmodifiableListView)
      return _scheduledNotifications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_scheduledNotifications);
  }

  @override
  String toString() {
    return 'ManageScheduledNotificationsState(isLoading: $isLoading, failureOrSuccessOption: $failureOrSuccessOption, scheduledNotifications: $scheduledNotifications)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ManageScheduledNotificationsStateImpl &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            const DeepCollectionEquality()
                .equals(other.failureOrSuccessOption, failureOrSuccessOption) &&
            const DeepCollectionEquality().equals(
                other._scheduledNotifications, _scheduledNotifications));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      isLoading,
      const DeepCollectionEquality().hash(failureOrSuccessOption),
      const DeepCollectionEquality().hash(_scheduledNotifications));

  /// Create a copy of ManageScheduledNotificationsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ManageScheduledNotificationsStateImplCopyWith<
          _$ManageScheduledNotificationsStateImpl>
      get copyWith => __$$ManageScheduledNotificationsStateImplCopyWithImpl<
          _$ManageScheduledNotificationsStateImpl>(this, _$identity);
}

abstract class _ManageScheduledNotificationsState
    implements ManageScheduledNotificationsState {
  const factory _ManageScheduledNotificationsState(
          {required final bool isLoading,
          required final dynamic failureOrSuccessOption,
          required final List<NotificationModel> scheduledNotifications}) =
      _$ManageScheduledNotificationsStateImpl;

  @override
  bool get isLoading;
  @override
  dynamic get failureOrSuccessOption;
  @override
  List<NotificationModel> get scheduledNotifications;

  /// Create a copy of ManageScheduledNotificationsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ManageScheduledNotificationsStateImplCopyWith<
          _$ManageScheduledNotificationsStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
