import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:flutter/material.dart';
import 'package:doso/doso.dart';
import '../../domain/core/unit.dart';
import '../../domain/facade/notifications_facade.dart';
import '../../domain/failure/notification_failure.dart';
import '../../domain/model/notification_model.dart';

part 'notification_event.dart';
part 'notification_state.dart';
part 'notification_bloc.freezed.dart';

@injectable
class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  final NotificationFacade _notificationFacade;

  NotificationBloc(this._notificationFacade)
      : super(const NotificationState.initial()) {
    on<NotificationEvent>((event, emit) async {
      await event.maybeMap(
        showNotification: (e) async {
          print('NotificationBloc: showNotification');
          emit(const NotificationState.loading());
          // final failureOrSuccess = await _notificationFacade.showNotification(e.notification);
          // failureOrSuccess.mapBoth(
          //    onLeft: (failure) => emit(NotificationState.failure(failure)),
          //    onRight:   (_) => emit(const NotificationState.success()),
          //  );
        },
        cancelNotification: (e) async {
          emit(const NotificationState.loading());
          final failureOrSuccess =
              await _notificationFacade.cancelNotification(e.id);
          failureOrSuccess.fold(
            onFailure: (failure) => emit(NotificationState.failure(failure)),
            onSuccess: (_) => emit(const NotificationState.success()),
          );
        },
        initialize: (e) async {
          print('NotificationBloc: initialize');
          emit(const NotificationState.loading());
          final failureOrSuccess =
              await _notificationFacade.initialize(e.context);
          failureOrSuccess.fold(
            onFailure: (failure) => emit(NotificationState.failure(failure)),
            onSuccess: (_) => emit(const NotificationState.success()),
          );
        },
        orElse: () {},
        checkForNotifications: (e) async {
          print('NotificationBloc: checkForNotifications');
          emit(const NotificationState.loading());
          final failureOrSuccess =
              await _notificationFacade.checkForNotifications(e.context);
          failureOrSuccess.fold(
            onFailure: (failure) => emit(NotificationState.failure(failure)),
            onSuccess: (notifications) => emit(NotificationState.success()),
          );
        },
      );
    });
  }
}
