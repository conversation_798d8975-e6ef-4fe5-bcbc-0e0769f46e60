import 'package:flutter/cupertino.dart';
import 'package:doso/doso.dart';
import '../core/unit.dart';

import '../failure/notification_failure.dart';
import '../model/notification_model.dart';

abstract class NotificationFacade {
  Future<Do<NotificationFailure, Unit>> scheduleNotification(
      NotificationModel notification, String frequency,
      {Widget? widget});
  // Future<Either<NotificationFailure, Unit>> scheduleSingleNotification(NotificationModel notification,String frequency,{Widget? widget});
  // Future<Either<NotificationFailure, Unit>> showNotification(NotificationModel notification);
  Future<Do<NotificationFailure, Unit>> cancelNotification(int id);
  Future<Do<NotificationFailure, Unit>> initialize(BuildContext context);

  Future<Do<NotificationFailure, List<NotificationModel>>> getNotifications();
  Future<Do<NotificationFailure, Unit>> deleteNotification(int id);
  Future<Do<NotificationFailure, Unit>> deleteAllNotifications();

  Future<Do<NotificationFailure, Unit>> checkForNotifications(
      BuildContext context);

  Future<Do<NotificationFailure, Unit>> scheduleWorkManagerNotification(
      NotificationModel notificationModel, String s);
  Stream<Do<NotificationFailure, NotificationModel>> streamNotifications();
}
