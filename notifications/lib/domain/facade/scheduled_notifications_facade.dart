import 'package:doso/doso.dart';
import '../core/unit.dart';
import '../failure/notification_failure.dart';
import 'package:timezone/timezone.dart' as tz;
import '../model/notification_model.dart';

abstract class ScheduledNotificationsFacade {
  Future<Do<NotificationFailure, Unit>> scheduleNotificationsWeekly({
    required List<String> daysToBeNotified,
    required List<String> timeOfDay,
    required String body,
    required String notificationGroupId,
  });
  //daily
  Future<Do<NotificationFailure, Unit>> scheduleNotificationsDaily({
    required List<String> timeOfDay,
    required String body,
    required String notificationGroupId,
  });
  //monthly
  Future<Do<NotificationFailure, Unit>> scheduleNotificationsMonthly({
    required tz.TZDateTime dateTime,
    required String body,
    required String notificationGroupId,
  });

  Future<Do<NotificationFailure, Unit>> disableNotificationGroup(
      String notificationGroupId);

  Future<Do<NotificationFailure, Unit>> scheduleSingleNotification({
    required String body,
    required tz.TZDateTime dateTime,
    required String notificationId,
    required String notificationType,
    required String title,
    required String? payload,
    required bool isForeground,
  });

  Future<Do<NotificationFailure, Unit>> cancelSingleNotification(
      String notificationId);
}
