/// A type that represents the absence of a value.
///
/// This is similar to `void` but can be used as a return type in generic contexts
/// where `void` cannot be used (e.g., in `Either<Failure, Unit>` or `Do<Failure, Unit>`).
///
/// This is commonly used in functional programming to represent operations
/// that don't return a meaningful value but may succeed or fail.
class Unit {
  const Unit._();

  /// The singleton instance of Unit.
  static const Unit _instance = Unit._();

  /// Factory constructor that returns the singleton instance.
  factory Unit() => _instance;

  @override
  String toString() => 'Unit()';

  @override
  bool operator ==(Object other) => other is Unit;

  @override
  int get hashCode => 0;
}

/// Convenience constant for the Unit instance.
const Unit unit = Unit._instance;
