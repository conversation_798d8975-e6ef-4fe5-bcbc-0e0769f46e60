import 'dart:async';

import 'package:flutter/material.dart';
import 'package:analytics/domain/facade/analytics_facade.dart';
import 'package:analytics/domain/models/therapy_session_model.dart';
import 'package:notifications/domain/facade/scheduled_notifications_facade.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:injectable/injectable.dart';

import '../pages/therapy/therapy_feedback_bottom_sheet.dart';

@LazySingleton()
class TherapyFeedbackService {
  static const String THERAPY_FEEDBACK_TASK = "com.junoplus.therapy.feedback";
  static const String THERAPY_NOTIFICATION_TYPE = "therapy_feedback";
  static const String FEEDBACK_NOTIFICATION_ID = "therapy_feedback";

  final ScheduledNotificationsFacade _notificationsFacade;
  final IAnalyticsFacade _analyticsFacade;
  final GlobalKey<NavigatorState> navigatorKey;
  Timer? _immediateCheckTimer;

  TherapyFeedbackService(
    this._notificationsFacade,
    this._analyticsFacade,
    this.navigatorKey,
  ) {
    print('🏗️ TherapyFeedbackService constructor called');
    try {
      _startImmediateCheckTimer();
      print('✅ TherapyFeedbackService initialized successfully');
    } catch (e) {
      print('❌ Error initializing TherapyFeedbackService: $e');
    }
  }

  /// Initialize the feedback service
  Future<void> initialize() async {
    // Listen for notification events from the notifications service
    // This would be connected to the notification stream in the main app
  }

  /// Start timer to check for immediate feedback triggers
  void _startImmediateCheckTimer() {
    print('🔄 Starting immediate feedback check timer');
    _immediateCheckTimer?.cancel();
    _immediateCheckTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      _checkImmediateTriggerPeriodically();
    });
  }

  /// Stop the immediate check timer
  void _stopImmediateCheckTimer() {
    _immediateCheckTimer?.cancel();
    _immediateCheckTimer = null;
  }

  /// Periodic check for immediate triggers
  void _checkImmediateTriggerPeriodically() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final triggerSessionId = prefs.getString('trigger_immediate_feedback');
      final triggerTimestamp =
          prefs.getInt('trigger_immediate_feedback_timestamp');

      if (triggerSessionId != null && triggerTimestamp != null) {
        final triggerTime =
            DateTime.fromMillisecondsSinceEpoch(triggerTimestamp);
        final now = DateTime.now();
        final timeDiff = now.difference(triggerTime);

        if (timeDiff.inSeconds <= 30) {
          print(
              '🚀 Periodic check found immediate trigger for session: $triggerSessionId');

          // Clear the trigger flag
          await prefs.remove('trigger_immediate_feedback');
          await prefs.remove('trigger_immediate_feedback_timestamp');

          // Show feedback immediately with proper context
          _tryShowBottomSheetWithContext(triggerSessionId);

          // Stop the timer since we found and processed the trigger
          _stopImmediateCheckTimer();
        } else if (timeDiff.inMinutes > 1) {
          // Trigger is too old, clear it
          await prefs.remove('trigger_immediate_feedback');
          await prefs.remove('trigger_immediate_feedback_timestamp');
        }
      }
    } catch (e) {
      print('❌ Error in periodic immediate trigger check: $e');
    }
  }

  /// Schedule feedback notification after therapy session
  Future<void> scheduleTherapyFeedback({
    required String sessionId,
    required Duration afterDuration,
  }) async {
    try {
      // Save session ID in SharedPreferences for retrieval when notification is clicked
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_therapy_session_id', sessionId);

      // Schedule notification after specified duration
      final scheduledTime = DateTime.now().add(afterDuration);
      final tz.TZDateTime tzScheduledTime =
          tz.TZDateTime.from(scheduledTime, tz.local);

      await _notificationsFacade.scheduleSingleNotification(
        body: "How was your therapy session? Please share your feedback.",
        dateTime: tzScheduledTime,
        notificationId: '${FEEDBACK_NOTIFICATION_ID}_$sessionId',
        notificationType: THERAPY_NOTIFICATION_TYPE,
        title: 'Therapy Session Feedback',
        payload: sessionId, // Pass session ID as payload
        isForeground: false,
      );

      // Mark feedback as requested in the session
      await _analyticsFacade.markFeedbackRequested(sessionId);

      print(
          '✅ Therapy feedback notification scheduled for session: $sessionId');
    } catch (e) {
      print('❌ Error scheduling therapy feedback: $e');
    }
  }

  /// Handle a therapy feedback notification being tapped
  Future<void> handleFeedbackNotificationTap(String? payload) async {
    print('🎯 handleFeedbackNotificationTap called with payload: $payload');

    if (payload == null || payload.isEmpty) {
      // Try to get session ID from SharedPreferences as fallback
      final prefs = await SharedPreferences.getInstance();
      payload = prefs.getString('last_therapy_session_id') ?? 'unknown';
      print('🔄 Using fallback session ID: $payload');
    }

    // Show feedback bottom sheet if app is in foreground
    if (navigatorKey.currentContext != null) {
      print('✅ App is in foreground, showing feedback bottom sheet');
      _showFeedbackBottomSheet(payload);
    } else {
      print('📱 App is in background, storing for later');
      // Store for later when app becomes active
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('pending_feedback_session', payload);
    }
  }

  /// Show feedback bottom sheet when app is in foreground
  void _showFeedbackBottomSheet(String sessionId) {
    print('🎯 _showFeedbackBottomSheet called for session: $sessionId');

    // Try to show the bottom sheet with proper context
    _tryShowBottomSheetWithContext(sessionId);
  }

  /// Store session for later feedback access
  void _storeSessionForLaterFeedback(String sessionId) async {
    print('💾 Storing session for later feedback: $sessionId');
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('pending_feedback_session', sessionId);
    await prefs.setInt(
        'pending_feedback_timestamp', DateTime.now().millisecondsSinceEpoch);
  }

  /// Try to show bottom sheet with proper context (when app is in foreground)
  void _tryShowBottomSheetWithContext(String sessionId) {
    print('🎯 _tryShowBottomSheetWithContext called for session: $sessionId');

    // Use post-frame callback to ensure the UI is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Add a longer delay to ensure MaterialApp is fully initialized
      Future.delayed(const Duration(milliseconds: 1500), () {
        final context = navigatorKey.currentContext;
        if (context != null && context.mounted) {
          print('✅ Context available, showing TherapyFeedbackBottomSheet');
          try {
            // Just try to show the bottom sheet directly
            TherapyFeedbackBottomSheet.show(context, sessionId);
          } catch (e) {
            print('❌ Error showing bottom sheet: $e');
            // Store for later if it still fails
            _storeSessionForLaterFeedback(sessionId);
          }
        } else {
          print('❌ Context still not available, storing for later');
          _storeSessionForLaterFeedback(sessionId);
        }
      });
    });
  }

  /// Check and handle any pending feedback requests when app becomes active
  /// Only shows feedback for the latest session that hasn't been completed
  Future<void> handlePendingFeedback() async {
    print('🔍 handlePendingFeedback called');
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check for immediate trigger flag first (highest priority - session just ended)
      await _checkImmediateTrigger();

      // Check for pending session from notification tap
      final pendingSessionId = prefs.getString('pending_feedback_session');
      if (pendingSessionId != null) {
        print('� Found pending session from notification: $pendingSessionId');
        await prefs.remove('pending_feedback_session');
        _showFeedbackBottomSheet(pendingSessionId);
        return;
      }

      // Check for immediate feedback flag (session ended in foreground)
      await _checkImmediateFeedback();

      // Check for the latest session that needs feedback
      await _checkLatestSessionForFeedback();
    } catch (e) {
      print('❌ Error handling pending feedback: $e');
    }
  }

  /// Check for immediate trigger flag (session just ended)
  Future<void> _checkImmediateTrigger() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final triggerSessionId = prefs.getString('trigger_immediate_feedback');
      final triggerTimestamp =
          prefs.getInt('trigger_immediate_feedback_timestamp');

      if (triggerSessionId != null && triggerTimestamp != null) {
        // Check if the trigger is very recent (within last 30 seconds)
        final triggerTime =
            DateTime.fromMillisecondsSinceEpoch(triggerTimestamp);
        final now = DateTime.now();
        final timeDiff = now.difference(triggerTime);

        if (timeDiff.inSeconds <= 30) {
          print('🚀 Found immediate trigger for session: $triggerSessionId');

          // Clear the trigger flag
          await prefs.remove('trigger_immediate_feedback');
          await prefs.remove('trigger_immediate_feedback_timestamp');

          // Show feedback immediately with proper context
          _tryShowBottomSheetWithContext(triggerSessionId);
          return;
        } else {
          // Trigger is too old, clear it
          await prefs.remove('trigger_immediate_feedback');
          await prefs.remove('trigger_immediate_feedback_timestamp');
        }
      }
    } catch (e) {
      print('❌ Error checking immediate trigger: $e');
    }
  }

  /// Check for immediate feedback flag set by session manager
  Future<void> _checkImmediateFeedback() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final immediateSessionId = prefs.getString('immediate_feedback_session');
      final immediateTimestamp = prefs.getInt('immediate_feedback_timestamp');

      if (immediateSessionId != null && immediateTimestamp != null) {
        // Check if the flag is recent (within last 5 minutes)
        final flagTime =
            DateTime.fromMillisecondsSinceEpoch(immediateTimestamp);
        final now = DateTime.now();
        final timeDiff = now.difference(flagTime);

        if (timeDiff.inMinutes <= 5) {
          print(
              '🎯 Found immediate feedback flag for session: $immediateSessionId');

          // Clear the flag
          await prefs.remove('immediate_feedback_session');
          await prefs.remove('immediate_feedback_timestamp');

          // Show feedback immediately
          _showFeedbackBottomSheet(immediateSessionId);
          return;
        } else {
          // Flag is too old, clear it
          await prefs.remove('immediate_feedback_session');
          await prefs.remove('immediate_feedback_timestamp');
        }
      }
    } catch (e) {
      print('❌ Error checking immediate feedback: $e');
    }
  }

  /// Check the latest therapy session to see if it needs feedback
  Future<void> _checkLatestSessionForFeedback() async {
    try {
      // Get the latest sessions from analytics
      final sessionsResult = await _analyticsFacade.getTherapySessionsLocal();

      sessionsResult.fold(
        onFailure: (error) => print('❌ Error getting sessions: $error'),
        onSuccess: (sessions) async {
          if (sessions.isEmpty) return;

          // Sort by start time to get the latest session
          final sortedSessions = List<TherapySessionModel>.from(sessions);
          sortedSessions.sort((a, b) {
            final aTime = a.sessionInfo.therapyStartTime;
            final bTime = b.sessionInfo.therapyStartTime;
            if (bTime == null && aTime == null) return 0;
            if (bTime == null) return -1;
            if (aTime == null) return 1;
            return bTime.compareTo(aTime);
          });

          final latestSession = sortedSessions.first;

          // Check if this session needs feedback
          if (_sessionNeedsFeedback(latestSession)) {
            print(
                '🎯 Latest session needs feedback: ${latestSession.sessionInfo.sessionId}');
            _showFeedbackBottomSheet(latestSession.sessionInfo.sessionId);
          }
        },
      );
    } catch (e) {
      print('❌ Error checking latest session for feedback: $e');
    }
  }

  /// Check if a session needs feedback
  bool _sessionNeedsFeedback(TherapySessionModel session) {
    // Session needs feedback if:
    // 1. Feedback was requested (notification was scheduled)
    // 2. Feedback is not yet completed
    // 3. Session is completed (not active)
    return session.feedback?.feedbackRequested == true &&
        session.feedback?.feedbackCompleted != true &&
        session.status == 'completed';
  }

  /// Handle foreground feedback request when session ends while app is active
  Future<void> handleForegroundSessionEnd(String sessionId) async {
    try {
      // Check if app is in foreground
      if (navigatorKey.currentContext != null) {
        print(
            '🎯 Session ended in foreground, showing immediate feedback request');

        // Show feedback request after a short delay to allow UI to settle
        Timer(const Duration(seconds: 2), () {
          _showFeedbackBottomSheet(sessionId);
        });
      }
    } catch (e) {
      print('❌ Error handling foreground session end: $e');
    }
  }

  /// Show in-app feedback request (for when app is in foreground during session end)
  Future<void> showInAppFeedbackRequest(String sessionId) async {
    final context = navigatorKey.currentContext;
    if (context != null) {
      // Small delay to ensure UI is ready
      await Future<void>.delayed(const Duration(seconds: 2));
      _showFeedbackBottomSheet(sessionId);
    }
  }

  /// Check if session already has feedback
  Future<bool> sessionHasFeedback(String sessionId) async {
    try {
      final sessionsResult = await _analyticsFacade.getTherapySessionsLocal();

      // Simple check based on result type (work around Either handling issues)
      if (sessionsResult.toString().startsWith('Right(')) {
        // In a real implementation, you'd properly extract the sessions
        // For now, assume false to allow feedback
        return false;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}
