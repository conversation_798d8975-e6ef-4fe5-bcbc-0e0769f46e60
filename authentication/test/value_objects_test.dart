import 'package:authentication/domain/model/value_objects.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EmailAddress', () {
    // This test ensures that the EmailAddress is created correctly with a valid email string.
    test('creates correct EmailAddress with valid email', () {
      final emailAddress = EmailAddress('<EMAIL>');
      expect(
          emailAddress.value
              .fold(onFailure: (_) => false, onSuccess: (_) => true),
          true);
    });

    // This test ensures that the EmailAddress is not created with an invalid email string.
    test('does not create EmailAddress with invalid email', () {
      final emailAddress = EmailAddress('invalid_email');
      expect(
          emailAddress.value
              .fold(onFailure: (_) => true, onSuccess: (_) => false),
          true);
    });
  });

  group('Password', () {
    // This test ensures that the Password is created correctly with a valid password string.
    test('creates correct Password with valid password', () {
      final password = Password('password123');
      expect(
          password.value.fold(onFailure: (_) => false, onSuccess: (_) => true),
          true);
    });

    // This test ensures that the Password is not created with an invalid password string.
    test('does not create Password with invalid password', () {
      final password = Password('pass');
      expect(
          password.value.fold(onFailure: (_) => true, onSuccess: (_) => false),
          true);
    });
  });
}
