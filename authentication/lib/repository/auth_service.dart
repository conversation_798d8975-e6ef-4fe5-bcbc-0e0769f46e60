// Import necessary packages and files
import 'package:authentication/domain/model/user_model.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:doso/doso.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:injectable/injectable.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../domain/facade/i_auth_facade.dart';
import '../domain/failure/auth_failure.dart';
import '../domain/model/value_objects.dart';
import 'firebase_user_mapper.dart';

// Injectable class annotation for lazy singleton
@LazySingleton(as: IAuthFacade)
class FirebaseAuthFacade implements IAuthFacade {
  final FirebaseAuth _firebaseAuth;
  final GoogleSignIn _googleSignIn;
  final FirebaseUserMapper _firebaseUserMapper;

  // Constructor to initialize dependencies
  FirebaseAuthFacade(
    this._firebaseAuth,
    this._googleSignIn,
    this._firebaseUserMapper,
  );

  @override
  Future<Do<void, UserModel>> getSignedInUser() async {
    final user = _firebaseAuth.currentUser;
    if (user != null) {
      final userModel = await _firebaseUserMapper.toDomain(user);
      return userModel != null ? Do.success(userModel) : Do.failure(null);
    } else {
      return Do.failure(null);
    }
  }

  @override
  Future<Do<AuthFailure, UserModel>> registerWithEmailAndPassword({
    required EmailAddress emailAddress,
    required Password password,
  }) async {
    final emailAddressStr = emailAddress.value
        .fold(onFailure: (failure) => 'INVALID EMAIL', onSuccess: (v) => v);
    final passwordStr = password.value
        .fold(onFailure: (failure) => 'INVALID PASSWORD', onSuccess: (v) => v);
    try {
      final userCredential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: emailAddressStr,
        password: passwordStr,
      );
      final userModel =
          await _firebaseUserMapper.toDomain(userCredential.user!);
      if (userModel != null) {
        return Do.success(userModel);
      } else {
        return Do.failure(const AuthFailure.serverError());
      }
    } on FirebaseAuthException catch (e) {
      if (e.code == 'email-already-in-use') {
        return Do.failure(const AuthFailure.emailAlreadyInUse());
      } else {
        return Do.failure(const AuthFailure.serverError());
      }
    }
  }

  @override
  Future<Do<AuthFailure, UserModel>> signInWithEmailAndPassword({
    required EmailAddress emailAddress,
    required Password password,
  }) async {
    final emailAddressStr = emailAddress.value
        .fold(onFailure: (failure) => 'INVALID EMAIL', onSuccess: (v) => v);
    final passwordStr = password.value
        .fold(onFailure: (failure) => 'INVALID PASSWORD', onSuccess: (v) => v);
    try {
      debugPrint('email: $emailAddressStr, password: $passwordStr');
      final userCredential = await _firebaseAuth.signInWithEmailAndPassword(
        email: emailAddressStr,
        password: passwordStr,
      );
      final userModel =
          await _firebaseUserMapper.toDomain(userCredential.user!);
      if (userModel != null) {
        return Do.success(userModel);
      } else {
        return Do.failure(const AuthFailure.serverError());
      }
    } on FirebaseAuthException catch (e) {
      if (e.code == 'user-not-found' || e.code == 'invalid-credential') {
        return Do.failure(
            const AuthFailure.invalidEmailAndPasswordCombination());
      } else {
        return Do.failure(const AuthFailure.serverError());
      }
    }
  }

  @override
  Future<Do<AuthFailure, UserModel>> signInWithGoogle() async {
    try {
      final googleUser = await _googleSignIn.authenticate();
      if (googleUser == null) {
        return Do.failure(const AuthFailure.cancelledByUser());
      }
      final googleAuthentication = await googleUser.authentication.idToken;
      final authCredential = GoogleAuthProvider.credential(
        idToken: googleAuthentication,
        accessToken: googleAuthentication,
      );

      // Check if email is already in use with email/password provider
      try {
        // Sign in with the Google credential directly
        final userCredential =
            await _firebaseAuth.signInWithCredential(authCredential);
        final userModel =
            await _firebaseUserMapper.toDomain(userCredential.user!);
        if (userModel != null) {
          return Do.success(userModel);
        } else {
          return Do.failure(const AuthFailure.serverError());
        }
      } on FirebaseAuthException catch (e) {
        // If the account already exists with a different credential/provider,
        // surface a suitable failure so the UI can prompt the user to sign in
        // with the existing method and link accounts if desired.
        if (e.code == 'account-exists-with-different-credential' ||
            e.code == 'email-already-in-use') {
          return Do.failure(const AuthFailure.emailAlreadyInUse());
        } else {
          return Do.failure(const AuthFailure.serverError());
        }
      }
    } on PlatformException catch (_) {
      return Do.failure(const AuthFailure.serverError());
    }
  }

  @override
  Future<Do<AuthFailure, UserModel>> signInWithApple() async {
    try {
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );
      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );
      final authResult =
          await _firebaseAuth.signInWithCredential(oauthCredential);
      final userModel = await _firebaseUserMapper.toDomain(authResult.user!);
      if (userModel != null) {
        return Do.success(userModel);
      } else {
        return Do.failure(const AuthFailure.serverError());
      }
    } on PlatformException catch (e) {
      if (e.code == 'ERROR_ABORTED_BY_USER') {
        debugPrint(e.message);
        return Do.failure(const AuthFailure.cancelledByUser());
      } else {
        return Do.failure(const AuthFailure.serverError());
      }
    }
  }

  @override
  Future<Do<AuthFailure, Object?>> sendPasswordResetEmail({
    required EmailAddress emailAddress,
  }) async {
    final emailAddressStr = emailAddress.value
        .fold(onFailure: (failure) => 'INVALID EMAIL', onSuccess: (v) => v);
    try {
      _firebaseAuth
          .sendPasswordResetEmail(email: emailAddressStr)
          .timeout(const Duration(seconds: 5));
      return Do.success(null);
    } on FirebaseAuthException catch (e) {
      if (e.code == 'user-not-found') {
        return Do.failure(const AuthFailure.userNotFound());
      } else {
        return Do.failure(const AuthFailure.serverError());
      }
    }
  }

  @override
  Future<void> signOut() async {
    await Future.wait([
      _googleSignIn.signOut(),
      _firebaseAuth.signOut(),
    ]);
  }

  @override
  Future<Do<AuthFailure, Object?>> isEmailVerified() async {
    try {
      await FirebaseAuth.instance.currentUser?.reload();
      final user = _firebaseAuth.currentUser;

      if (user != null) {
        debugPrint('User: ${user.emailVerified}');
        if (user.emailVerified) {
          return Do.success(null);
        } else {
          return Do.failure(const AuthFailure.emailVerificationFailed());
        }
      } else {
        return Do.failure(const AuthFailure.emailVerificationFailed());
      }
    } on FirebaseAuthException catch (e) {
      return Do.failure(const AuthFailure.emailVerificationFailed());
    }
  }

  @override
  Future<Do<AuthFailure, Object?>> sendEmailVerification() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user != null) {
        await user.sendEmailVerification();
        return Do.success(null);
      } else {
        return Do.failure(const AuthFailure.emailVerificationSendFailure());
      }
    } on FirebaseAuthException catch (e) {
      return Do.failure(const AuthFailure.emailVerificationSendFailure());
    }
  }
}
