import 'package:doso/doso.dart';
import '../failure/auth_failure.dart';
import '../model/user_model.dart';
import '../model/value_objects.dart';

// This interface defines the methods for authentication operations.
abstract class IAuthFacade {
  // Method to sign in with Google
  Future<Do<AuthFailure, UserModel>> signInWithGoogle();

  // Method to sign in with Apple
  Future<Do<AuthFailure, UserModel>> signInWithApple();

  // Method to get the currently signed-in user
  Future<Do<void, UserModel>> getSignedInUser();

  // Method to register a user with email and password
  Future<Do<AuthFailure, UserModel>> registerWithEmailAndPassword({
    required EmailAddress emailAddress,
    required Password password,
  });

  // Method to sign out the current user
  Future<void> signOut();

  // Method to sign in a user with email and password
  Future<Do<AuthFailure, UserModel>> signInWithEmailAndPassword({
    required EmailAddress emailAddress,
    required Password password,
  });

  // Method to send a password reset email to a user
  Future<Do<AuthFailure, Object?>> sendPasswordResetEmail({
    required EmailAddress emailAddress,
  });
  // email verification
  Future<Do<AuthFailure, Object?>> sendEmailVerification();
  // check if email is verified
  Future<Do<AuthFailure, Object?>> isEmailVerified();
}
