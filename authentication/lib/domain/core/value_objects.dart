import 'package:doso/doso.dart';
import 'package:meta/meta.dart';
import 'package:uuid/uuid.dart';

import 'errors.dart';
import 'value_validators.dart';
import 'failures.dart';

@immutable
abstract class ValueObject<T> {
  const ValueObject();
  Do<ValueFailure<T>, T> get value;

  T getOrCrash() {
    return value.fold(
        onFailure: (f) => throw UnexpectedValueError(f), onSuccess: (v) => v);
  }

  T getOrElse(T dflt) {
    return value.fold(onFailure: (_) => dflt, onSuccess: (v) => v);
  }

  // Either<ValueFailure<dynamic>, Unit> get failureOrUnit {
  //      return value.mapBoth(onLeft:Left(ValueFailure<dynamic>(value)), (_) => Right(unit));
  //
  //
  // }

  bool isValid() {
    return value.fold(onFailure: (_) => false, onSuccess: (_) => true);
  }

  @override
  bool operator ==(Object o) {
    if (identical(this, o)) return true;
    return o is ValueObject<T> && o.value == value;
  }

  @override
  int get hashCode => value.hashCode;

  @override
  String toString() => 'Value($value)';
}

class UniqueId extends ValueObject<String> {
  @override
  final Do<ValueFailure<String>, String> value;

  factory UniqueId() {
    return UniqueId._(Do.success(Uuid().v1()));
  }

  factory UniqueId.fromUniqueString(String uniqueIdStr) {
    return UniqueId._(Do.success(uniqueIdStr));
  }

  const UniqueId._(this.value);
}

class StringSingleLine extends ValueObject<String> {
  @override
  final Do<ValueFailure<String>, String> value;

  factory StringSingleLine(String input) {
    return StringSingleLine._(validateSingleLine(input));
  }

  const StringSingleLine._(this.value);
}
