import 'package:doso/doso.dart';
import 'package:authentication/domain/model/user_model.dart';
import 'package:flutter/material.dart';
import '../domain/failure/auth_failure.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../application/bloc/sign_in/sign_in_bloc.dart';

class AppleSignInButtonWrapper extends StatelessWidget {
  final Widget buttonChild;
  final void Function(UserModel) onSignInSuccess;
  final void Function(AuthFailure) onSignInFailure;

  const AppleSignInButtonWrapper({
    required this.buttonChild,
    required this.onSignInSuccess,
    required this.onSignInFailure,
  });

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SignInBloc, SignInState>(
      listener: (context, state) {
        state.authFailureOrSuccessOption.fold(
          onFailure: (_) {},
          onSuccess: (either) => either.fold(
            onFailure: onSignInFailure,
            onSuccess: onSignInSuccess,
          ),
        );
      },
      builder: (context, state) {
        return GestureDetector(
          onTap: () {
            context.read<SignInBloc>().add(SignInEvent.signInWithApple());
          },
          child: buttonChild,
        );
      },
    );
  }
}
