// Import necessary packages
import 'package:doso/doso.dart';
import 'package:authentication/domain/model/user_model.dart';
import 'package:flutter/material.dart';
import '../domain/failure/auth_failure.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../application/bloc/sign_in/sign_in_bloc.dart';
/*
This file defines the GoogleSignInButtonWrapper class, a StatelessWidget that wraps the Google sign-in button.

The class takes in a buttonChild widget and two callback functions, onSignInSuccess and onSignInFailure, which are triggered when the sign-in process succeeds or fails, respectively.

The build method of GoogleSignInButtonWrapper returns a BlocConsumer that listens to SignInBloc state changes. When the state changes, it either does nothing (if authFailureOrSuccessOption is None) or calls the appropriate callback function based on whether the sign-in was successful or not.

The GestureDetector widget triggers the SignInEvent.signInWithGoogle event when tapped, which initiates the sign-in process with Google.
*/

// This class is a StatelessWidget that wraps the Google sign-in button.
// It takes in a buttonChild widget and two callback functions, onSignInSuccess and onSignInFailure.
class GoogleSignInButtonWrapper extends StatelessWidget {
  // Declare the buttonChild widget and the callback functions
  final Widget buttonChild;
  final void Function(UserModel)
      onSignInSuccess; // Callback function for sign-in success
  final void Function(AuthFailure)
      onSignInFailure; // Callback function for sign-in failure

  // Constructor for the GoogleSignInButtonWrapper class
  const GoogleSignInButtonWrapper({
    required this.buttonChild,
    required this.onSignInSuccess,
    required this.onSignInFailure,
    Key? key,
  }) : super(key: key);

  // The build method returns a BlocConsumer that listens to SignInBloc state changes.
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SignInBloc, SignInState>(
      listener: (context, state) {
        // When the state changes, it either does nothing (if authFailureOrSuccessOption is None)
        // or calls the appropriate callback function based on whether the sign-in was successful or not.
        state.authFailureOrSuccessOption.fold(
          onFailure: (_) {},
          onSuccess: (either) => either.fold(
            onFailure: onSignInFailure,
            onSuccess: onSignInSuccess,
          ),
        );
      },
      builder: (context, state) {
        // The builder method returns a GestureDetector widget that triggers the SignInEvent.signInWithGoogle event when tapped.
        return GestureDetector(
          onTap: () {
            context.read<SignInBloc>().add(SignInEvent.signInWithGoogle());
          },
          child:
              buttonChild, // Display the buttonChild widget inside GestureDetector
        );
      },
    );
  }
}
