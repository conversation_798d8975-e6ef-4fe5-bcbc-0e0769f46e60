part of 'sign_in_bloc.dart';

@freezed
abstract class SignInState with _$SignInState {
  // Factory constructor for creating SignInState instances
  const factory SignInState({
    required EmailAddress emailAddress, // The email address entered by the user
    required Password password, // The password entered by the user
    required bool
        isSubmitting, // Indicates if the authentication process is in progress
    required bool
        obscureText, // Indicates if the password field should show as obscured or not
    required Do<void, Do<AuthFailure, Object?>>
        passwordResetOption, // Option for handling password reset failures or successes
    required Do<void, Do<AuthFailure, UserModel>>
        authFailureOrSuccessOption, // Option for handling authentication failures or successes
  }) = _SignInState;

  // Factory method for creating the initial state
  factory SignInState.initial() => SignInState(
        emailAddress: EmailAddress(''), // Initial email address is empty
        password: Password(''), // Initial password is empty
        isSubmitting: false, // Not submitting by default
        obscureText: true, // Default value for password fields is obscured
        authFailureOrSuccessOption:
            Do.failure(null), // No authentication failure or success initially
        passwordResetOption:
            Do.failure(null), // No password reset option initially
      );
}
