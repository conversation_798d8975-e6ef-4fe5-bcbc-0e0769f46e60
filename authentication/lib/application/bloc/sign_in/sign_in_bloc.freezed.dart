// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sign_in_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SignInEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SignInEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SignInEvent()';
  }
}

/// @nodoc
class $SignInEventCopyWith<$Res> {
  $SignInEventCopyWith(SignInEvent _, $Res Function(SignInEvent) __);
}

/// Adds pattern-matching-related methods to [SignInEvent].
extension SignInEventPatterns on SignInEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(EmailChanged value)? emailChanged,
    TResult Function(PasswordChanged value)? passwordChanged,
    TResult Function(ToggleObscureText value)? toggleObscureText,
    TResult Function(ForgotPassword value)? forgotPassword,
    TResult Function(SignInWithGoogle value)? signInWithGoogle,
    TResult Function(SignInWithApple value)? signInWithApple,
    TResult Function(SignInWithEmail value)? signInWithEmail,
    TResult Function(RegisterWithEmail value)? registerWithEmail,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case EmailChanged() when emailChanged != null:
        return emailChanged(_that);
      case PasswordChanged() when passwordChanged != null:
        return passwordChanged(_that);
      case ToggleObscureText() when toggleObscureText != null:
        return toggleObscureText(_that);
      case ForgotPassword() when forgotPassword != null:
        return forgotPassword(_that);
      case SignInWithGoogle() when signInWithGoogle != null:
        return signInWithGoogle(_that);
      case SignInWithApple() when signInWithApple != null:
        return signInWithApple(_that);
      case SignInWithEmail() when signInWithEmail != null:
        return signInWithEmail(_that);
      case RegisterWithEmail() when registerWithEmail != null:
        return registerWithEmail(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(EmailChanged value) emailChanged,
    required TResult Function(PasswordChanged value) passwordChanged,
    required TResult Function(ToggleObscureText value) toggleObscureText,
    required TResult Function(ForgotPassword value) forgotPassword,
    required TResult Function(SignInWithGoogle value) signInWithGoogle,
    required TResult Function(SignInWithApple value) signInWithApple,
    required TResult Function(SignInWithEmail value) signInWithEmail,
    required TResult Function(RegisterWithEmail value) registerWithEmail,
  }) {
    final _that = this;
    switch (_that) {
      case EmailChanged():
        return emailChanged(_that);
      case PasswordChanged():
        return passwordChanged(_that);
      case ToggleObscureText():
        return toggleObscureText(_that);
      case ForgotPassword():
        return forgotPassword(_that);
      case SignInWithGoogle():
        return signInWithGoogle(_that);
      case SignInWithApple():
        return signInWithApple(_that);
      case SignInWithEmail():
        return signInWithEmail(_that);
      case RegisterWithEmail():
        return registerWithEmail(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(EmailChanged value)? emailChanged,
    TResult? Function(PasswordChanged value)? passwordChanged,
    TResult? Function(ToggleObscureText value)? toggleObscureText,
    TResult? Function(ForgotPassword value)? forgotPassword,
    TResult? Function(SignInWithGoogle value)? signInWithGoogle,
    TResult? Function(SignInWithApple value)? signInWithApple,
    TResult? Function(SignInWithEmail value)? signInWithEmail,
    TResult? Function(RegisterWithEmail value)? registerWithEmail,
  }) {
    final _that = this;
    switch (_that) {
      case EmailChanged() when emailChanged != null:
        return emailChanged(_that);
      case PasswordChanged() when passwordChanged != null:
        return passwordChanged(_that);
      case ToggleObscureText() when toggleObscureText != null:
        return toggleObscureText(_that);
      case ForgotPassword() when forgotPassword != null:
        return forgotPassword(_that);
      case SignInWithGoogle() when signInWithGoogle != null:
        return signInWithGoogle(_that);
      case SignInWithApple() when signInWithApple != null:
        return signInWithApple(_that);
      case SignInWithEmail() when signInWithEmail != null:
        return signInWithEmail(_that);
      case RegisterWithEmail() when registerWithEmail != null:
        return registerWithEmail(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String emailStr)? emailChanged,
    TResult Function(String passwordStr)? passwordChanged,
    TResult Function()? toggleObscureText,
    TResult Function(String email)? forgotPassword,
    TResult Function()? signInWithGoogle,
    TResult Function()? signInWithApple,
    TResult Function(String email, String password)? signInWithEmail,
    TResult Function(String email, String password)? registerWithEmail,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case EmailChanged() when emailChanged != null:
        return emailChanged(_that.emailStr);
      case PasswordChanged() when passwordChanged != null:
        return passwordChanged(_that.passwordStr);
      case ToggleObscureText() when toggleObscureText != null:
        return toggleObscureText();
      case ForgotPassword() when forgotPassword != null:
        return forgotPassword(_that.email);
      case SignInWithGoogle() when signInWithGoogle != null:
        return signInWithGoogle();
      case SignInWithApple() when signInWithApple != null:
        return signInWithApple();
      case SignInWithEmail() when signInWithEmail != null:
        return signInWithEmail(_that.email, _that.password);
      case RegisterWithEmail() when registerWithEmail != null:
        return registerWithEmail(_that.email, _that.password);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String emailStr) emailChanged,
    required TResult Function(String passwordStr) passwordChanged,
    required TResult Function() toggleObscureText,
    required TResult Function(String email) forgotPassword,
    required TResult Function() signInWithGoogle,
    required TResult Function() signInWithApple,
    required TResult Function(String email, String password) signInWithEmail,
    required TResult Function(String email, String password) registerWithEmail,
  }) {
    final _that = this;
    switch (_that) {
      case EmailChanged():
        return emailChanged(_that.emailStr);
      case PasswordChanged():
        return passwordChanged(_that.passwordStr);
      case ToggleObscureText():
        return toggleObscureText();
      case ForgotPassword():
        return forgotPassword(_that.email);
      case SignInWithGoogle():
        return signInWithGoogle();
      case SignInWithApple():
        return signInWithApple();
      case SignInWithEmail():
        return signInWithEmail(_that.email, _that.password);
      case RegisterWithEmail():
        return registerWithEmail(_that.email, _that.password);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String emailStr)? emailChanged,
    TResult? Function(String passwordStr)? passwordChanged,
    TResult? Function()? toggleObscureText,
    TResult? Function(String email)? forgotPassword,
    TResult? Function()? signInWithGoogle,
    TResult? Function()? signInWithApple,
    TResult? Function(String email, String password)? signInWithEmail,
    TResult? Function(String email, String password)? registerWithEmail,
  }) {
    final _that = this;
    switch (_that) {
      case EmailChanged() when emailChanged != null:
        return emailChanged(_that.emailStr);
      case PasswordChanged() when passwordChanged != null:
        return passwordChanged(_that.passwordStr);
      case ToggleObscureText() when toggleObscureText != null:
        return toggleObscureText();
      case ForgotPassword() when forgotPassword != null:
        return forgotPassword(_that.email);
      case SignInWithGoogle() when signInWithGoogle != null:
        return signInWithGoogle();
      case SignInWithApple() when signInWithApple != null:
        return signInWithApple();
      case SignInWithEmail() when signInWithEmail != null:
        return signInWithEmail(_that.email, _that.password);
      case RegisterWithEmail() when registerWithEmail != null:
        return registerWithEmail(_that.email, _that.password);
      case _:
        return null;
    }
  }
}

/// @nodoc

class EmailChanged implements SignInEvent {
  const EmailChanged(this.emailStr);

  final String emailStr;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $EmailChangedCopyWith<EmailChanged> get copyWith =>
      _$EmailChangedCopyWithImpl<EmailChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is EmailChanged &&
            (identical(other.emailStr, emailStr) ||
                other.emailStr == emailStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, emailStr);

  @override
  String toString() {
    return 'SignInEvent.emailChanged(emailStr: $emailStr)';
  }
}

/// @nodoc
abstract mixin class $EmailChangedCopyWith<$Res>
    implements $SignInEventCopyWith<$Res> {
  factory $EmailChangedCopyWith(
          EmailChanged value, $Res Function(EmailChanged) _then) =
      _$EmailChangedCopyWithImpl;
  @useResult
  $Res call({String emailStr});
}

/// @nodoc
class _$EmailChangedCopyWithImpl<$Res> implements $EmailChangedCopyWith<$Res> {
  _$EmailChangedCopyWithImpl(this._self, this._then);

  final EmailChanged _self;
  final $Res Function(EmailChanged) _then;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? emailStr = null,
  }) {
    return _then(EmailChanged(
      null == emailStr
          ? _self.emailStr
          : emailStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class PasswordChanged implements SignInEvent {
  const PasswordChanged(this.passwordStr);

  final String passwordStr;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PasswordChangedCopyWith<PasswordChanged> get copyWith =>
      _$PasswordChangedCopyWithImpl<PasswordChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PasswordChanged &&
            (identical(other.passwordStr, passwordStr) ||
                other.passwordStr == passwordStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, passwordStr);

  @override
  String toString() {
    return 'SignInEvent.passwordChanged(passwordStr: $passwordStr)';
  }
}

/// @nodoc
abstract mixin class $PasswordChangedCopyWith<$Res>
    implements $SignInEventCopyWith<$Res> {
  factory $PasswordChangedCopyWith(
          PasswordChanged value, $Res Function(PasswordChanged) _then) =
      _$PasswordChangedCopyWithImpl;
  @useResult
  $Res call({String passwordStr});
}

/// @nodoc
class _$PasswordChangedCopyWithImpl<$Res>
    implements $PasswordChangedCopyWith<$Res> {
  _$PasswordChangedCopyWithImpl(this._self, this._then);

  final PasswordChanged _self;
  final $Res Function(PasswordChanged) _then;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? passwordStr = null,
  }) {
    return _then(PasswordChanged(
      null == passwordStr
          ? _self.passwordStr
          : passwordStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class ToggleObscureText implements SignInEvent {
  const ToggleObscureText();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ToggleObscureText);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SignInEvent.toggleObscureText()';
  }
}

/// @nodoc

class ForgotPassword implements SignInEvent {
  const ForgotPassword(this.email);

  final String email;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ForgotPasswordCopyWith<ForgotPassword> get copyWith =>
      _$ForgotPasswordCopyWithImpl<ForgotPassword>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ForgotPassword &&
            (identical(other.email, email) || other.email == email));
  }

  @override
  int get hashCode => Object.hash(runtimeType, email);

  @override
  String toString() {
    return 'SignInEvent.forgotPassword(email: $email)';
  }
}

/// @nodoc
abstract mixin class $ForgotPasswordCopyWith<$Res>
    implements $SignInEventCopyWith<$Res> {
  factory $ForgotPasswordCopyWith(
          ForgotPassword value, $Res Function(ForgotPassword) _then) =
      _$ForgotPasswordCopyWithImpl;
  @useResult
  $Res call({String email});
}

/// @nodoc
class _$ForgotPasswordCopyWithImpl<$Res>
    implements $ForgotPasswordCopyWith<$Res> {
  _$ForgotPasswordCopyWithImpl(this._self, this._then);

  final ForgotPassword _self;
  final $Res Function(ForgotPassword) _then;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? email = null,
  }) {
    return _then(ForgotPassword(
      null == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class SignInWithGoogle implements SignInEvent {
  const SignInWithGoogle();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SignInWithGoogle);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SignInEvent.signInWithGoogle()';
  }
}

/// @nodoc

class SignInWithApple implements SignInEvent {
  const SignInWithApple();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SignInWithApple);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SignInEvent.signInWithApple()';
  }
}

/// @nodoc

class SignInWithEmail implements SignInEvent {
  const SignInWithEmail(this.email, this.password);

  final String email;
  final String password;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SignInWithEmailCopyWith<SignInWithEmail> get copyWith =>
      _$SignInWithEmailCopyWithImpl<SignInWithEmail>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SignInWithEmail &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.password, password) ||
                other.password == password));
  }

  @override
  int get hashCode => Object.hash(runtimeType, email, password);

  @override
  String toString() {
    return 'SignInEvent.signInWithEmail(email: $email, password: $password)';
  }
}

/// @nodoc
abstract mixin class $SignInWithEmailCopyWith<$Res>
    implements $SignInEventCopyWith<$Res> {
  factory $SignInWithEmailCopyWith(
          SignInWithEmail value, $Res Function(SignInWithEmail) _then) =
      _$SignInWithEmailCopyWithImpl;
  @useResult
  $Res call({String email, String password});
}

/// @nodoc
class _$SignInWithEmailCopyWithImpl<$Res>
    implements $SignInWithEmailCopyWith<$Res> {
  _$SignInWithEmailCopyWithImpl(this._self, this._then);

  final SignInWithEmail _self;
  final $Res Function(SignInWithEmail) _then;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? email = null,
    Object? password = null,
  }) {
    return _then(SignInWithEmail(
      null == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      null == password
          ? _self.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class RegisterWithEmail implements SignInEvent {
  const RegisterWithEmail(this.email, this.password);

  final String email;
  final String password;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RegisterWithEmailCopyWith<RegisterWithEmail> get copyWith =>
      _$RegisterWithEmailCopyWithImpl<RegisterWithEmail>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RegisterWithEmail &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.password, password) ||
                other.password == password));
  }

  @override
  int get hashCode => Object.hash(runtimeType, email, password);

  @override
  String toString() {
    return 'SignInEvent.registerWithEmail(email: $email, password: $password)';
  }
}

/// @nodoc
abstract mixin class $RegisterWithEmailCopyWith<$Res>
    implements $SignInEventCopyWith<$Res> {
  factory $RegisterWithEmailCopyWith(
          RegisterWithEmail value, $Res Function(RegisterWithEmail) _then) =
      _$RegisterWithEmailCopyWithImpl;
  @useResult
  $Res call({String email, String password});
}

/// @nodoc
class _$RegisterWithEmailCopyWithImpl<$Res>
    implements $RegisterWithEmailCopyWith<$Res> {
  _$RegisterWithEmailCopyWithImpl(this._self, this._then);

  final RegisterWithEmail _self;
  final $Res Function(RegisterWithEmail) _then;

  /// Create a copy of SignInEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? email = null,
    Object? password = null,
  }) {
    return _then(RegisterWithEmail(
      null == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      null == password
          ? _self.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$SignInState {
  EmailAddress get emailAddress; // The email address entered by the user
  Password get password; // The password entered by the user
  bool
      get isSubmitting; // Indicates if the authentication process is in progress
  bool
      get obscureText; // Indicates if the password field should show as obscured or not
  Do<void, Do<AuthFailure, Object?>>
      get passwordResetOption; // Option for handling password reset failures or successes
  Do<void, Do<AuthFailure, UserModel>> get authFailureOrSuccessOption;

  /// Create a copy of SignInState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SignInStateCopyWith<SignInState> get copyWith =>
      _$SignInStateCopyWithImpl<SignInState>(this as SignInState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SignInState &&
            (identical(other.emailAddress, emailAddress) ||
                other.emailAddress == emailAddress) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.isSubmitting, isSubmitting) ||
                other.isSubmitting == isSubmitting) &&
            (identical(other.obscureText, obscureText) ||
                other.obscureText == obscureText) &&
            (identical(other.passwordResetOption, passwordResetOption) ||
                other.passwordResetOption == passwordResetOption) &&
            (identical(other.authFailureOrSuccessOption,
                    authFailureOrSuccessOption) ||
                other.authFailureOrSuccessOption ==
                    authFailureOrSuccessOption));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      emailAddress,
      password,
      isSubmitting,
      obscureText,
      passwordResetOption,
      authFailureOrSuccessOption);

  @override
  String toString() {
    return 'SignInState(emailAddress: $emailAddress, password: $password, isSubmitting: $isSubmitting, obscureText: $obscureText, passwordResetOption: $passwordResetOption, authFailureOrSuccessOption: $authFailureOrSuccessOption)';
  }
}

/// @nodoc
abstract mixin class $SignInStateCopyWith<$Res> {
  factory $SignInStateCopyWith(
          SignInState value, $Res Function(SignInState) _then) =
      _$SignInStateCopyWithImpl;
  @useResult
  $Res call(
      {EmailAddress emailAddress,
      Password password,
      bool isSubmitting,
      bool obscureText,
      Do<void, Do<AuthFailure, Object?>> passwordResetOption,
      Do<void, Do<AuthFailure, UserModel>> authFailureOrSuccessOption});
}

/// @nodoc
class _$SignInStateCopyWithImpl<$Res> implements $SignInStateCopyWith<$Res> {
  _$SignInStateCopyWithImpl(this._self, this._then);

  final SignInState _self;
  final $Res Function(SignInState) _then;

  /// Create a copy of SignInState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? emailAddress = null,
    Object? password = null,
    Object? isSubmitting = null,
    Object? obscureText = null,
    Object? passwordResetOption = null,
    Object? authFailureOrSuccessOption = null,
  }) {
    return _then(_self.copyWith(
      emailAddress: null == emailAddress
          ? _self.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as EmailAddress,
      password: null == password
          ? _self.password
          : password // ignore: cast_nullable_to_non_nullable
              as Password,
      isSubmitting: null == isSubmitting
          ? _self.isSubmitting
          : isSubmitting // ignore: cast_nullable_to_non_nullable
              as bool,
      obscureText: null == obscureText
          ? _self.obscureText
          : obscureText // ignore: cast_nullable_to_non_nullable
              as bool,
      passwordResetOption: null == passwordResetOption
          ? _self.passwordResetOption
          : passwordResetOption // ignore: cast_nullable_to_non_nullable
              as Do<void, Do<AuthFailure, Object?>>,
      authFailureOrSuccessOption: null == authFailureOrSuccessOption
          ? _self.authFailureOrSuccessOption
          : authFailureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Do<void, Do<AuthFailure, UserModel>>,
    ));
  }
}

/// Adds pattern-matching-related methods to [SignInState].
extension SignInStatePatterns on SignInState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SignInState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SignInState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SignInState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SignInState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SignInState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SignInState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            EmailAddress emailAddress,
            Password password,
            bool isSubmitting,
            bool obscureText,
            Do<void, Do<AuthFailure, Object?>> passwordResetOption,
            Do<void, Do<AuthFailure, UserModel>> authFailureOrSuccessOption)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SignInState() when $default != null:
        return $default(
            _that.emailAddress,
            _that.password,
            _that.isSubmitting,
            _that.obscureText,
            _that.passwordResetOption,
            _that.authFailureOrSuccessOption);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            EmailAddress emailAddress,
            Password password,
            bool isSubmitting,
            bool obscureText,
            Do<void, Do<AuthFailure, Object?>> passwordResetOption,
            Do<void, Do<AuthFailure, UserModel>> authFailureOrSuccessOption)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SignInState():
        return $default(
            _that.emailAddress,
            _that.password,
            _that.isSubmitting,
            _that.obscureText,
            _that.passwordResetOption,
            _that.authFailureOrSuccessOption);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            EmailAddress emailAddress,
            Password password,
            bool isSubmitting,
            bool obscureText,
            Do<void, Do<AuthFailure, Object?>> passwordResetOption,
            Do<void, Do<AuthFailure, UserModel>> authFailureOrSuccessOption)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SignInState() when $default != null:
        return $default(
            _that.emailAddress,
            _that.password,
            _that.isSubmitting,
            _that.obscureText,
            _that.passwordResetOption,
            _that.authFailureOrSuccessOption);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _SignInState implements SignInState {
  const _SignInState(
      {required this.emailAddress,
      required this.password,
      required this.isSubmitting,
      required this.obscureText,
      required this.passwordResetOption,
      required this.authFailureOrSuccessOption});

  @override
  final EmailAddress emailAddress;
// The email address entered by the user
  @override
  final Password password;
// The password entered by the user
  @override
  final bool isSubmitting;
// Indicates if the authentication process is in progress
  @override
  final bool obscureText;
// Indicates if the password field should show as obscured or not
  @override
  final Do<void, Do<AuthFailure, Object?>> passwordResetOption;
// Option for handling password reset failures or successes
  @override
  final Do<void, Do<AuthFailure, UserModel>> authFailureOrSuccessOption;

  /// Create a copy of SignInState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SignInStateCopyWith<_SignInState> get copyWith =>
      __$SignInStateCopyWithImpl<_SignInState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SignInState &&
            (identical(other.emailAddress, emailAddress) ||
                other.emailAddress == emailAddress) &&
            (identical(other.password, password) ||
                other.password == password) &&
            (identical(other.isSubmitting, isSubmitting) ||
                other.isSubmitting == isSubmitting) &&
            (identical(other.obscureText, obscureText) ||
                other.obscureText == obscureText) &&
            (identical(other.passwordResetOption, passwordResetOption) ||
                other.passwordResetOption == passwordResetOption) &&
            (identical(other.authFailureOrSuccessOption,
                    authFailureOrSuccessOption) ||
                other.authFailureOrSuccessOption ==
                    authFailureOrSuccessOption));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      emailAddress,
      password,
      isSubmitting,
      obscureText,
      passwordResetOption,
      authFailureOrSuccessOption);

  @override
  String toString() {
    return 'SignInState(emailAddress: $emailAddress, password: $password, isSubmitting: $isSubmitting, obscureText: $obscureText, passwordResetOption: $passwordResetOption, authFailureOrSuccessOption: $authFailureOrSuccessOption)';
  }
}

/// @nodoc
abstract mixin class _$SignInStateCopyWith<$Res>
    implements $SignInStateCopyWith<$Res> {
  factory _$SignInStateCopyWith(
          _SignInState value, $Res Function(_SignInState) _then) =
      __$SignInStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {EmailAddress emailAddress,
      Password password,
      bool isSubmitting,
      bool obscureText,
      Do<void, Do<AuthFailure, Object?>> passwordResetOption,
      Do<void, Do<AuthFailure, UserModel>> authFailureOrSuccessOption});
}

/// @nodoc
class __$SignInStateCopyWithImpl<$Res> implements _$SignInStateCopyWith<$Res> {
  __$SignInStateCopyWithImpl(this._self, this._then);

  final _SignInState _self;
  final $Res Function(_SignInState) _then;

  /// Create a copy of SignInState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? emailAddress = null,
    Object? password = null,
    Object? isSubmitting = null,
    Object? obscureText = null,
    Object? passwordResetOption = null,
    Object? authFailureOrSuccessOption = null,
  }) {
    return _then(_SignInState(
      emailAddress: null == emailAddress
          ? _self.emailAddress
          : emailAddress // ignore: cast_nullable_to_non_nullable
              as EmailAddress,
      password: null == password
          ? _self.password
          : password // ignore: cast_nullable_to_non_nullable
              as Password,
      isSubmitting: null == isSubmitting
          ? _self.isSubmitting
          : isSubmitting // ignore: cast_nullable_to_non_nullable
              as bool,
      obscureText: null == obscureText
          ? _self.obscureText
          : obscureText // ignore: cast_nullable_to_non_nullable
              as bool,
      passwordResetOption: null == passwordResetOption
          ? _self.passwordResetOption
          : passwordResetOption // ignore: cast_nullable_to_non_nullable
              as Do<void, Do<AuthFailure, Object?>>,
      authFailureOrSuccessOption: null == authFailureOrSuccessOption
          ? _self.authFailureOrSuccessOption
          : authFailureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Do<void, Do<AuthFailure, UserModel>>,
    ));
  }
}

// dart format on
