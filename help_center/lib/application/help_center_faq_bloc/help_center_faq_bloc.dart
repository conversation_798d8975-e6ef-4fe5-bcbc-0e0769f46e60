import 'dart:async';
import 'package:injectable/injectable.dart';
import 'package:bloc/bloc.dart';
import 'package:doso/doso.dart';
import 'package:meta/meta.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/facade/help_center_facade.dart';
import '../../domain/failure/help_center_failure.dart';
import '../../domain/model/help_center_model.dart';
import 'package:equatable/equatable.dart';
part 'help_center_faq_event.dart';
part 'help_center_faq_state.dart';
part 'help_center_faq_bloc.freezed.dart';

@injectable
class HelpCenterFaqBloc extends Bloc<HelpCenterFaqEvent, HelpCenterFaqState> {
  final HelpCenterFacade _helpCenterFacade;
  StreamSubscription<Do<HelpCenterFailure, List<HelpCenterFaqModel?>>>?
      _streamFaqSubscription;

  HelpCenterFaqBloc(this._helpCenterFacade)
      : super(const HelpCenterFaqState.initial()) {
    on<LoadHelpCenterFaq>(_onLoadHelpCenterFaq);
    on<HelpCenterFaqLoaded>(_onHelpCenterFaqLoaded);
  }

  Future<void> _onLoadHelpCenterFaq(
    LoadHelpCenterFaq event,
    Emitter<HelpCenterFaqState> emit,
  ) async {
    emit(const HelpCenterFaqState.loadingFaqs());
    await _streamFaqSubscription?.cancel();
    _streamFaqSubscription = _helpCenterFacade.getHelpCenterFaq().listen(
      (failureOrFaqs) {
        add(HelpCenterFaqEvent.helpCenterFaqLoaded(failureOrFaqs));
      },
    );
  }

  Future<void> _onHelpCenterFaqLoaded(
    HelpCenterFaqLoaded event,
    Emitter<HelpCenterFaqState> emit,
  ) async {
    event.failureOrFaqs.fold(
      onFailure: (failure) => emit(HelpCenterFaqState.failureFaqs(failure)),
      onSuccess: (faqs) => emit(HelpCenterFaqState.successFaqs(faqs)),
    );
  }

  @override
  Future<void> close() async {
    await _streamFaqSubscription?.cancel();
    return super.close();
  }
}
