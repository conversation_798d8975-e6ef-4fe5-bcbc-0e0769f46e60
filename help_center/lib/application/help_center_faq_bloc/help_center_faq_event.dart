part of 'help_center_faq_bloc.dart';

@freezed
class HelpCenterFaqEvent with _$HelpCenterFaqEvent {
  // Event representing a request to load the help center FAQs
  const factory HelpCenterFaqEvent.loadHelpCenterFaq() = LoadHelpCenterFaq;
  // Event representing a load of the help center FAQs
  const factory HelpCenterFaqEvent.helpCenterFaqLoaded(
          Do<HelpCenterFailure, List<HelpCenterFaqModel?>> failureOrFaqs) =
      HelpCenterFaqLoaded;
}
