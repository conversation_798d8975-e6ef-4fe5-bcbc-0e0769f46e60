import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:doso/doso.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:help_center/domain/facade/help_center_facade.dart';
import 'package:help_center/domain/failure/help_center_failure.dart';
import 'package:help_center/domain/model/help_center_model.dart';
import 'package:injectable/injectable.dart';

part 'help_center_contact_details_event.dart';
part 'help_center_contact_details_state.dart';
part 'help_center_contact_details_bloc.freezed.dart';

@injectable
class HelpCenterContactDetailsBloc
    extends Bloc<HelpCenterContactDetailsEvent, HelpCenterContactDetailsState> {
  final HelpCenterFacade _helpCenterFacade;

  HelpCenterContactDetailsBloc(this._helpCenterFacade)
      : super(const HelpCenterContactDetailsState.initial()) {
    on<GetContactDetails>(_onGetContactDetails);
  }

  Future<void> _onGetContactDetails(
    GetContactDetails event,
    Emitter<HelpCenterContactDetailsState> emit,
  ) async {
    emit(const HelpCenterContactDetailsState.loadingContactDetails());

    final contactDetails = await _helpCenterFacade.getContactDetails();
    contactDetails.fold(
      onFailure: (failure) =>
          emit(HelpCenterContactDetailsState.failureContactDetails(failure)),
      onSuccess: (contact) =>
          emit(HelpCenterContactDetailsState.successContactDetails(contact)),
    );
  }

  @override
  Future<void> close() async {
    return super.close();
  }
}
