import 'package:doso/doso.dart';
import 'package:help_center/domain/failure/help_center_failure.dart';

import '../model/help_center_model.dart';

abstract class HelpCenterFacade {
  // Get all help center videos
  Stream<Do<HelpCenterFailure, List<HelpCenterVideoModel?>>>
      getHelpCenterVideos();
  // Get all help center FAQs
  Stream<Do<HelpCenterFailure, List<HelpCenterFaqModel?>>> getHelpCenterFaq();
  // Get help center contact details
  Future<Do<HelpCenterFailure, HelpCenterContactModel?>> getContactDetails();
}
