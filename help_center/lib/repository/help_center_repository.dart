import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:doso/doso.dart';
import 'package:help_center/domain/facade/help_center_facade.dart';
import 'package:help_center/domain/failure/help_center_failure.dart';
import 'package:help_center/domain/model/help_center_model.dart';
import 'package:injectable/injectable.dart';

@LazySingleton(as: HelpCenterFacade)
class HelpCenterRepository implements HelpCenterFacade {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  @override
  Future<Do<HelpCenterFailure, HelpCenterContactModel?>>
      getContactDetails() async {
    try {
      final snapshot = await _firestore
          .collection('help_center_contact')
          .doc('contact')
          .get();
      final data = snapshot.data();
      if (data != null) {
        final contact = HelpCenterContactModel.fromJson(data);
        return Do.success(contact);
      } else {
        return Do.failure(const HelpCenterFailure.getContactDetailsFailure(
            'Failed to get contact details'));
      }
    } catch (e) {
      return Do.failure(HelpCenterFailure.getContactDetailsFailure(
          'Failed to get contact details'));
    }
  }

  @override
  Stream<Do<HelpCenterFailure, List<HelpCenterFaqModel?>>>
      getHelpCenterFaq() async* {
    try {
      yield* _firestore.collection('help_center_faq').snapshots().map(
          (snapshot) => Do.success(snapshot.docs
              .map((e) => HelpCenterFaqModel.fromJson(e.data()))
              .toList()));
    } catch (e) {
      yield Do.failure(
          HelpCenterFailure.getHelpCenterFaqFailure('Failed to get FAQs'));
    }
  }

  @override
  Stream<Do<HelpCenterFailure, List<HelpCenterVideoModel?>>>
      getHelpCenterVideos() async* {
    try {
      yield* _firestore
          .collection('help_center_videos')
          .snapshots()
          .map((event) {
        final videos = event.docs
            .map((e) => HelpCenterVideoModel.fromJson(e.data()))
            .toList();
        return Do.success(videos);
      });
    } catch (e) {
      yield Do.failure(
          HelpCenterFailure.getHelpCenterVideosFailure('Failed to get videos'));
    }
  }
}
