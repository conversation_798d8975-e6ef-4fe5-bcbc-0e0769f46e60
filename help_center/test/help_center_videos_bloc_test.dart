import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:doso/doso.dart';
import 'package:help_center/application/help_center_videos_bloc/help_center_videos_bloc.dart';
import 'package:help_center/domain/facade/help_center_facade.dart';
import 'package:help_center/domain/failure/help_center_failure.dart';
import 'package:help_center/domain/model/help_center_model.dart';
import 'package:mocktail/mocktail.dart';

class MockHelpCenterFacade extends Mock implements HelpCenterFacade {}

void main() {
  late HelpCenterVideosBloc bloc;
  late MockHelpCenterFacade mockHelpCenterFacade;

  setUp(() {
    mockHelpCenterFacade = MockHelpCenterFacade();
    bloc = HelpCenterVideosBloc(mockHelpCenterFacade);
  });

  test('initial state is HelpCenterVideosState.initial', () {
    expect(bloc.state, equals(const HelpCenterVideosState.initial()));
  });

  blocTest<HelpCenterVideosBloc, HelpCenterVideosState>(
    'emits [loadingVideos, successVideos] when LoadHelpCenterVideos is added and succeeds',
    build: () {
      when(() => mockHelpCenterFacade.getHelpCenterVideos()).thenAnswer(
          (_) => Stream.value(Do.success([HelpCenterVideoModel()])));
      return bloc;
    },
    act: (bloc) => bloc.add(const LoadHelpCenterVideos()),
    expect: () => [
      isA<LoadingVideos>(),
      isA<SuccessVideos>().having(
          (state) => state.videos, 'videos', [isA<HelpCenterVideoModel>()]),
    ],
  );

  blocTest<HelpCenterVideosBloc, HelpCenterVideosState>(
    'emits [loadingVideos, failureVideos] when LoadHelpCenterVideos is added and fails',
    build: () {
      when(() => mockHelpCenterFacade.getHelpCenterVideos()).thenAnswer((_) =>
          Stream.value(Do.failure(HelpCenterFailure.getHelpCenterVideosFailure(
              'Failed to get videos'))));
      return bloc;
    },
    act: (bloc) => bloc.add(const LoadHelpCenterVideos()),
    expect: () => [
      isA<LoadingVideos>(),
      isA<FailureVideos>().having(
          (state) => state.failure, 'failure', isA<HelpCenterFailure>()),
    ],
  );
}
