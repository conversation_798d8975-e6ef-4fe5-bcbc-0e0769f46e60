import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:doso/doso.dart';
import 'package:help_center/application/help_center_faq_bloc/help_center_faq_bloc.dart';
import 'package:help_center/domain/facade/help_center_facade.dart';
import 'package:help_center/domain/failure/help_center_failure.dart';
import 'package:help_center/domain/model/help_center_model.dart';
import 'package:mocktail/mocktail.dart';

class MockHelpCenterFacade extends Mock implements HelpCenterFacade {}

void main() {
  late HelpCenterFaqBloc bloc;
  late MockHelpCenterFacade mockHelpCenterFacade;

  setUp(() {
    mockHelpCenterFacade = MockHelpCenterFacade();
    bloc = HelpCenterFaqBloc(mockHelpCenterFacade);
  });

  test('initial state is HelpCenterFaqState.initial', () {
    expect(bloc.state, equals(const HelpCenterFaqState.initial()));
  });

  blocTest<HelpCenterFaqBloc, HelpCenterFaqState>(
    'emits [loadingFaqs, successFaqs] when LoadHelpCenterFaq is added and succeeds',
    build: () {
      when(() => mockHelpCenterFacade.getHelpCenterFaq())
          .thenAnswer((_) => Stream.value(Do.success([HelpCenterFaqModel()])));
      return bloc;
    },
    act: (bloc) => bloc.add(const LoadHelpCenterFaq()),
    expect: () => [
      isA<LoadingFaqs>(),
      isA<SuccessFaqs>()
          .having((state) => state.faqs, 'faqs', [isA<HelpCenterFaqModel>()]),
    ],
  );

  blocTest<HelpCenterFaqBloc, HelpCenterFaqState>(
    'emits [loadingFaqs, failureFaqs] when LoadHelpCenterFaq is added and fails',
    build: () {
      when(() => mockHelpCenterFacade.getHelpCenterFaq()).thenAnswer((_) =>
          Stream.value(Do.failure(HelpCenterFailure.getHelpCenterFaqFailure(
              'Failed to get faqs'))));
      return bloc;
    },
    act: (bloc) => bloc.add(const LoadHelpCenterFaq()),
    expect: () => [
      isA<LoadingFaqs>(),
      isA<FailureFaqs>().having(
          (state) => state.failure, 'failure', isA<HelpCenterFailure>()),
    ],
  );
}
