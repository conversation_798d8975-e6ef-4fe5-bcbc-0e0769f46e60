import 'package:test/test.dart';
import 'package:account_management/infrastructure/services/flow_categorization_service.dart';
import 'package:account_management/infrastructure/services/firestore_service.dart';
import 'package:account_management/domain/model/period_tracking_model.dart';

// Simple mock for testing
class MockFirestoreService extends FirestoreService {
  // Override methods as needed for testing
}

void main() {
  group('FlowCategorizationService', () {
    late FlowCategorizationService service;
    late MockFirestoreService mockFirestoreService;

    setUp(() {
      mockFirestoreService = MockFirestoreService();
      service = FlowCategorizationService(mockFirestoreService);
    });

    group('Conditional Rules Tests', () {
      test(
          'Rule 1.1: Day count ≤ 9, previous day NOT flow, day before current NOT flow -> period flow day',
          () async {
        // Setup: Current day is 5 days after previous period
        final currentDate = DateTime(2025, 1, 10);
        final previousPeriodDate = DateTime(2025, 1, 5);

        final existingData = <String, Map<String, PeriodTrackingModel>>{
          '2025_01': {
            '05': PeriodTrackingModel(
              date: previousPeriodDate,
              isPeriodDate: true,
              flowLevel: 2,
            ),
            '08': PeriodTrackingModel(
              date: DateTime(2025, 1, 8),
              isPeriodDate: false,
              flowLevel: 0, // No flow
            ),
            '09': PeriodTrackingModel(
              date: DateTime(2025, 1, 9),
              isPeriodDate: false,
              flowLevel: 0, // No flow
            ),
          }
        };

        final result = await service.categorizeFlowDay(
          currentDate: currentDate,
          flowLevel: 2,
          existingData: existingData,
        );

        result.fold(
          onFailure: (failure) =>
              fail('Expected success but got failure: $failure'),
          onSuccess: (categorization) {
            expect(
                categorization.currentDayCategory, DayCategory.periodFlowDay);
            expect(categorization.previousDayUpdate, null);
          },
        );
      });

      test(
          'Rule 1.2: Day count ≤ 9, previous day NOT flow, day before current IS flow and period -> period flow day',
          () async {
        final currentDate = DateTime(2025, 1, 10);
        final previousPeriodDate = DateTime(2025, 1, 5);

        final existingData = <String, Map<String, PeriodTrackingModel>>{
          '2025_01': {
            '05': PeriodTrackingModel(
              date: previousPeriodDate,
              isPeriodDate: true,
              flowLevel: 2,
            ),
            '08': PeriodTrackingModel(
              date: DateTime(2025, 1, 8),
              isPeriodDate: true, // Is period date
              flowLevel: 1, // Has flow
            ),
            '09': PeriodTrackingModel(
              date: DateTime(2025, 1, 9),
              isPeriodDate: false,
              flowLevel: 0, // No flow
            ),
          }
        };

        final result = await service.categorizeFlowDay(
          currentDate: currentDate,
          flowLevel: 2,
          existingData: existingData,
        );

        expect(result.isSuccess(), true);
        final categorization = result.getSuccess();
        expect(categorization.currentDayCategory, DayCategory.periodFlowDay);
      });

      test(
          'Rule 1.3: Day count ≤ 9, previous day NOT flow, day before current IS flow but NOT period -> flow day',
          () async {
        final currentDate = DateTime(2025, 1, 10);
        final previousPeriodDate = DateTime(2025, 1, 5);

        final existingData = <String, Map<String, PeriodTrackingModel>>{
          '2025_01': {
            '05': PeriodTrackingModel(
              date: previousPeriodDate,
              isPeriodDate: true,
              flowLevel: 2,
            ),
            '08': PeriodTrackingModel(
              date: DateTime(2025, 1, 8),
              isPeriodDate: false, // NOT period date
              flowLevel: 1, // Has flow
            ),
            '09': PeriodTrackingModel(
              date: DateTime(2025, 1, 9),
              isPeriodDate: false,
              flowLevel: 0, // No flow
            ),
          }
        };

        final result = await service.categorizeFlowDay(
          currentDate: currentDate,
          flowLevel: 2,
          existingData: existingData,
        );

        expect(result.isSuccess(), true);
        final categorization = result.getSuccess();
        expect(categorization.currentDayCategory, DayCategory.flowDay);
      });

      test(
          'Rule 1.4: Day count ≤ 9, previous day IS flow and period -> period flow day',
          () async {
        final currentDate = DateTime(2025, 1, 10);
        final previousPeriodDate = DateTime(2025, 1, 5);

        final existingData = <String, Map<String, PeriodTrackingModel>>{
          '2025_01': {
            '05': PeriodTrackingModel(
              date: previousPeriodDate,
              isPeriodDate: true,
              flowLevel: 2,
            ),
            '09': PeriodTrackingModel(
              date: DateTime(2025, 1, 9),
              isPeriodDate: true, // Is period date
              flowLevel: 1, // Has flow
            ),
          }
        };

        final result = await service.categorizeFlowDay(
          currentDate: currentDate,
          flowLevel: 2,
          existingData: existingData,
        );

        expect(result.isSuccess(), true);
        final categorization = result.getSuccess();
        expect(categorization.currentDayCategory, DayCategory.periodFlowDay);
      });

      test(
          'Rule 1.5: Day count ≤ 9, previous day IS flow but NOT period -> flow day',
          () async {
        final currentDate = DateTime(2025, 1, 10);
        final previousPeriodDate = DateTime(2025, 1, 5);

        final existingData = <String, Map<String, PeriodTrackingModel>>{
          '2025_01': {
            '05': PeriodTrackingModel(
              date: previousPeriodDate,
              isPeriodDate: true,
              flowLevel: 2,
            ),
            '09': PeriodTrackingModel(
              date: DateTime(2025, 1, 9),
              isPeriodDate: false, // NOT period date
              flowLevel: 1, // Has flow
            ),
          }
        };

        final result = await service.categorizeFlowDay(
          currentDate: currentDate,
          flowLevel: 2,
          existingData: existingData,
        );

        expect(result.isSuccess(), true);
        final categorization = result.getSuccess();
        expect(categorization.currentDayCategory, DayCategory.flowDay);
      });

      test('Rule 2.1: Day count > 9, day before current NOT flow -> flow day',
          () async {
        final currentDate = DateTime(2025, 1, 20);
        final previousPeriodDate = DateTime(2025, 1, 5); // 15 days ago

        final existingData = <String, Map<String, PeriodTrackingModel>>{
          '2025_01': {
            '05': PeriodTrackingModel(
              date: previousPeriodDate,
              isPeriodDate: true,
              flowLevel: 2,
            ),
            '18': PeriodTrackingModel(
              date: DateTime(2025, 1, 18),
              isPeriodDate: false,
              flowLevel: 0, // No flow
            ),
          }
        };

        final result = await service.categorizeFlowDay(
          currentDate: currentDate,
          flowLevel: 2,
          existingData: existingData,
        );

        expect(result.isSuccess(), true);
        final categorization = result.getSuccess();
        expect(categorization.currentDayCategory, DayCategory.flowDay);
      });

      test(
          'Rule 2.2: Day count > 9, day before current IS flow and period -> period flow day',
          () async {
        final currentDate = DateTime(2025, 1, 20);
        final previousPeriodDate = DateTime(2025, 1, 5); // 15 days ago

        final existingData = <String, Map<String, PeriodTrackingModel>>{
          '2025_01': {
            '05': PeriodTrackingModel(
              date: previousPeriodDate,
              isPeriodDate: true,
              flowLevel: 2,
            ),
            '18': PeriodTrackingModel(
              date: DateTime(2025, 1, 18),
              isPeriodDate: true, // Is period date
              flowLevel: 1, // Has flow
            ),
          }
        };

        final result = await service.categorizeFlowDay(
          currentDate: currentDate,
          flowLevel: 2,
          existingData: existingData,
        );

        expect(result.isSuccess(), true);
        final categorization = result.getSuccess();
        expect(categorization.currentDayCategory, DayCategory.periodFlowDay);
      });

      test(
          'Rule 2.3: Day count > 9, day before current IS flow but NOT period -> flow day',
          () async {
        final currentDate = DateTime(2025, 1, 20);
        final previousPeriodDate = DateTime(2025, 1, 5); // 15 days ago

        final existingData = <String, Map<String, PeriodTrackingModel>>{
          '2025_01': {
            '05': PeriodTrackingModel(
              date: previousPeriodDate,
              isPeriodDate: true,
              flowLevel: 2,
            ),
            '18': PeriodTrackingModel(
              date: DateTime(2025, 1, 18),
              isPeriodDate: false, // NOT period date
              flowLevel: 1, // Has flow
            ),
          }
        };

        final result = await service.categorizeFlowDay(
          currentDate: currentDate,
          flowLevel: 2,
          existingData: existingData,
        );

        expect(result.isSuccess(), true);
        final categorization = result.getSuccess();
        expect(categorization.currentDayCategory, DayCategory.flowDay);
      });
    });

    group('Previous Day Update Rules', () {
      test(
          'Rule 3.1: Current day is period flow day, previous day is flow, day before previous is flow and period -> update previous to period flow day',
          () async {
        final currentDate = DateTime(2025, 1, 10);
        final previousPeriodDate = DateTime(2025, 1, 5);

        final existingData = <String, Map<String, PeriodTrackingModel>>{
          '2025_01': {
            '05': PeriodTrackingModel(
              date: previousPeriodDate,
              isPeriodDate: true,
              flowLevel: 2,
            ),
            '08': PeriodTrackingModel(
              date: DateTime(2025, 1, 8),
              isPeriodDate: true, // Is period date
              flowLevel: 1, // Has flow
            ),
            '09': PeriodTrackingModel(
              date: DateTime(2025, 1, 9),
              isPeriodDate: false, // Will be updated
              flowLevel: 1, // Has flow
            ),
          }
        };

        final result = await service.categorizeFlowDay(
          currentDate: currentDate,
          flowLevel: 2,
          existingData: existingData,
        );

        expect(result.isSuccess(), true);
        final categorization = result.getSuccess();
        expect(categorization.currentDayCategory, DayCategory.periodFlowDay);
        expect(categorization.previousDayUpdate,
            PreviousDayUpdate.toPeriodFlowDay);
      });

      test(
          'Rule 3.2: Current day is period flow day, previous day is flow, day before previous is NOT flow -> update previous to period day',
          () async {
        final currentDate = DateTime(2025, 1, 10);
        final previousPeriodDate = DateTime(2025, 1, 5);

        final existingData = <String, Map<String, PeriodTrackingModel>>{
          '2025_01': {
            '05': PeriodTrackingModel(
              date: previousPeriodDate,
              isPeriodDate: true,
              flowLevel: 2,
            ),
            '08': PeriodTrackingModel(
              date: DateTime(2025, 1, 8),
              isPeriodDate: false, // NOT flow
              flowLevel: 0,
            ),
            '09': PeriodTrackingModel(
              date: DateTime(2025, 1, 9),
              isPeriodDate: false, // Will be updated
              flowLevel: 1, // Has flow
            ),
          }
        };

        final result = await service.categorizeFlowDay(
          currentDate: currentDate,
          flowLevel: 2,
          existingData: existingData,
        );

        expect(result.isSuccess(), true);
        final categorization = result.getSuccess();
        expect(categorization.currentDayCategory, DayCategory.periodFlowDay);
        expect(categorization.previousDayUpdate, PreviousDayUpdate.toPeriodDay);
      });
    });
  });
}
