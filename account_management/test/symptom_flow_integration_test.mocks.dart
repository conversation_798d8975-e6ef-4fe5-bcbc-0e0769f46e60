// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in account_management/test/symptom_flow_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:account_management/domain/core/unit.dart' as _i7;
import 'package:account_management/domain/facade/period_tracking_facade.dart'
    as _i8;
import 'package:account_management/domain/failure/period_tracking_failure.dart'
    as _i5;
import 'package:account_management/domain/model/period_tracking_model.dart'
    as _i6;
import 'package:account_management/infrastructure/services/firestore_service.dart'
    as _i3;
import 'package:account_management/infrastructure/services/period_data_service.dart'
    as _i9;
import 'package:doso/doso.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeDo_0<F, S> extends _i1.SmartFake implements _i2.Do<F, S> {
  _FakeDo_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [FirestoreService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirestoreService extends _i1.Mock implements _i3.FirestoreService {
  MockFirestoreService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<
    _i2.Do<
      _i5.PeriodTrackingFailure,
      Map<String, Map<String, _i6.PeriodTrackingModel>>
    >
  >
  watchYearData(int? year) =>
      (super.noSuchMethod(
            Invocation.method(#watchYearData, [year]),
            returnValue:
                _i4.Stream<
                  _i2.Do<
                    _i5.PeriodTrackingFailure,
                    Map<String, Map<String, _i6.PeriodTrackingModel>>
                  >
                >.empty(),
          )
          as _i4.Stream<
            _i2.Do<
              _i5.PeriodTrackingFailure,
              Map<String, Map<String, _i6.PeriodTrackingModel>>
            >
          >);

  @override
  _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>
  batchUpdatePeriodDates({
    required Map<String, Map<String, Set<String>>>? monthlyUpdates,
    required bool? isPeriodDate,
    Map<DateTime, int>? flowLevels,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#batchUpdatePeriodDates, [], {
              #monthlyUpdates: monthlyUpdates,
              #isPeriodDate: isPeriodDate,
              #flowLevels: flowLevels,
            }),
            returnValue:
                _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>.value(
                  _FakeDo_0<_i5.PeriodTrackingFailure, _i7.Unit>(
                    this,
                    Invocation.method(#batchUpdatePeriodDates, [], {
                      #monthlyUpdates: monthlyUpdates,
                      #isPeriodDate: isPeriodDate,
                      #flowLevels: flowLevels,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>);

  @override
  _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>
  batchUpdateOvulationDates({
    required Set<DateTime>? ovulationDates,
    required bool? isOvulationDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#batchUpdateOvulationDates, [], {
              #ovulationDates: ovulationDates,
              #isOvulationDate: isOvulationDate,
            }),
            returnValue:
                _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>.value(
                  _FakeDo_0<_i5.PeriodTrackingFailure, _i7.Unit>(
                    this,
                    Invocation.method(#batchUpdateOvulationDates, [], {
                      #ovulationDates: ovulationDates,
                      #isOvulationDate: isOvulationDate,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>);

  @override
  _i4.Future<Set<DateTime>> getExistingOvulationDatesInRange(
    DateTime? startDate,
    DateTime? endDate,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getExistingOvulationDatesInRange, [
              startDate,
              endDate,
            ]),
            returnValue: _i4.Future<Set<DateTime>>.value(<DateTime>{}),
          )
          as _i4.Future<Set<DateTime>>);

  @override
  _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>> saveSymptomData({
    required DateTime? date,
    Map<String, dynamic>? symptomData,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#saveSymptomData, [], {
              #date: date,
              #symptomData: symptomData,
            }),
            returnValue:
                _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>.value(
                  _FakeDo_0<_i5.PeriodTrackingFailure, _i7.Unit>(
                    this,
                    Invocation.method(#saveSymptomData, [], {
                      #date: date,
                      #symptomData: symptomData,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>);

  @override
  _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>
  updateFlowCategorization({
    required DateTime? date,
    required int? flowLevel,
    required bool? isPeriodDate,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateFlowCategorization, [], {
              #date: date,
              #flowLevel: flowLevel,
              #isPeriodDate: isPeriodDate,
            }),
            returnValue:
                _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>.value(
                  _FakeDo_0<_i5.PeriodTrackingFailure, _i7.Unit>(
                    this,
                    Invocation.method(#updateFlowCategorization, [], {
                      #date: date,
                      #flowLevel: flowLevel,
                      #isPeriodDate: isPeriodDate,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>);

  @override
  _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i6.PeriodTrackingModel?>>
  getSymptomData({required DateTime? date}) =>
      (super.noSuchMethod(
            Invocation.method(#getSymptomData, [], {#date: date}),
            returnValue: _i4.Future<
              _i2.Do<_i5.PeriodTrackingFailure, _i6.PeriodTrackingModel?>
            >.value(
              _FakeDo_0<_i5.PeriodTrackingFailure, _i6.PeriodTrackingModel?>(
                this,
                Invocation.method(#getSymptomData, [], {#date: date}),
              ),
            ),
          )
          as _i4.Future<
            _i2.Do<_i5.PeriodTrackingFailure, _i6.PeriodTrackingModel?>
          >);
}

/// A class which mocks [PeriodTrackingFacade].
///
/// See the documentation for Mockito's code generation for more information.
class MockPeriodTrackingFacade extends _i1.Mock
    implements _i8.PeriodTrackingFacade {
  MockPeriodTrackingFacade() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<
    _i2.Do<
      _i5.PeriodTrackingFailure,
      Map<String, Map<String, _i6.PeriodTrackingModel>>
    >
  >
  watchYearData(int? year) =>
      (super.noSuchMethod(
            Invocation.method(#watchYearData, [year]),
            returnValue:
                _i4.Stream<
                  _i2.Do<
                    _i5.PeriodTrackingFailure,
                    Map<String, Map<String, _i6.PeriodTrackingModel>>
                  >
                >.empty(),
          )
          as _i4.Stream<
            _i2.Do<
              _i5.PeriodTrackingFailure,
              Map<String, Map<String, _i6.PeriodTrackingModel>>
            >
          >);

  @override
  _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>> selectPeriodDates(
    Set<DateTime>? selectedDates, {
    Map<DateTime, int>? flowLevels,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #selectPeriodDates,
              [selectedDates],
              {#flowLevels: flowLevels},
            ),
            returnValue:
                _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>.value(
                  _FakeDo_0<_i5.PeriodTrackingFailure, _i7.Unit>(
                    this,
                    Invocation.method(
                      #selectPeriodDates,
                      [selectedDates],
                      {#flowLevels: flowLevels},
                    ),
                  ),
                ),
          )
          as _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>);

  @override
  _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>> deselectPeriodDates(
    Set<DateTime>? datesToDeselect,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#deselectPeriodDates, [datesToDeselect]),
            returnValue:
                _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>.value(
                  _FakeDo_0<_i5.PeriodTrackingFailure, _i7.Unit>(
                    this,
                    Invocation.method(#deselectPeriodDates, [datesToDeselect]),
                  ),
                ),
          )
          as _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>);

  @override
  _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>
  updatePredictionMetadata(
    Map<String, Map<String, _i6.PeriodTrackingModel>>? yearData,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updatePredictionMetadata, [yearData]),
            returnValue:
                _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>.value(
                  _FakeDo_0<_i5.PeriodTrackingFailure, _i7.Unit>(
                    this,
                    Invocation.method(#updatePredictionMetadata, [yearData]),
                  ),
                ),
          )
          as _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>);

  @override
  _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>
  handleFlowSymptomUpdate(
    DateTime? date,
    int? flowLevel,
    Map<String, Map<String, _i6.PeriodTrackingModel>>? yearData,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#handleFlowSymptomUpdate, [
              date,
              flowLevel,
              yearData,
            ]),
            returnValue:
                _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>.value(
                  _FakeDo_0<_i5.PeriodTrackingFailure, _i7.Unit>(
                    this,
                    Invocation.method(#handleFlowSymptomUpdate, [
                      date,
                      flowLevel,
                      yearData,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>);

  @override
  _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, Map<String, Set<DateTime>>>>
  generatePredictions({int? monthsAhead = 6}) =>
      (super.noSuchMethod(
            Invocation.method(#generatePredictions, [], {
              #monthsAhead: monthsAhead,
            }),
            returnValue: _i4.Future<
              _i2.Do<_i5.PeriodTrackingFailure, Map<String, Set<DateTime>>>
            >.value(
              _FakeDo_0<_i5.PeriodTrackingFailure, Map<String, Set<DateTime>>>(
                this,
                Invocation.method(#generatePredictions, [], {
                  #monthsAhead: monthsAhead,
                }),
              ),
            ),
          )
          as _i4.Future<
            _i2.Do<_i5.PeriodTrackingFailure, Map<String, Set<DateTime>>>
          >);

  @override
  _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>
  initializePredictionsFromOnboarding(
    DateTime? lastPeriodDate,
    int? cycleLength,
    int? periodLength,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#initializePredictionsFromOnboarding, [
              lastPeriodDate,
              cycleLength,
              periodLength,
            ]),
            returnValue:
                _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>.value(
                  _FakeDo_0<_i5.PeriodTrackingFailure, _i7.Unit>(
                    this,
                    Invocation.method(#initializePredictionsFromOnboarding, [
                      lastPeriodDate,
                      cycleLength,
                      periodLength,
                    ]),
                  ),
                ),
          )
          as _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>);
}

/// A class which mocks [PeriodDataService].
///
/// See the documentation for Mockito's code generation for more information.
class MockPeriodDataService extends _i1.Mock implements _i9.PeriodDataService {
  MockPeriodDataService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>> selectPeriodDates(
    Set<DateTime>? selectedDates, {
    Map<DateTime, int>? flowLevels,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #selectPeriodDates,
              [selectedDates],
              {#flowLevels: flowLevels},
            ),
            returnValue:
                _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>.value(
                  _FakeDo_0<_i5.PeriodTrackingFailure, _i7.Unit>(
                    this,
                    Invocation.method(
                      #selectPeriodDates,
                      [selectedDates],
                      {#flowLevels: flowLevels},
                    ),
                  ),
                ),
          )
          as _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>);

  @override
  _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>> deselectPeriodDates(
    Set<DateTime>? datesToDeselect,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#deselectPeriodDates, [datesToDeselect]),
            returnValue:
                _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>.value(
                  _FakeDo_0<_i5.PeriodTrackingFailure, _i7.Unit>(
                    this,
                    Invocation.method(#deselectPeriodDates, [datesToDeselect]),
                  ),
                ),
          )
          as _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>);

  @override
  _i4.Future<Set<DateTime>> getAllExistingPeriodDates() =>
      (super.noSuchMethod(
            Invocation.method(#getAllExistingPeriodDates, []),
            returnValue: _i4.Future<Set<DateTime>>.value(<DateTime>{}),
          )
          as _i4.Future<Set<DateTime>>);

  @override
  List<List<DateTime>> calculatePeriodCycles(Set<DateTime>? periodDates) =>
      (super.noSuchMethod(
            Invocation.method(#calculatePeriodCycles, [periodDates]),
            returnValue: <List<DateTime>>[],
          )
          as List<List<DateTime>>);

  @override
  bool isValidCycleForOvulation(
    List<DateTime>? currentCycle,
    List<List<DateTime>>? allCycles,
    int? cycleIndex,
    int? userCycleLength,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#isValidCycleForOvulation, [
              currentCycle,
              allCycles,
              cycleIndex,
              userCycleLength,
            ]),
            returnValue: false,
          )
          as bool);

  @override
  _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>> processFlowEntry({
    required DateTime? date,
    required int? flowLevel,
    required Map<String, Map<String, _i6.PeriodTrackingModel>>? existingData,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#processFlowEntry, [], {
              #date: date,
              #flowLevel: flowLevel,
              #existingData: existingData,
            }),
            returnValue:
                _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>.value(
                  _FakeDo_0<_i5.PeriodTrackingFailure, _i7.Unit>(
                    this,
                    Invocation.method(#processFlowEntry, [], {
                      #date: date,
                      #flowLevel: flowLevel,
                      #existingData: existingData,
                    }),
                  ),
                ),
          )
          as _i4.Future<_i2.Do<_i5.PeriodTrackingFailure, _i7.Unit>>);

  @override
  _i4.Future<bool> shouldMarkAsPeriodDate(
    DateTime? targetDate,
    Map<DateTime, int>? newFlowData,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#shouldMarkAsPeriodDate, [
              targetDate,
              newFlowData,
            ]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  Set<DateTime> extractPeriodDatesFromYearData(
    Map<String, Map<String, _i6.PeriodTrackingModel>>? yearData,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#extractPeriodDatesFromYearData, [yearData]),
            returnValue: <DateTime>{},
          )
          as Set<DateTime>);

  @override
  Set<DateTime> extractOvulationDatesFromYearData(
    Map<String, Map<String, _i6.PeriodTrackingModel>>? yearData,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#extractOvulationDatesFromYearData, [yearData]),
            returnValue: <DateTime>{},
          )
          as Set<DateTime>);

  @override
  _i4.Future<Set<DateTime>> getAllPeriodDates() =>
      (super.noSuchMethod(
            Invocation.method(#getAllPeriodDates, []),
            returnValue: _i4.Future<Set<DateTime>>.value(<DateTime>{}),
          )
          as _i4.Future<Set<DateTime>>);
}
