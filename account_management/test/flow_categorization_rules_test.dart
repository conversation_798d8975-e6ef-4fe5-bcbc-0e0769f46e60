import 'package:test/test.dart';
import 'package:account_management/domain/service/flow_categorization_rules_validator.dart';

void main() {
  group('Flow Categorization Rules Engine Tests', () {
    test('should validate all rules successfully', () {
      // Run comprehensive validation
      final report = FlowCategorizationRulesValidator.validateAllRules();
      
      // Print detailed report
      print('\n${report.getSummary()}');
      
      // Assert that all tests pass
      expect(report.failedTests, equals(0), 
        reason: 'Some rules failed validation. Check the detailed report above.');
      expect(report.passedTests, greaterThan(0), 
        reason: 'No tests were executed.');
      
      // Verify we have comprehensive test coverage
      expect(report.totalTests, greaterThanOrEqualTo(10), 
        reason: 'Should have at least 10 test scenarios covering all rules.');
    });
    
    test('should provide detailed rule explanations', () {
      // Test a specific scenario to verify rule explanations
      final testScenario = TestScenario(
        name: 'Test Rule Explanation',
        description: 'Verify that rule explanations are provided',
        currentDate: DateTime(2024, 1, 15),
        flowLevel: 2,
        existingData: {},
        expectedCurrentDayCategory: DayCategory.flowDay,
        expectedPreviousDayUpdate: null,
      );
      
      final result = FlowCategorizationRulesValidator.validateScenario(testScenario);
      
      // Verify that rule result contains explanations
      expect(result.ruleResult, isNotNull);
      expect(result.ruleResult!.ruleResults, isNotEmpty);
      
      // Check that each rule has proper explanation
      for (final rule in result.ruleResult!.ruleResults) {
        expect(rule.ruleId, isNotEmpty, reason: 'Rule should have an ID');
        expect(rule.ruleName, isNotEmpty, reason: 'Rule should have a name');
        expect(rule.explanation, isNotEmpty, reason: 'Rule should have an explanation');
        expect(rule.condition, isNotEmpty, reason: 'Rule should have a condition');
      }
      
      // Verify summary generation
      final summary = result.ruleResult!.getRulesSummary();
      expect(summary, contains('Flow Categorization Rules Summary'));
      expect(summary, contains('Final Results:'));
    });
    
    test('should handle edge cases correctly', () {
      // Test with no existing data (first flow entry ever)
      final testScenario = TestScenario(
        name: 'First Flow Entry Ever',
        description: 'Test behavior when no previous period data exists',
        currentDate: DateTime(2024, 1, 15),
        flowLevel: 2,
        existingData: {},
        expectedCurrentDayCategory: DayCategory.flowDay,
        expectedPreviousDayUpdate: null,
      );
      
      final result = FlowCategorizationRulesValidator.validateScenario(testScenario);
      
      // Should not throw errors and should provide a result
      expect(result.passed, isTrue);
      expect(result.actualCurrentDayCategory, isNotNull);
      expect(result.error, isNull);
    });
    
    test('should categorize rules by applied vs failed', () {
      // Run validation and check rule categorization
      final report = FlowCategorizationRulesValidator.validateAllRules();
      
      // Check that we can categorize rules
      final passedResults = report.getPassedResults();
      final failedResults = report.getFailedResults();
      
      expect(passedResults.length + failedResults.length, equals(report.totalTests));
      
      // For passed results, verify rule details
      for (final result in passedResults) {
        expect(result.ruleResult, isNotNull);
        
        final appliedRules = result.ruleResult!.getAppliedRules();
        final failedRules = result.ruleResult!.getFailedRules();
        
        expect(appliedRules, isNotEmpty, reason: 'Should have at least one applied rule');
        
        // Verify rule structure
        for (final rule in appliedRules) {
          expect(rule.conditionMet, isTrue);
          expect(rule.outcome, isNotNull);
        }
        
        for (final rule in failedRules) {
          expect(rule.conditionMet, isFalse);
        }
      }
    });
    
    test('should provide comprehensive rule coverage', () {
      // Verify that we test all the major rule categories
      final report = FlowCategorizationRulesValidator.validateAllRules();
      
      final allRuleIds = <String>{};
      for (final result in report.results) {
        if (result.ruleResult != null) {
          for (final rule in result.ruleResult!.ruleResults) {
            allRuleIds.add(rule.ruleId);
          }
        }
      }
      
      // Verify we have rules for all major categories
      final expectedRuleCategories = [
        'R1_PERIOD_PROXIMITY',      // Period proximity check
        'R2_PREVIOUS_DAY_FLOW',     // Previous day flow check
        'R3A_DAY_BEFORE_PREVIOUS_FLOW', // Day before previous flow (within 9 days)
        'R4_DAY_BEFORE_PREVIOUS_FLOW_BEYOND', // Day before previous flow (beyond 9 days)
        'R5_CHECK_PREVIOUS_DAY_UPDATE', // Previous day update check
      ];
      
      for (final category in expectedRuleCategories) {
        expect(allRuleIds.any((id) => id.startsWith(category.split('_')[0])), 
          isTrue, reason: 'Should have rules for category: $category');
      }
      
      print('\nAll Rule IDs found: ${allRuleIds.toList()..sort()}');
    });
  });
}
