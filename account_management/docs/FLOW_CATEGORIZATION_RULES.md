# Flow Categorization Rules Engine

## Overview

The Flow Categorization Rules Engine is a comprehensive system for categorizing flow days based on conditional rules. It acts as a transparent facade above all flow categorization calculations, making rules easy to understand, maintain, and verify.

## Architecture

### Core Components

1. **FlowCategorizationRulesEngine** - Main rules engine with transparent rule evaluation
2. **FlowCategorizationRulesValidator** - Comprehensive testing and validation system
3. **FlowCategorizationService** - Legacy service that now uses the rules engine

### Key Features

- **Transparent Rule Evaluation**: Every rule application is logged with detailed explanations
- **Comprehensive Testing**: 10+ test scenarios covering all conditional rules
- **Detailed Reporting**: Complete rule summaries with success/failure indicators
- **Backward Compatibility**: Existing services continue to work unchanged

## Rules Documentation

### Rule Categories

#### 1. Period Proximity Rules (R1)
- **R1_PERIOD_PROXIMITY**: Determines if current day is within 9 days of most recent period
- **Threshold**: 9 days
- **Impact**: Affects all subsequent rule evaluation

#### 2. Previous Day Flow Rules (R2)
- **R2_PREVIOUS_DAY_FLOW**: Checks if previous day has flow level > 0
- **Impact**: Determines which sub-rules to apply

#### 3. Within 9 Days Rules (R3A, R3B)
- **R3A_DAY_BEFORE_PREVIOUS_FLOW**: When previous day has no flow
- **R3A1_BOTH_NO_FLOW**: Both previous days no flow → Period Flow Day
- **R3A2A_FLOW_AND_PERIOD**: Day before previous has flow+period → Period Flow Day
- **R3A2B_FLOW_NOT_PERIOD**: Day before previous has flow only → Flow Day
- **R3B_PREVIOUS_DAY_PERIOD**: When previous day has flow
- **R3B1_FLOW_AND_PERIOD**: Previous day flow+period → Period Flow Day
- **R3B2_FLOW_NOT_PERIOD**: Previous day flow only → Flow Day

#### 4. Beyond 9 Days Rules (R4)
- **R4A_NO_FLOW_BEYOND**: No previous flow → Flow Day
- **R4B1_FLOW_AND_PERIOD_BEYOND**: Previous flow+period → Period Flow Day
- **R4B2_FLOW_NOT_PERIOD_BEYOND**: Previous flow only → Flow Day

#### 5. Previous Day Update Rules (R5-R7)
- **R5_CHECK_PREVIOUS_DAY_UPDATE**: Determines if previous day needs updating
- **R6_DAY_BEFORE_PREVIOUS_FLOW_UPDATE**: Checks day before previous for update logic
- **R7A_UPDATE_TO_PERIOD_FLOW_DAY**: Update previous day to period flow day
- **R7B_UPDATE_TO_PERIOD_DAY**: Update previous day to period day (connecting day)

## Usage

### Basic Usage

```dart
// Use the rules engine directly
final result = FlowCategorizationRulesEngine.categorizeFlowDay(
  currentDate: DateTime(2024, 1, 15),
  flowLevel: 2,
  existingData: existingPeriodData,
);

// Get detailed rule summary
print(result.getRulesSummary());

// Get only applied rules
final appliedRules = result.getAppliedRules();
```

### Validation and Testing

```dart
// Run comprehensive validation
final report = FlowCategorizationRulesValidator.validateAllRules();
print(report.getSummary());

// Test specific scenario
final testScenario = TestScenario(
  name: 'Custom Test',
  currentDate: DateTime(2024, 1, 15),
  flowLevel: 2,
  existingData: testData,
  expectedCurrentDayCategory: DayCategory.periodFlowDay,
);

final result = FlowCategorizationRulesValidator.validateScenario(testScenario);
```

### Legacy Service Integration

```dart
// Existing service automatically uses rules engine
final service = FlowCategorizationService();
final result = await service.categorizeFlowDay(
  currentDate: currentDate,
  flowLevel: flowLevel,
  existingData: existingData,
);
```

## Rule Outcomes

### Day Categories
- **FlowDay**: Has flow level but not considered a period day
- **PeriodFlowDay**: Has flow level and is considered a period day

### Previous Day Updates
- **ToPeriodFlowDay**: Re-categorize previous day to a "period flow day"
- **ToPeriodDay**: Re-categorize previous day as "just a period day" (connecting day)

## Testing

### Test Scenarios

The validator includes comprehensive test scenarios:

1. **Rule 1.1**: Both previous days no flow (within 9 days)
2. **Rule 1.2**: Day before previous flow+period (within 9 days)
3. **Rule 1.3**: Day before previous flow only (within 9 days)
4. **Rule 1.4**: Previous day flow+period (within 9 days)
5. **Rule 1.5**: Previous day flow only (within 9 days)
6. **Rule 2.1**: Beyond 9 days, no previous flow
7. **Rule 2.2**: Beyond 9 days, previous flow+period
8. **Rule 2.3**: Beyond 9 days, previous flow only
9. **Rule 3.1**: Update previous day to period flow day
10. **Rule 3.2**: Update previous day to period day

### Running Tests

```bash
# Run rules validation tests
cd account_management
dart test test/flow_categorization_rules_test.dart
```

## Benefits

### For Developers
- **Transparency**: Every rule application is logged and explained
- **Maintainability**: Rules are clearly documented and testable
- **Debuggability**: Easy to trace why a specific categorization was made
- **Reliability**: Comprehensive test coverage ensures correctness

### For Users
- **Consistency**: Rules are applied uniformly across all flow entries
- **Accuracy**: Complex conditional logic handles edge cases properly
- **Predictability**: Clear rules make behavior predictable

## Future Enhancements

1. **Rule Configuration**: Make rules configurable per user preferences
2. **Machine Learning**: Use historical data to improve rule accuracy
3. **Performance Optimization**: Cache rule evaluations for better performance
4. **Extended Validation**: Add more edge case test scenarios
5. **Visual Rule Editor**: Create UI for viewing and editing rules

## Troubleshooting

### Common Issues

1. **Type Conflicts**: Ensure proper import aliases when using both old and new enums
2. **Missing Data**: Rules handle missing previous day data gracefully
3. **Edge Cases**: First flow entry ever is handled as a special case

### Debug Information

The rules engine provides extensive debug logging:
- Rule evaluation steps
- Context data used
- Condition results
- Final outcomes
- Applied vs failed rules

Enable debug logging to see detailed rule evaluation:

```dart
debugPrint('🔧 Rules Engine Result Summary:');
debugPrint('   Applied Rules: ${result.getAppliedRules().length}');
debugPrint('   Final Current Day Category: ${result.finalCurrentDayCategory}');
```
