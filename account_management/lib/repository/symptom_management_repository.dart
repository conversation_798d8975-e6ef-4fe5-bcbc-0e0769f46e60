import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:doso/doso.dart';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

import '../domain/core/unit.dart';
import '../domain/facade/symptom_management_facade.dart';
import '../domain/failure/symptom_management_failure.dart';
import '../domain/model/symptom_model.dart';

@LazySingleton(as: SymptomManagementFacade)
class SymptomManagementRepository implements SymptomManagementFacade {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  static const String _localCacheKey = 'symptoms_cache';
  static const String _lastSyncKey = 'symptoms_last_sync';
  static const String _iconsDirectoryName = 'symptom_icons';

  @override
  Stream<Do<SymptomManagementFailure, Map<String, List<SymptomModel>>>>
      watchSymptomsByCategory() {
    try {
      return _firestore
          .collection('symptoms_master')
          .snapshots()
          .asyncMap((snapshot) async {
        try {
          final Map<String, List<SymptomModel>> symptomsByCategory = {};

          for (final doc in snapshot.docs) {
            final data = doc.data();
            final symptoms = (data['symptoms'] as List<dynamic>?)
                    ?.map((symptomData) => SymptomModel.fromJson(
                        symptomData as Map<String, dynamic>))
                    .toList() ??
                [];

            if (symptoms.isNotEmpty) {
              symptomsByCategory[doc.id] = symptoms;
            }
          }

          // Update local cache
          await saveToLocalCache(symptomsByCategory);

          // Download missing icons
          await _downloadMissingIcons(symptomsByCategory);

          return Do.success(symptomsByCategory);
        } catch (e) {
          return Do.failure(SymptomManagementFailure.parseFailure());
        }
      });
    } catch (e) {
      return Stream.value(
          Do.failure(SymptomManagementFailure.networkFailure()));
    }
  }

  @override
  Future<Do<SymptomManagementFailure, Unit>> syncSymptomsFromRemote() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return Do.failure(SymptomManagementFailure.unauthenticated());
      }

      final snapshot = await _firestore.collection('symptoms_master').get();
      final Map<String, List<SymptomModel>> symptomsByCategory = {};

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final symptoms = (data['symptoms'] as List<dynamic>?)
                ?.map((symptomData) =>
                    SymptomModel.fromJson(symptomData as Map<String, dynamic>))
                .toList() ??
            [];

        if (symptoms.isNotEmpty) {
          symptomsByCategory[doc.id] = symptoms;
        }
      }

      // Save to local cache
      await saveToLocalCache(symptomsByCategory);

      // Download missing icons
      await _downloadMissingIcons(symptomsByCategory);

      // Update last sync timestamp
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastSyncKey, DateTime.now().millisecondsSinceEpoch);

      return Do.success(Unit());
    } catch (e) {
      return Do.failure(SymptomManagementFailure.syncFailure());
    }
  }

  @override
  Future<Do<SymptomManagementFailure, String>> downloadAndCacheIcon(
      String iconUrl, String symptomName) async {
    try {
      final response = await http.get(Uri.parse(iconUrl));
      if (response.statusCode != 200) {
        return Do.failure(SymptomManagementFailure.downloadFailure());
      }

      final directory = await getApplicationDocumentsDirectory();
      final iconsDir = Directory('${directory.path}/$_iconsDirectoryName');
      if (!await iconsDir.exists()) {
        await iconsDir.create(recursive: true);
      }

      final fileName = '${symptomName.toLowerCase().replaceAll(' ', '_')}.svg';
      final file = File('${iconsDir.path}/$fileName');
      await file.writeAsBytes(response.bodyBytes);

      return Do.success(file.path);
    } catch (e) {
      return Do.failure(SymptomManagementFailure.downloadFailure());
    }
  }

  @override
  Future<String?> getLocalIconPath(String symptomName) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = '${symptomName.toLowerCase().replaceAll(' ', '_')}.svg';
      final file = File('${directory.path}/$_iconsDirectoryName/$fileName');

      if (await file.exists()) {
        return file.path;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<bool> shouldSyncWithRemote() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastSync = prefs.getInt(_lastSyncKey) ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;

      // Sync if last sync was more than 24 hours ago
      const twentyFourHours = 24 * 60 * 60 * 1000;
      return (now - lastSync) > twentyFourHours;
    } catch (e) {
      return true; // Default to sync if we can't determine
    }
  }

  @override
  Future<Do<SymptomManagementFailure, Map<String, List<SymptomModel>>>>
      getLocalSymptoms() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_localCacheKey);

      if (cachedData == null) {
        return Do.success({});
      }

      final Map<String, dynamic> decodedData = jsonDecode(cachedData);
      final Map<String, List<SymptomModel>> symptomsByCategory = {};

      decodedData.forEach((category, symptoms) {
        symptomsByCategory[category] = (symptoms as List<dynamic>)
            .map((symptomData) =>
                SymptomModel.fromJson(symptomData as Map<String, dynamic>))
            .toList();
      });

      return Do.success(symptomsByCategory);
    } catch (e) {
      return Do.failure(SymptomManagementFailure.cacheFailure());
    }
  }

  @override
  Future<Do<SymptomManagementFailure, Unit>> saveToLocalCache(
      Map<String, List<SymptomModel>> symptomsByCategory) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final Map<String, dynamic> dataToCache = {};
      symptomsByCategory.forEach((category, symptoms) {
        dataToCache[category] =
            symptoms.map((symptom) => symptom.toJson()).toList();
      });

      await prefs.setString(_localCacheKey, jsonEncode(dataToCache));
      return Do.success(Unit());
    } catch (e) {
      return Do.failure(SymptomManagementFailure.cacheFailure());
    }
  }

  @override
  Future<Do<SymptomManagementFailure, Unit>> clearLocalCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_localCacheKey);
      await prefs.remove(_lastSyncKey);

      // Also clear icon cache
      final directory = await getApplicationDocumentsDirectory();
      final iconsDir = Directory('${directory.path}/$_iconsDirectoryName');
      if (await iconsDir.exists()) {
        await iconsDir.delete(recursive: true);
      }

      return Do.success(Unit());
    } catch (e) {
      return Do.failure(SymptomManagementFailure.cacheFailure());
    }
  }

  // Helper method to download missing icons
  Future<void> _downloadMissingIcons(
      Map<String, List<SymptomModel>> symptomsByCategory) async {
    for (final symptoms in symptomsByCategory.values) {
      for (final symptom in symptoms) {
        // Only try to download if iconUrl is not null
        if (symptom.iconUrl != null && symptom.iconUrl!.isNotEmpty) {
          final localPath = await getLocalIconPath(symptom.name);
          if (localPath == null) {
            // Icon not cached, download it
            await downloadAndCacheIcon(symptom.iconUrl!, symptom.name);
          }
        }
      }
    }
  }
}
