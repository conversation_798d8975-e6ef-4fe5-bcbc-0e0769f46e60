import 'package:doso/doso.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import '../../domain/core/unit.dart';
import '../../domain/failure/period_tracking_failure.dart';
import '../../domain/model/period_tracking_model.dart';
import '../../domain/service/flow_categorization_rules_engine.dart'
    as rules_engine;

/// Service for categorizing flow days based on conditional rules
/// Now acts as a facade to the comprehensive rules engine
@injectable
class FlowCategorizationService {
  FlowCategorizationService();

  /// Categorizes a day when a user enters a "flow" based on the relationship to past tracked days
  ///
  /// Inputs:
  /// - currentDate: The date where flow was entered
  /// - flowLevel: The flow level entered (1-3)
  /// - existingData: Map of existing period tracking data for context
  ///
  /// Returns: Updated categorization for current day and any previous days that need updating
  Future<Do<PeriodTrackingFailure, FlowCategorizationResult>>
      categorizeFlowDay({
    required DateTime currentDate,
    required int flowLevel,
    required Map<String, Map<String, PeriodTrackingModel>> existingData,
  }) async {
    try {
      debugPrint(
          '🔧 FlowCategorizationService: Using rules engine for $currentDate with flow level $flowLevel');

      // Use the comprehensive rules engine
      final ruleResult =
          rules_engine.FlowCategorizationRulesEngine.categorizeFlowDay(
        currentDate: currentDate,
        flowLevel: flowLevel,
        existingData: existingData,
      );

      // Log the detailed rule evaluation for transparency
      debugPrint('🔧 Rules Engine Result Summary:');
      debugPrint('   Applied Rules: ${ruleResult.getAppliedRules().length}');
      debugPrint(
          '   Final Current Day Category: ${ruleResult.finalCurrentDayCategory}');
      debugPrint(
          '   Final Previous Day Update: ${ruleResult.finalPreviousDayUpdate}');

      // Convert to legacy format for backward compatibility
      final legacyResult = FlowCategorizationResult(
        currentDayCategory:
            _convertDayCategory(ruleResult.finalCurrentDayCategory),
        previousDayUpdate:
            _convertPreviousDayUpdate(ruleResult.finalPreviousDayUpdate),
        flowLevel: flowLevel,
      );

      return Do.success(legacyResult);
    } catch (e) {
      debugPrint('❌ Error in flow categorization service: $e');
      return const Do.failure(PeriodTrackingFailure.unexpected());
    }
  }

  /// Apply the conditional rules for categorizing the current day
  FlowCategorizationResult _applyConditionalRules({
    required DateTime currentDate,
    required int flowLevel,
    required int dayCount,
    required PeriodTrackingModel? previousDayData,
    required PeriodTrackingModel? dayBeforePreviousData,
  }) {
    // Rule 1: Check for an Existing Period
    DayCategory currentDayCategory;

    if (dayCount <= 9) {
      // Within 9 days of previous period
      if (previousDayData?.isFlowDay != true) {
        // Previous day is NOT a flow day
        if (dayBeforePreviousData?.isFlowDay != true) {
          // Day before current day is NOT a flow day
          currentDayCategory = DayCategory.periodFlowDay;
        } else {
          // Day before current day IS a flow day
          if (dayBeforePreviousData?.isPeriodDate == true) {
            // Day before current day is also a period day
            currentDayCategory = DayCategory.periodFlowDay;
          } else {
            // Day before current day is NOT a period day
            currentDayCategory = DayCategory.flowDay;
          }
        }
      } else {
        // Previous day IS a flow day
        if (previousDayData?.isPeriodDate == true) {
          // Previous day is also a period day
          currentDayCategory = DayCategory.periodFlowDay;
        } else {
          // Previous day is NOT a period day
          currentDayCategory = DayCategory.flowDay;
        }
      }
    } else {
      // More than 9 days from previous period
      if (dayBeforePreviousData?.isFlowDay != true) {
        // Day before current day is NOT a flow day
        currentDayCategory = DayCategory.flowDay;
      } else {
        // Day before current day IS a flow day
        if (dayBeforePreviousData?.isPeriodDate == true) {
          // Day before current day is also a period day
          currentDayCategory = DayCategory.periodFlowDay;
        } else {
          // Day before current day is NOT a period day
          currentDayCategory = DayCategory.flowDay;
        }
      }
    }

    // Rule 2: Check for Update to Previous Day
    PreviousDayUpdate? previousDayUpdate;

    if (currentDayCategory == DayCategory.periodFlowDay &&
        previousDayData?.isFlowDay == true) {
      if (dayBeforePreviousData?.isFlowDay == true) {
        if (dayBeforePreviousData?.isPeriodDate == true) {
          // Day before previous day is also a period day
          previousDayUpdate = PreviousDayUpdate.toPeriodFlowDay;
        } else {
          // Day before previous day is NOT a flow day
          previousDayUpdate = PreviousDayUpdate.toPeriodDay;
        }
      }
    }

    return FlowCategorizationResult(
      currentDayCategory: currentDayCategory,
      previousDayUpdate: previousDayUpdate,
      flowLevel: flowLevel,
    );
  }

  /// Find the most recent period date before the given date
  DateTime? _findMostRecentPeriodDate(
    Map<String, Map<String, PeriodTrackingModel>> existingData,
    DateTime beforeDate,
  ) {
    DateTime? mostRecent;

    for (final monthData in existingData.values) {
      for (final dayData in monthData.values) {
        if (dayData.isPeriodDate == true &&
            dayData.date != null &&
            dayData.date!.isBefore(beforeDate)) {
          if (mostRecent == null || dayData.date!.isAfter(mostRecent)) {
            mostRecent = dayData.date;
          }
        }
      }
    }

    return mostRecent;
  }

  /// Get period tracking data for a specific date
  PeriodTrackingModel? _getDataForDate(
    Map<String, Map<String, PeriodTrackingModel>> existingData,
    DateTime date,
  ) {
    final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
    final dayKey = date.day.toString().padLeft(2, '0');

    return existingData[monthKey]?[dayKey];
  }

  /// Convert new DayCategory enum to legacy enum
  DayCategory _convertDayCategory(rules_engine.DayCategory newCategory) {
    switch (newCategory) {
      case rules_engine.DayCategory.flowDay:
        return DayCategory.flowDay;
      case rules_engine.DayCategory.periodFlowDay:
        return DayCategory.periodFlowDay;
    }
  }

  /// Convert new PreviousDayUpdate enum to legacy enum
  PreviousDayUpdate? _convertPreviousDayUpdate(
      rules_engine.PreviousDayUpdate? newUpdate) {
    if (newUpdate == null) return null;

    switch (newUpdate) {
      case rules_engine.PreviousDayUpdate.toPeriodFlowDay:
        return PreviousDayUpdate.toPeriodFlowDay;
      case rules_engine.PreviousDayUpdate.toPeriodDay:
        return PreviousDayUpdate.toPeriodDay;
    }
  }
}

/// Result of flow categorization
class FlowCategorizationResult {
  final DayCategory currentDayCategory;
  final PreviousDayUpdate? previousDayUpdate;
  final int flowLevel;

  FlowCategorizationResult({
    required this.currentDayCategory,
    this.previousDayUpdate,
    required this.flowLevel,
  });
}

/// Categories for days based on flow and period status
enum DayCategory {
  flowDay, // Has flow level but not considered a period day
  periodFlowDay, // Has flow level and is considered a period day
}

/// Updates that may need to be applied to previous days
enum PreviousDayUpdate {
  toPeriodFlowDay, // Re-categorize previous day to a "period flow day"
  toPeriodDay, // Re-categorize previous day as "just a period day"
}
