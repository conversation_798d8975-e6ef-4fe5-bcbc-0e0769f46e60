import 'package:doso/doso.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import '../../domain/core/unit.dart';
import '../../domain/failure/period_tracking_failure.dart';
import '../../domain/model/period_tracking_model.dart';
import 'firestore_service.dart';

/// Service for categorizing flow days based on conditional rules
@injectable
class FlowCategorizationService {
  final FirestoreService _firestoreService;

  FlowCategorizationService(this._firestoreService);

  /// Categorizes a day when a user enters a "flow" based on the relationship to past tracked days
  /// 
  /// Inputs:
  /// - currentDate: The date where flow was entered
  /// - flowLevel: The flow level entered (1-3)
  /// - existingData: Map of existing period tracking data for context
  /// 
  /// Returns: Updated categorization for current day and any previous days that need updating
  Future<Do<PeriodTrackingFailure, FlowCategorizationResult>> categorizeFlowDay({
    required DateTime currentDate,
    required int flowLevel,
    required Map<String, Map<String, PeriodTrackingModel>> existingData,
  }) async {
    try {
      debugPrint('🩸 Categorizing flow day for $currentDate with flow level $flowLevel');

      // Step 1: Find the most recent period date
      final previousFirstPeriodFlowDay = _findMostRecentPeriodDate(existingData, currentDate);
      
      // Step 2: Calculate day count between current day and previous first period flow day
      final dayCount = previousFirstPeriodFlowDay != null 
          ? currentDate.difference(previousFirstPeriodFlowDay).inDays
          : 999; // Large number if no previous period found

      // Step 3: Get status of previous days
      final previousDay = currentDate.subtract(const Duration(days: 1));
      final dayBeforePrevious = currentDate.subtract(const Duration(days: 2));
      
      final previousDayData = _getDataForDate(existingData, previousDay);
      final dayBeforePreviousData = _getDataForDate(existingData, dayBeforePrevious);

      debugPrint('🩸 Previous first period flow day: $previousFirstPeriodFlowDay');
      debugPrint('🩸 Day count: $dayCount');
      debugPrint('🩸 Previous day flow: ${previousDayData?.isFlowDay}, period: ${previousDayData?.isPeriodDate}');
      debugPrint('🩸 Day before previous flow: ${dayBeforePreviousData?.isFlowDay}, period: ${dayBeforePreviousData?.isPeriodDate}');

      // Step 4: Apply conditional rules
      final result = _applyConditionalRules(
        currentDate: currentDate,
        flowLevel: flowLevel,
        dayCount: dayCount,
        previousDayData: previousDayData,
        dayBeforePreviousData: dayBeforePreviousData,
      );

      debugPrint('🩸 Categorization result: ${result.currentDayCategory}');
      if (result.previousDayUpdate != null) {
        debugPrint('🩸 Previous day update: ${result.previousDayUpdate}');
      }

      return Do.success(result);
    } catch (e) {
      debugPrint('❌ Error categorizing flow day: $e');
      return Do.failure(PeriodTrackingFailure.unexpected());
    }
  }

  /// Apply the conditional rules for categorizing the current day
  FlowCategorizationResult _applyConditionalRules({
    required DateTime currentDate,
    required int flowLevel,
    required int dayCount,
    required PeriodTrackingModel? previousDayData,
    required PeriodTrackingModel? dayBeforePreviousData,
  }) {
    // Rule 1: Check for an Existing Period
    DayCategory currentDayCategory;
    
    if (dayCount <= 9) {
      // Within 9 days of previous period
      if (previousDayData?.isFlowDay != true) {
        // Previous day is NOT a flow day
        if (dayBeforePreviousData?.isFlowDay != true) {
          // Day before current day is NOT a flow day
          currentDayCategory = DayCategory.periodFlowDay;
        } else {
          // Day before current day IS a flow day
          if (dayBeforePreviousData?.isPeriodDate == true) {
            // Day before current day is also a period day
            currentDayCategory = DayCategory.periodFlowDay;
          } else {
            // Day before current day is NOT a period day
            currentDayCategory = DayCategory.flowDay;
          }
        }
      } else {
        // Previous day IS a flow day
        if (previousDayData?.isPeriodDate == true) {
          // Previous day is also a period day
          currentDayCategory = DayCategory.periodFlowDay;
        } else {
          // Previous day is NOT a period day
          currentDayCategory = DayCategory.flowDay;
        }
      }
    } else {
      // More than 9 days from previous period
      if (dayBeforePreviousData?.isFlowDay != true) {
        // Day before current day is NOT a flow day
        currentDayCategory = DayCategory.flowDay;
      } else {
        // Day before current day IS a flow day
        if (dayBeforePreviousData?.isPeriodDate == true) {
          // Day before current day is also a period day
          currentDayCategory = DayCategory.periodFlowDay;
        } else {
          // Day before current day is NOT a period day
          currentDayCategory = DayCategory.flowDay;
        }
      }
    }

    // Rule 2: Check for Update to Previous Day
    PreviousDayUpdate? previousDayUpdate;
    
    if (currentDayCategory == DayCategory.periodFlowDay && previousDayData?.isFlowDay == true) {
      if (dayBeforePreviousData?.isFlowDay == true) {
        if (dayBeforePreviousData?.isPeriodDate == true) {
          // Day before previous day is also a period day
          previousDayUpdate = PreviousDayUpdate.toPeriodFlowDay;
        } else {
          // Day before previous day is NOT a flow day
          previousDayUpdate = PreviousDayUpdate.toPeriodDay;
        }
      }
    }

    return FlowCategorizationResult(
      currentDayCategory: currentDayCategory,
      previousDayUpdate: previousDayUpdate,
      flowLevel: flowLevel,
    );
  }

  /// Find the most recent period date before the given date
  DateTime? _findMostRecentPeriodDate(
    Map<String, Map<String, PeriodTrackingModel>> existingData,
    DateTime beforeDate,
  ) {
    DateTime? mostRecent;
    
    for (final monthData in existingData.values) {
      for (final dayData in monthData.values) {
        if (dayData.isPeriodDate == true && 
            dayData.date != null && 
            dayData.date!.isBefore(beforeDate)) {
          if (mostRecent == null || dayData.date!.isAfter(mostRecent)) {
            mostRecent = dayData.date;
          }
        }
      }
    }
    
    return mostRecent;
  }

  /// Get period tracking data for a specific date
  PeriodTrackingModel? _getDataForDate(
    Map<String, Map<String, PeriodTrackingModel>> existingData,
    DateTime date,
  ) {
    final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
    final dayKey = date.day.toString().padLeft(2, '0');
    
    return existingData[monthKey]?[dayKey];
  }
}

/// Result of flow categorization
class FlowCategorizationResult {
  final DayCategory currentDayCategory;
  final PreviousDayUpdate? previousDayUpdate;
  final int flowLevel;

  FlowCategorizationResult({
    required this.currentDayCategory,
    this.previousDayUpdate,
    required this.flowLevel,
  });
}

/// Categories for days based on flow and period status
enum DayCategory {
  flowDay,        // Has flow level but not considered a period day
  periodFlowDay,  // Has flow level and is considered a period day
}

/// Updates that may need to be applied to previous days
enum PreviousDayUpdate {
  toPeriodFlowDay,  // Re-categorize previous day to a "period flow day"
  toPeriodDay,      // Re-categorize previous day as "just a period day"
}
