import 'package:doso/doso.dart';
import 'package:flutter/foundation.dart';
import '../../domain/core/unit.dart';
import 'package:injectable/injectable.dart';
import '../../domain/failure/period_tracking_failure.dart';
import '../../domain/model/period_tracking_model.dart';
import 'firestore_service.dart';
import 'flow_categorization_service.dart';

/// Service for managing period date selection and deselection
@injectable
class PeriodDataService {
  final FirestoreService _firestoreService;
  final FlowCategorizationService _flowCategorizationService;

  PeriodDataService(this._firestoreService, this._flowCategorizationService);

  /// Select period dates in Firestore (sets isPeriodDate: true) with optional flow levels
  Future<Do<PeriodTrackingFailure, Unit>> selectPeriodDates(
    Set<DateTime> selectedDates, {
    Map<DateTime, int>? flowLevels,
  }) async {
    try {
      if (selectedDates.isEmpty) {
        return const Do.success(unit);
      }

      debugPrint(
          '📅 Selecting ${selectedDates.length} period dates: $selectedDates');
      if (flowLevels != null && flowLevels.isNotEmpty) {
        debugPrint(
            '💧 With flow levels for ${flowLevels.length} dates: $flowLevels');
      }

      // Group dates by month for efficient updates
      final monthlyUpdates = _groupDatesByMonth(selectedDates);

      final result = await _firestoreService.batchUpdatePeriodDates(
        monthlyUpdates: monthlyUpdates,
        isPeriodDate: true,
        flowLevels: flowLevels,
      );

      return result.fold(
        onFailure: (failure) {
          debugPrint('❌ Failed to select period dates: $failure');
          return Do.failure(failure);
        },
        onSuccess: (_) {
          debugPrint(
              '✅ Successfully selected ${selectedDates.length} period dates');
          if (flowLevels != null && flowLevels.isNotEmpty) {
            debugPrint(
                '✅ Successfully saved flow levels for ${flowLevels.length} dates');
          }
          return Do.success(unit);
        },
      );
    } catch (e) {
      debugPrint('❌ Error selecting period dates: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  /// Deselect period dates in Firestore (sets isPeriodDate: false)
  Future<Do<PeriodTrackingFailure, Unit>> deselectPeriodDates(
      Set<DateTime> datesToDeselect) async {
    try {
      if (datesToDeselect.isEmpty) {
        return const Do.success(unit);
      }

      debugPrint(
          '📅 Deselecting ${datesToDeselect.length} period dates: $datesToDeselect');

      // Group dates by month for efficient updates
      final monthlyUpdates = _groupDatesByMonth(datesToDeselect);

      final result = await _firestoreService.batchUpdatePeriodDates(
        monthlyUpdates: monthlyUpdates,
        isPeriodDate: false,
      );

      return result.fold(
        onFailure: (failure) {
          debugPrint('❌ Failed to deselect period dates: $failure');
          return Do.failure(failure);
        },
        onSuccess: (_) {
          debugPrint(
              '✅ Successfully deselected ${datesToDeselect.length} period dates');
          return Do.success(unit);
        },
      );
    } catch (e) {
      debugPrint('❌ Error deselecting period dates: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  /// Get all existing period dates from Firestore
  Future<Set<DateTime>> getAllExistingPeriodDates() async {
    try {
      final user = _firestoreService.currentUser;
      if (user == null) return {};

      final allDates = <DateTime>{};
      final now = DateTime.now();

      // Look back 2 years and forward 1 year for comprehensive data
      final startYear = now.year - 2;
      final endYear = now.year + 1;

      for (int year = startYear; year <= endYear; year++) {
        final yearDataResult =
            await _firestoreService.watchYearData(year).first;

        yearDataResult.fold(
          onFailure: (failure) {
            debugPrint('Failed to get year data for $year: $failure');
          },
          onSuccess: (yearData) {
            for (final monthEntry in yearData.entries) {
              for (final dayEntry in monthEntry.value.entries) {
                final model = dayEntry.value;
                if (model.isPeriodDate == true && model.date != null) {
                  final normalizedDate = DateTime(
                    model.date!.year,
                    model.date!.month,
                    model.date!.day,
                  );
                  allDates.add(normalizedDate);
                }
              }
            }
          },
        );
      }

      debugPrint('📅 Found ${allDates.length} existing period dates');
      return allDates;
    } catch (e) {
      debugPrint('❌ Error getting all existing period dates: $e');
      return {};
    }
  }

  /// Calculate period cycles from period dates
  List<List<DateTime>> calculatePeriodCycles(Set<DateTime> periodDates) {
    if (periodDates.isEmpty) return [];

    final sortedDates = periodDates.toList()..sort();
    final cycles = <List<DateTime>>[];
    var currentCycle = <DateTime>[];

    for (int i = 0; i < sortedDates.length; i++) {
      final currentDate = sortedDates[i];

      if (currentCycle.isEmpty) {
        currentCycle.add(currentDate);
      } else {
        final lastDate = currentCycle.last;
        final daysDifference = currentDate.difference(lastDate).inDays;

        if (daysDifference <= 2) {
          // Consecutive day or single-day gap - add to current cycle
          currentCycle.add(currentDate);
        } else {
          // Gap of 2+ days found - end current cycle and start new one
          cycles.add(List.from(currentCycle));
          currentCycle = [currentDate];
        }
      }
    }

    // Add the last cycle
    if (currentCycle.isNotEmpty) {
      cycles.add(currentCycle);
    }

    debugPrint(
        '📊 Calculated ${cycles.length} period cycles from ${periodDates.length} dates');
    return cycles;
  }

  /// Check if a cycle is valid for ovulation calculation
  bool isValidCycleForOvulation(
    List<DateTime> currentCycle,
    List<List<DateTime>> allCycles,
    int cycleIndex,
    int userCycleLength,
  ) {
    if (currentCycle.isEmpty) return false;

    // Must have at least 1 day
    if (currentCycle.length < 1) return false;

    // If there's a next cycle, check the gap
    if (cycleIndex + 1 < allCycles.length) {
      final nextCycle = allCycles[cycleIndex + 1];
      final currentCycleEnd = currentCycle.last;
      final nextCycleStart = nextCycle.first;
      final gapDays = nextCycleStart.difference(currentCycleEnd).inDays;

      // Gap should be reasonable (between 7 and 45 days)
      // This allows for cycle lengths from 14 to 50 days
      if (gapDays < 7 || gapDays > 45) {
        debugPrint('⚠️ Invalid cycle gap: $gapDays days between cycles');
        return false;
      }

      // Gap should be somewhat close to user's cycle length (±10 days tolerance)
      final expectedGap = userCycleLength - currentCycle.length;
      if ((gapDays - expectedGap).abs() > 10) {
        debugPrint(
            '⚠️ Cycle gap ($gapDays) differs significantly from expected ($expectedGap)');
        return false;
      }
    }

    return true;
  }

  /// Process flow level entry using categorization rules
  Future<Do<PeriodTrackingFailure, Unit>> processFlowEntry({
    required DateTime date,
    required int flowLevel,
    required Map<String, Map<String, PeriodTrackingModel>> existingData,
  }) async {
    try {
      debugPrint('🩸 Processing flow entry for $date with level $flowLevel');

      // Use flow categorization service to determine day category
      final categorizationResult =
          await _flowCategorizationService.categorizeFlowDay(
        currentDate: date,
        flowLevel: flowLevel,
        existingData: existingData,
      );

      return categorizationResult.fold(
        onFailure: (failure) {
          debugPrint('❌ Flow categorization failed: $failure');
          return Do.failure(failure);
        },
        onSuccess: (result) async {
          // Update current day based on categorization
          final isPeriodDate =
              result.currentDayCategory == DayCategory.periodFlowDay;

          final updateResult = await _firestoreService.updateFlowCategorization(
            date: date,
            flowLevel: flowLevel,
            isPeriodDate: isPeriodDate,
          );

          return updateResult.fold(
            onFailure: (failure) => Do.failure(failure),
            onSuccess: (_) async {
              // Handle previous day updates if needed
              if (result.previousDayUpdate != null) {
                final previousDay = date.subtract(const Duration(days: 1));
                final shouldBePeriodDate = result.previousDayUpdate ==
                        PreviousDayUpdate.toPeriodFlowDay ||
                    result.previousDayUpdate == PreviousDayUpdate.toPeriodDay;

                final previousUpdateResult =
                    await _firestoreService.updateFlowCategorization(
                  date: previousDay,
                  flowLevel:
                      0, // Keep existing flow level, just update period status
                  isPeriodDate: shouldBePeriodDate,
                );

                return previousUpdateResult.fold(
                  onFailure: (failure) => Do.failure(failure),
                  onSuccess: (_) {
                    debugPrint('✅ Successfully processed flow entry for $date');
                    return const Do.success(unit);
                  },
                );
              } else {
                debugPrint('✅ Successfully processed flow entry for $date');
                return const Do.success(unit);
              }
            },
          );
        },
      );
    } catch (e) {
      debugPrint('❌ Error processing flow entry: $e');
      return const Do.failure(PeriodTrackingFailure.updateFailure());
    }
  }

  /// Validates if a date should be considered a period date based on flow data
  /// Rule: At least 2 days in any 3 consecutive day period should have flow
  Future<bool> shouldMarkAsPeriodDate(
    DateTime targetDate,
    Map<DateTime, int> newFlowData,
  ) async {
    try {
      debugPrint('🩸 Validating period date for $targetDate');
      debugPrint('🩸 New flow data: $newFlowData');

      // Check if target date has flow
      if (!newFlowData.containsKey(targetDate) ||
          newFlowData[targetDate]! <= 0) {
        debugPrint('🩸 Target date has no flow, not a period date');
        return false;
      }

      // Get existing flow data from Firestore
      final existingFlowData = await _getExistingFlowData(targetDate);

      // Merge existing and new flow data
      final allFlowData = <DateTime, int>{...existingFlowData, ...newFlowData};

      debugPrint('🩸 Combined flow data: $allFlowData');

      // Get all dates with flow around the target date (within 7 days)
      final datesWithFlow = allFlowData.entries
          .where((entry) =>
              entry.value > 0 &&
              entry.key.difference(targetDate).inDays.abs() <= 7)
          .map((entry) => entry.key)
          .toSet();

      debugPrint('🩸 Dates with flow near target: $datesWithFlow');

      // Check 3-day windows around the target date
      for (int offset = -2; offset <= 2; offset++) {
        final windowStart = targetDate.add(Duration(days: offset));
        final windowDates = [
          windowStart,
          windowStart.add(const Duration(days: 1)),
          windowStart.add(const Duration(days: 2)),
        ];

        final flowDaysInWindow =
            windowDates.where((date) => datesWithFlow.contains(date)).length;

        debugPrint(
            '🩸 Window starting $windowStart: $flowDaysInWindow/3 days with flow');

        if (flowDaysInWindow >= 2) {
          debugPrint(
              '✅ Found valid period window: at least 2 days with flow in 3-day period');
          return true;
        }
      }

      debugPrint('❌ No valid period window found');
      return false;
    } catch (e) {
      debugPrint('❌ Error validating period date: $e');
      return false;
    }
  }

  /// Get existing flow data from Firestore for validation
  Future<Map<DateTime, int>> _getExistingFlowData(DateTime targetDate) async {
    try {
      // Get data from a wider range (2 weeks) to properly validate period patterns
      final startDate = targetDate.subtract(const Duration(days: 14));
      final endDate = targetDate.add(const Duration(days: 14));

      final flowData = <DateTime, int>{};

      // Query each day in the range
      for (DateTime date = startDate;
          date.isBefore(endDate) || date.isAtSameMomentAs(endDate);
          date = date.add(const Duration(days: 1))) {
        final result = await _firestoreService.getSymptomData(date: date);
        result.fold(
          onFailure: (_) {}, // Ignore failures, just skip the date
          onSuccess: (data) {
            if (data != null && data.flowLevel != null && data.flowLevel! > 0) {
              flowData[date] = data.flowLevel!;
            }
          },
        );
      }

      debugPrint('🩸 Retrieved existing flow data: $flowData');
      return flowData;
    } catch (e) {
      debugPrint('❌ Error getting existing flow data: $e');
      return {};
    }
  }

  /// Group dates by month for efficient Firestore operations
  Map<String, Map<String, Set<String>>> _groupDatesByMonth(
      Set<DateTime> dates) {
    final monthlyUpdates = <String, Map<String, Set<String>>>{};

    for (final date in dates) {
      final year = date.year.toString();
      final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
      final dayKey = date.day.toString().padLeft(2, '0');

      monthlyUpdates[year] ??= {};
      monthlyUpdates[year]![monthKey] ??= {};
      monthlyUpdates[year]![monthKey]!.add(dayKey);
    }

    return monthlyUpdates;
  }

  /// Extract period dates from year data
  Set<DateTime> extractPeriodDatesFromYearData(
      Map<String, Map<String, PeriodTrackingModel>> yearData) {
    final periodDates = <DateTime>{};

    for (final monthEntry in yearData.entries) {
      for (final dayEntry in monthEntry.value.entries) {
        final model = dayEntry.value;
        if (model.isPeriodDate == true && model.date != null) {
          final normalizedDate = DateTime(
            model.date!.year,
            model.date!.month,
            model.date!.day,
          );
          periodDates.add(normalizedDate);
        }
      }
    }

    return periodDates;
  }

  /// Extract ovulation dates from year data
  Set<DateTime> extractOvulationDatesFromYearData(
      Map<String, Map<String, PeriodTrackingModel>> yearData) {
    final ovulationDates = <DateTime>{};

    for (final monthEntry in yearData.entries) {
      for (final dayEntry in monthEntry.value.entries) {
        final model = dayEntry.value;
        if (model.isOvulationDate == true && model.date != null) {
          final normalizedDate = DateTime(
            model.date!.year,
            model.date!.month,
            model.date!.day,
          );
          ovulationDates.add(normalizedDate);
        }
      }
    }

    return ovulationDates;
  }

  /// Get all existing period dates from Firestore
  Future<Set<DateTime>> getAllPeriodDates() async {
    try {
      debugPrint('🔍 PeriodDataService.getAllPeriodDates called');

      // Get data for multiple years to ensure we capture all period dates
      final currentYear = DateTime.now().year;
      final Set<DateTime> allPeriodDates = {};

      // Check current year and previous year
      for (int year = currentYear - 1; year <= currentYear + 1; year++) {
        final yearData = await _firestoreService.watchYearData(year).first;

        yearData.fold(
          onFailure: (failure) {
            debugPrint(
                '⚠️ Failed to get period dates for year $year: $failure');
          },
          onSuccess: (monthsData) {
            for (final monthData in monthsData.values) {
              for (final dayData in monthData.values) {
                if (dayData.isPeriodDate == true && dayData.date != null) {
                  final normalizedDate = DateTime(
                    dayData.date!.year,
                    dayData.date!.month,
                    dayData.date!.day,
                  );
                  allPeriodDates.add(normalizedDate);
                }
              }
            }
          },
        );
      }

      debugPrint('🔍 Found ${allPeriodDates.length} total period dates');
      return allPeriodDates;
    } catch (e) {
      debugPrint('❌ Error getting all period dates: $e');
      return <DateTime>{};
    }
  }
}
