import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:doso/doso.dart';
import 'package:injectable/injectable.dart';
import '../../domain/core/unit.dart';
import '../../domain/failure/period_tracking_failure.dart';
import '../../domain/model/period_tracking_model.dart';

/// Low-level Firestore operations service for period tracking
@injectable
class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Get current authenticated user
  User? get currentUser => _auth.currentUser;

  /// Watch year data from Firestore
  Stream<
      Do<PeriodTrackingFailure,
          Map<String, Map<String, PeriodTrackingModel>>>> watchYearData(
      int year) {
    try {
      final user = currentUser;
      print(
          '🔥 FirestoreService.watchYearData called for year: $year, user: ${user?.uid}');

      if (user == null) {
        print('❌ FirestoreService.watchYearData: User not authenticated');
        return Stream.value(
            Do.failure(const PeriodTrackingFailure.unauthenticated()));
      }

      final collectionPath =
          'period_tracking/${user.uid}/years/${year.toString()}/months';
      print('🔥 FirestoreService: Watching collection path: $collectionPath');

      return _firestore
          .collection('period_tracking')
          .doc(user.uid)
          .collection('years')
          .doc(year.toString())
          .collection('months')
          .snapshots()
          .map<
              Do<PeriodTrackingFailure,
                  Map<String, Map<String, PeriodTrackingModel>>>>((snapshot) {
        try {
          print(
              '🔥 FirestoreService: Received snapshot with ${snapshot.docs.length} documents');
          final yearData = <String, Map<String, PeriodTrackingModel>>{};

          for (final doc in snapshot.docs) {
            final monthKey = doc.id; // e.g., "2025_01"
            final data = doc.data();
            final daysData = data['days'] as Map<String, dynamic>? ?? {};

            print(
                '🔥 FirestoreService: Processing month $monthKey with ${daysData.keys.length} days');

            final monthData = <String, PeriodTrackingModel>{};
            for (final dayEntry in daysData.entries) {
              final dayKey = dayEntry.key; // e.g., "01"
              final dayData = dayEntry.value as Map<String, dynamic>;

              try {
                final model = PeriodTrackingModel.fromJson(dayData);
                monthData[dayKey] = model;

                // Debug first few models
                if (monthData.length <= 3) {
                  print(
                      '🔥 FirestoreService: Parsed day $dayKey: isPeriod=${model.isPeriodDate}, isOvulation=${model.isOvulationDate}');
                }
              } catch (e) {
                debugPrint('Error parsing day data for $monthKey/$dayKey: $e');
                print(
                    '❌ FirestoreService: Error parsing day data for $monthKey/$dayKey: $e');
                print('❌ Raw day data: $dayData');
                // Continue processing other days
              }
            }

            if (monthData.isNotEmpty) {
              yearData[monthKey] = monthData;
              print(
                  '🔥 FirestoreService: Added month $monthKey with ${monthData.length} days to yearData');
            }
          }

          print(
              '🔥 FirestoreService: Final yearData has ${yearData.keys.length} months');
          return Do.success(yearData);
        } catch (e) {
          debugPrint('Error processing year data: $e');
          print('❌ FirestoreService: Error processing year data: $e');
          return Do.failure(const PeriodTrackingFailure.unexpected());
        }
      });
    } catch (e) {
      debugPrint('Error watching year data: $e');
      print('❌ FirestoreService: Error watching year data: $e');
      return Stream.value(const Do.failure(PeriodTrackingFailure.unexpected()));
    }
  }

  /// Batch update period dates with optional flow levels
  Future<Do<PeriodTrackingFailure, Unit>> batchUpdatePeriodDates({
    required Map<String, Map<String, Set<String>>> monthlyUpdates,
    required bool isPeriodDate,
    Map<DateTime, int>? flowLevels, // Optional flow levels for period dates
  }) async {
    try {
      final user = currentUser;
      if (user == null) {
        return const Do.failure(PeriodTrackingFailure.unauthenticated());
      }

      final batch = _firestore.batch();

      for (final yearEntry in monthlyUpdates.entries) {
        final year = yearEntry.key;

        for (final monthEntry in yearEntry.value.entries) {
          final monthKey = monthEntry.key;
          final dayKeys = monthEntry.value;

          final monthDocRef = _firestore
              .collection('period_tracking')
              .doc(user.uid)
              .collection('years')
              .doc(year)
              .collection('months')
              .doc(monthKey);

          // First, read the existing document to preserve data
          final existingDoc = await monthDocRef.get();
          final existingData = existingDoc.exists
              ? existingDoc.data() as Map<String, dynamic>?
              : null;
          final existingDays =
              existingData?['days'] as Map<String, dynamic>? ?? {};

          // Create the complete days structure
          final updatedDays = Map<String, dynamic>.from(existingDays);

          for (final dayKey in dayKeys) {
            final dayNum = int.parse(dayKey);
            final monthParts = monthKey.split('_');
            final year = int.parse(monthParts[0]);
            final month = int.parse(monthParts[1]);
            final currentDate = DateTime(year, month, dayNum);

            // Get existing day data or create new
            final existingDayData =
                updatedDays[dayKey] as Map<String, dynamic>? ?? {};

            // Check if flow level is provided for this date
            final flowLevel = flowLevels?[currentDate];

            // Update only the necessary fields, preserving others
            final updatedDayData = {
              ...existingDayData,
              'isPeriodDate': isPeriodDate,
              'lastUpdated': FieldValue.serverTimestamp(),
              'date':
                  existingDayData['date'] ?? Timestamp.fromDate(currentDate),
            };

            // Add flow level if provided and this is a period date
            if (flowLevel != null && isPeriodDate) {
              updatedDayData['flowLevel'] = flowLevel;
            }

            updatedDays[dayKey] = updatedDayData;
          }

          // Check if document exists and use appropriate operation
          final docExists = existingDoc.exists;
          if (docExists) {
            batch.update(
              monthDocRef,
              {'days': updatedDays},
            );
          } else {
            batch.set(
              monthDocRef,
              {'days': updatedDays},
              SetOptions(merge: true),
            );
          }
        }
      }

      // Commit the batch
      debugPrint('🔄 About to commit batch...');
      await batch.commit();
      debugPrint('✅ Batch committed successfully');

      debugPrint('🎯 About to return Do.success(unit)');
      return const Do.success(unit);
    } catch (e) {
      debugPrint('Error in batch update period dates: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  /// Batch update ovulation dates
  Future<Do<PeriodTrackingFailure, Unit>> batchUpdateOvulationDates({
    required Set<DateTime> ovulationDates,
    required bool isOvulationDate,
  }) async {
    try {
      final user = currentUser;
      if (user == null) {
        return const Do.failure(PeriodTrackingFailure.unauthenticated());
      }

      final batch = _firestore.batch();
      final monthlyUpdates = <String, Map<String, Set<String>>>{};

      // Group dates by month
      for (final date in ovulationDates) {
        final year = date.year.toString();
        final monthKey =
            '${date.year}_${date.month.toString().padLeft(2, '0')}';
        final dayKey = date.day.toString().padLeft(2, '0');

        monthlyUpdates[year] ??= {};
        monthlyUpdates[year]![monthKey] ??= {};
        monthlyUpdates[year]![monthKey]!.add(dayKey);
      }

      // Update each month document
      for (final yearEntry in monthlyUpdates.entries) {
        final year = yearEntry.key;

        for (final monthEntry in yearEntry.value.entries) {
          final monthKey = monthEntry.key;
          final dayKeys = monthEntry.value;

          final monthDocRef = _firestore
              .collection('period_tracking')
              .doc(user.uid)
              .collection('years')
              .doc(year)
              .collection('months')
              .doc(monthKey);

          // Check if document exists first
          final docSnapshot = await monthDocRef.get();

          // Create update map for ovulation dates
          final Map<String, dynamic> updates = {};
          for (final dayKey in dayKeys) {
            final dayNum = int.parse(dayKey);
            final monthParts = monthKey.split('_');
            final year = int.parse(monthParts[0]);
            final month = int.parse(monthParts[1]);
            final currentDate = DateTime(year, month, dayNum);

            updates['days.$dayKey.isOvulationDate'] = isOvulationDate;
            updates['days.$dayKey.lastUpdated'] = FieldValue.serverTimestamp();
            updates['days.$dayKey.date'] = Timestamp.fromDate(currentDate);
          }

          // Use appropriate operation based on document existence

          batch.update(monthDocRef, updates);
        }
      }

      await batch.commit();
      return const Do.success(unit);
    } catch (e) {
      debugPrint('Error in batch update ovulation dates: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  /// Get existing ovulation dates within a date range
  Future<Set<DateTime>> getExistingOvulationDatesInRange(
      DateTime startDate, DateTime endDate) async {
    final user = currentUser;
    if (user == null) return {};

    try {
      final ovulationDates = <DateTime>{};

      // Get all years that might contain ovulation dates in the range
      final startYear = startDate.year;
      final endYear = endDate.year;

      for (int year = startYear; year <= endYear; year++) {
        final yearDoc = await _firestore
            .collection('period_tracking')
            .doc(user.uid)
            .collection('years')
            .doc(year.toString())
            .collection('months')
            .get();

        for (final monthDoc in yearDoc.docs) {
          final monthData = monthDoc.data();
          final daysData = monthData['days'] as Map<String, dynamic>? ?? {};

          for (final dayEntry in daysData.entries) {
            final dayData = dayEntry.value as Map<String, dynamic>;
            final isOvulationDate =
                dayData['isOvulationDate'] as bool? ?? false;

            if (isOvulationDate) {
              final dateTimestamp = dayData['date'] as Timestamp?;
              if (dateTimestamp != null) {
                final date = dateTimestamp.toDate();
                final normalizedDate =
                    DateTime(date.year, date.month, date.day);

                // Check if date is within range
                if (!normalizedDate.isBefore(startDate) &&
                    !normalizedDate.isAfter(endDate)) {
                  ovulationDates.add(normalizedDate);
                }
              }
            }
          }
        }
      }

      return ovulationDates;
    } catch (e) {
      debugPrint('Error getting existing ovulation dates: $e');
      return {};
    }
  }

  /// Save symptom data for a specific date
  Future<Do<PeriodTrackingFailure, Unit>> saveSymptomData({
    required DateTime date,
    Map<String, dynamic>? symptomData,
  }) async {
    try {
      final user = currentUser;
      if (user == null) {
        return const Do.failure(PeriodTrackingFailure.unauthenticated());
      }

      final year = date.year.toString();
      final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
      final dayKey = date.day.toString().padLeft(2, '0');

      final monthDocRef = _firestore
          .collection('period_tracking')
          .doc(user.uid)
          .collection('years')
          .doc(year)
          .collection('months')
          .doc(monthKey);

      // First, read the existing document to preserve existing data
      final existingDoc = await monthDocRef.get();
      final existingData = existingDoc.exists
          ? existingDoc.data() as Map<String, dynamic>?
          : null;
      final existingDays = existingData?['days'] as Map<String, dynamic>? ?? {};

      // Get existing day data or create new
      final existingDayData =
          existingDays[dayKey] as Map<String, dynamic>? ?? {};

      // Merge symptom data with existing day data, preserving existing fields
      final updatedDayData = {
        ...existingDayData,
        'date': existingDayData['date'] ?? Timestamp.fromDate(date),
        'lastUpdated': FieldValue.serverTimestamp(),
        ...?symptomData,
      };

      // Update the complete days structure
      final updatedDays = Map<String, dynamic>.from(existingDays);
      updatedDays[dayKey] = updatedDayData;

      // Save the complete days structure
      await monthDocRef.set(
        {'days': updatedDays},
        SetOptions(merge: true),
      );
      return const Do.success(unit);
    } catch (e) {
      debugPrint('Error saving symptom data: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  /// Update flow categorization for a specific date
  Future<Do<PeriodTrackingFailure, Unit>> updateFlowCategorization({
    required DateTime date,
    required int flowLevel,
    required bool isPeriodDate,
  }) async {
    try {
      final user = currentUser;
      if (user == null) {
        return const Do.failure(PeriodTrackingFailure.unauthenticated());
      }

      final year = date.year.toString();
      final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
      final dayKey = date.day.toString().padLeft(2, '0');

      final monthDocRef = _firestore
          .collection('period_tracking')
          .doc(user.uid)
          .collection('years')
          .doc(year)
          .collection('months')
          .doc(monthKey);

      await monthDocRef.set({
        'days': {
          dayKey: {
            'date': Timestamp.fromDate(date),
            'flowLevel': flowLevel,
            'isPeriodDate': isPeriodDate,
            'lastUpdated': Timestamp.now(),
          }
        }
      }, SetOptions(merge: true));

      debugPrint(
          '✅ Updated flow categorization for $date: flow=$flowLevel, period=$isPeriodDate');
      return const Do.success(unit);
    } catch (e) {
      debugPrint('❌ Error updating flow categorization: $e');
      return const Do.failure(PeriodTrackingFailure.updateFailure());
    }
  }

  /// Get symptom data for a specific date
  Future<Do<PeriodTrackingFailure, PeriodTrackingModel?>> getSymptomData({
    required DateTime date,
  }) async {
    try {
      final user = currentUser;
      if (user == null) {
        return const Do.failure(PeriodTrackingFailure.unauthenticated());
      }

      final year = date.year.toString();
      final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
      final dayKey = date.day.toString().padLeft(2, '0');

      final monthDoc = await _firestore
          .collection('period_tracking')
          .doc(user.uid)
          .collection('years')
          .doc(year)
          .collection('months')
          .doc(monthKey)
          .get();

      if (!monthDoc.exists) {
        return const Do.success(null);
      }

      final monthData = monthDoc.data()!;
      final daysData = monthData['days'] as Map<String, dynamic>? ?? {};
      final dayData = daysData[dayKey] as Map<String, dynamic>?;

      if (dayData == null) {
        return const Do.success(null);
      }

      final model = PeriodTrackingModel.fromJson(dayData);
      return Do.success(model);
    } catch (e) {
      debugPrint('Error getting symptom data: $e');
      return const Do.failure(PeriodTrackingFailure.unexpected());
    }
  }
}
