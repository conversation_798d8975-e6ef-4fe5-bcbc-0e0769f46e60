part of 'medication_watcher_bloc.dart';

// MedicationWatcherEvent is a freezed union class that will be used to represent the different events that can occur in the MedicationWatcherBloc.
@freezed
class MedicationWatcherEvent with _$MedicationWatcherEvent {
  // Event representing the start of watching all medications
  const factory MedicationWatcherEvent.watchAllStarted() = _WatchAllStarted;
  // Event representing the reception of medications
  const factory MedicationWatcherEvent.medicationsReceived(
          Do<MedicationFailure, List<MedicationModel>> failureOrMedications) =
      _MedicationsReceived;
}
