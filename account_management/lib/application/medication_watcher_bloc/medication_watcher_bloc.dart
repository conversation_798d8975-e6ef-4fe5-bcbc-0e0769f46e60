import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:doso/doso.dart';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../domain/facade/medication_facade.dart';
import '../../domain/failure/medication_failure.dart';
import '../../domain/model/medication_model.dart';

part 'medication_watcher_event.dart';
part 'medication_watcher_state.dart';
part 'medication_watcher_bloc.freezed.dart';

// Represents the Medication Watcher Bloc
@injectable
class MedicationWatcherBloc
    extends Bloc<MedicationWatcherEvent, MedicationWatcherState> {
  final MedicationFacade _medicationFacade;
  StreamSubscription<Do<MedicationFailure, List<MedicationModel>>>?
      _medicationStreamSubscription;
  MedicationWatcherBloc(this._medicationFacade)
      : super(const MedicationWatcherState.initial()) {
    on<_WatchAllStarted>(_onWatchAllStarted);
    on<_MedicationsReceived>(_onMedicationsReceived);
  }
  // Represents the Medication Watcher Event
  Future<void> _onWatchAllStarted(
    _WatchAllStarted event,
    Emitter<MedicationWatcherState> emit,
  ) async {
    emit(const MedicationWatcherState.loading());
    await _medicationStreamSubscription?.cancel();
    _medicationStreamSubscription = _medicationFacade.getMedications().listen(
      (failureOrMedications) {
        add(_MedicationsReceived(failureOrMedications));
      },
    );
  }

  // Represents the Medication Watcher Event
  Future<void> _onMedicationsReceived(
    _MedicationsReceived event,
    Emitter<MedicationWatcherState> emit,
  ) async {
    event.failureOrMedications.fold(
      onFailure: (failure) => emit(MedicationWatcherState.loadFailure(failure)),
      onSuccess: (medications) =>
          emit(MedicationWatcherState.loadSuccess(medications)),
    );
  }

  // Represents the Medication Watcher Bloc cancellation
  @override
  Future<void> close() async {
    await _medicationStreamSubscription?.cancel();
    return super.close();
  }
}
