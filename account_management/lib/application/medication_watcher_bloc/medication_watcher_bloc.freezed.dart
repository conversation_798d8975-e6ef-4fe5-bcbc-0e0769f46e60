// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'medication_watcher_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MedicationWatcherEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is MedicationWatcherEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MedicationWatcherEvent()';
  }
}

/// @nodoc
class $MedicationWatcherEventCopyWith<$Res> {
  $MedicationWatcherEventCopyWith(
      MedicationWatcherEvent _, $Res Function(MedicationWatcherEvent) __);
}

/// Adds pattern-matching-related methods to [MedicationWatcherEvent].
extension MedicationWatcherEventPatterns on MedicationWatcherEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_MedicationsReceived value)? medicationsReceived,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted(_that);
      case _MedicationsReceived() when medicationsReceived != null:
        return medicationsReceived(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_MedicationsReceived value) medicationsReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted():
        return watchAllStarted(_that);
      case _MedicationsReceived():
        return medicationsReceived(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_MedicationsReceived value)? medicationsReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted(_that);
      case _MedicationsReceived() when medicationsReceived != null:
        return medicationsReceived(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(
            Do<MedicationFailure, List<MedicationModel>> failureOrMedications)?
        medicationsReceived,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted();
      case _MedicationsReceived() when medicationsReceived != null:
        return medicationsReceived(_that.failureOrMedications);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(
            Do<MedicationFailure, List<MedicationModel>> failureOrMedications)
        medicationsReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted():
        return watchAllStarted();
      case _MedicationsReceived():
        return medicationsReceived(_that.failureOrMedications);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(
            Do<MedicationFailure, List<MedicationModel>> failureOrMedications)?
        medicationsReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted();
      case _MedicationsReceived() when medicationsReceived != null:
        return medicationsReceived(_that.failureOrMedications);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _WatchAllStarted implements MedicationWatcherEvent {
  const _WatchAllStarted();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _WatchAllStarted);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MedicationWatcherEvent.watchAllStarted()';
  }
}

/// @nodoc

class _MedicationsReceived implements MedicationWatcherEvent {
  const _MedicationsReceived(this.failureOrMedications);

  final Do<MedicationFailure, List<MedicationModel>> failureOrMedications;

  /// Create a copy of MedicationWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MedicationsReceivedCopyWith<_MedicationsReceived> get copyWith =>
      __$MedicationsReceivedCopyWithImpl<_MedicationsReceived>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MedicationsReceived &&
            (identical(other.failureOrMedications, failureOrMedications) ||
                other.failureOrMedications == failureOrMedications));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrMedications);

  @override
  String toString() {
    return 'MedicationWatcherEvent.medicationsReceived(failureOrMedications: $failureOrMedications)';
  }
}

/// @nodoc
abstract mixin class _$MedicationsReceivedCopyWith<$Res>
    implements $MedicationWatcherEventCopyWith<$Res> {
  factory _$MedicationsReceivedCopyWith(_MedicationsReceived value,
          $Res Function(_MedicationsReceived) _then) =
      __$MedicationsReceivedCopyWithImpl;
  @useResult
  $Res call(
      {Do<MedicationFailure, List<MedicationModel>> failureOrMedications});
}

/// @nodoc
class __$MedicationsReceivedCopyWithImpl<$Res>
    implements _$MedicationsReceivedCopyWith<$Res> {
  __$MedicationsReceivedCopyWithImpl(this._self, this._then);

  final _MedicationsReceived _self;
  final $Res Function(_MedicationsReceived) _then;

  /// Create a copy of MedicationWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureOrMedications = null,
  }) {
    return _then(_MedicationsReceived(
      null == failureOrMedications
          ? _self.failureOrMedications
          : failureOrMedications // ignore: cast_nullable_to_non_nullable
              as Do<MedicationFailure, List<MedicationModel>>,
    ));
  }
}

/// @nodoc
mixin _$MedicationWatcherState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is MedicationWatcherState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MedicationWatcherState()';
  }
}

/// @nodoc
class $MedicationWatcherStateCopyWith<$Res> {
  $MedicationWatcherStateCopyWith(
      MedicationWatcherState _, $Res Function(MedicationWatcherState) __);
}

/// Adds pattern-matching-related methods to [MedicationWatcherState].
extension MedicationWatcherStatePatterns on MedicationWatcherState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Loading value)? loading,
    TResult Function(Success value)? loadSuccess,
    TResult Function(LoadFailure value)? loadFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial(_that);
      case Loading() when loading != null:
        return loading(_that);
      case Success() when loadSuccess != null:
        return loadSuccess(_that);
      case LoadFailure() when loadFailure != null:
        return loadFailure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Loading value) loading,
    required TResult Function(Success value) loadSuccess,
    required TResult Function(LoadFailure value) loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial():
        return initial(_that);
      case Loading():
        return loading(_that);
      case Success():
        return loadSuccess(_that);
      case LoadFailure():
        return loadFailure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Loading value)? loading,
    TResult? Function(Success value)? loadSuccess,
    TResult? Function(LoadFailure value)? loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial(_that);
      case Loading() when loading != null:
        return loading(_that);
      case Success() when loadSuccess != null:
        return loadSuccess(_that);
      case LoadFailure() when loadFailure != null:
        return loadFailure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(List<MedicationModel> medications)? loadSuccess,
    TResult Function(MedicationFailure failure)? loadFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial();
      case Loading() when loading != null:
        return loading();
      case Success() when loadSuccess != null:
        return loadSuccess(_that.medications);
      case LoadFailure() when loadFailure != null:
        return loadFailure(_that.failure);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(List<MedicationModel> medications) loadSuccess,
    required TResult Function(MedicationFailure failure) loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial():
        return initial();
      case Loading():
        return loading();
      case Success():
        return loadSuccess(_that.medications);
      case LoadFailure():
        return loadFailure(_that.failure);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(List<MedicationModel> medications)? loadSuccess,
    TResult? Function(MedicationFailure failure)? loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial();
      case Loading() when loading != null:
        return loading();
      case Success() when loadSuccess != null:
        return loadSuccess(_that.medications);
      case LoadFailure() when loadFailure != null:
        return loadFailure(_that.failure);
      case _:
        return null;
    }
  }
}

/// @nodoc

class Initial implements MedicationWatcherState {
  const Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MedicationWatcherState.initial()';
  }
}

/// @nodoc

class Loading implements MedicationWatcherState {
  const Loading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Loading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MedicationWatcherState.loading()';
  }
}

/// @nodoc

class Success implements MedicationWatcherState {
  const Success(final List<MedicationModel> medications)
      : _medications = medications;

  final List<MedicationModel> _medications;
  List<MedicationModel> get medications {
    if (_medications is EqualUnmodifiableListView) return _medications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_medications);
  }

  /// Create a copy of MedicationWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SuccessCopyWith<Success> get copyWith =>
      _$SuccessCopyWithImpl<Success>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Success &&
            const DeepCollectionEquality()
                .equals(other._medications, _medications));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_medications));

  @override
  String toString() {
    return 'MedicationWatcherState.loadSuccess(medications: $medications)';
  }
}

/// @nodoc
abstract mixin class $SuccessCopyWith<$Res>
    implements $MedicationWatcherStateCopyWith<$Res> {
  factory $SuccessCopyWith(Success value, $Res Function(Success) _then) =
      _$SuccessCopyWithImpl;
  @useResult
  $Res call({List<MedicationModel> medications});
}

/// @nodoc
class _$SuccessCopyWithImpl<$Res> implements $SuccessCopyWith<$Res> {
  _$SuccessCopyWithImpl(this._self, this._then);

  final Success _self;
  final $Res Function(Success) _then;

  /// Create a copy of MedicationWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? medications = null,
  }) {
    return _then(Success(
      null == medications
          ? _self._medications
          : medications // ignore: cast_nullable_to_non_nullable
              as List<MedicationModel>,
    ));
  }
}

/// @nodoc

class LoadFailure implements MedicationWatcherState {
  const LoadFailure(this.failure);

  final MedicationFailure failure;

  /// Create a copy of MedicationWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LoadFailureCopyWith<LoadFailure> get copyWith =>
      _$LoadFailureCopyWithImpl<LoadFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LoadFailure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'MedicationWatcherState.loadFailure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class $LoadFailureCopyWith<$Res>
    implements $MedicationWatcherStateCopyWith<$Res> {
  factory $LoadFailureCopyWith(
          LoadFailure value, $Res Function(LoadFailure) _then) =
      _$LoadFailureCopyWithImpl;
  @useResult
  $Res call({MedicationFailure failure});

  $MedicationFailureCopyWith<$Res> get failure;
}

/// @nodoc
class _$LoadFailureCopyWithImpl<$Res> implements $LoadFailureCopyWith<$Res> {
  _$LoadFailureCopyWithImpl(this._self, this._then);

  final LoadFailure _self;
  final $Res Function(LoadFailure) _then;

  /// Create a copy of MedicationWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(LoadFailure(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as MedicationFailure,
    ));
  }

  /// Create a copy of MedicationWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MedicationFailureCopyWith<$Res> get failure {
    return $MedicationFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

// dart format on
