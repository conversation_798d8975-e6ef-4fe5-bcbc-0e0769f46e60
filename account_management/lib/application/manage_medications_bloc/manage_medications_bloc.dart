import 'package:account_management/domain/model/medication_model.dart';
import 'package:bloc/bloc.dart';
import 'package:doso/doso.dart';
import 'package:meta/meta.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:account_management/domain/facade/medication_facade.dart';
import '../../domain/failure/medication_failure.dart';

part 'manage_medications_event.dart';
part 'manage_medications_state.dart';
part 'manage_medications_bloc.freezed.dart';

// This class manages the medication-related operations such as adding, updating, and deleting medications.
@injectable
class ManageMedicationsBloc
    extends Bloc<ManageMedicationsEvent, ManageMedicationsState> {
  final MedicationFacade _medicationFacade;
  ManageMedicationsBloc(this._medicationFacade)
      : super(const ManageMedicationsState.initial()) {
    on<_DeleteMedication>(_onDeleteMedication);
  }
  // Handles the event of deleting a medication.
  Future<void> _onDeleteMedication(
    _DeleteMedication event,
    Emitter<ManageMedicationsState> emit,
  ) async {
    emit(const ManageMedicationsState.actionInProgress());
    final failureOrSuccess = await _medicationFacade.delete(event.medication);
    failureOrSuccess.fold(
      onFailure: (failure) =>
          emit(ManageMedicationsState.medicationFailure(failure)),
      onSuccess: (_) => emit(const ManageMedicationsState.medicationDeleted()),
    );
  }
}
