import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import '../../domain/facade/period_reminder_facade.dart';
import '../../domain/facade/period_tracking_facade.dart';
import '../../domain/model/period_reminder_settings.dart';

part 'period_reminder_settings_event.dart';
part 'period_reminder_settings_state.dart';
part 'period_reminder_settings_bloc.freezed.dart';

@injectable
class PeriodReminderSettingsBloc
    extends Bloc<PeriodReminderSettingsEvent, PeriodReminderSettingsState> {
  final PeriodReminderFacade _PeriodReminderFacade;
  PeriodReminderSettings _currentSettings = PeriodReminderSettings.empty();

  PeriodReminderSettingsBloc(this._PeriodReminderFacade)
      : super(const PeriodReminderSettingsState.initial()) {
    on<_LoadSettings>(_onLoadSettings);
    on<_TogglePeriodReminder>(_onTogglePeriodReminder);
    on<_UpdatePeriodReminderDays>(_onUpdatePeriodReminderDays);
    on<_ToggleOvulationReminder>(_onToggleOvulationReminder);
    on<_UpdateOvulationReminderDays>(_onUpdateOvulationReminderDays);
    on<_SaveSettings>(_onSaveSettings);
  }

  Future<void> _onLoadSettings(
    _LoadSettings event,
    Emitter<PeriodReminderSettingsState> emit,
  ) async {
    emit(const PeriodReminderSettingsState.loading());

    try {
      // Load settings from repository
      final result = await _PeriodReminderFacade.getReminderSettings();
      result.fold(
        onFailure: (failure) => emit(PeriodReminderSettingsState.failure(
          message: failure.toString(),
        )),
        onSuccess: (settings) {
          _currentSettings = settings;
          emit(PeriodReminderSettingsState.loaded(settings: settings));
        },
      );
    } catch (e) {
      emit(PeriodReminderSettingsState.failure(message: e.toString()));
    }
  }

  void _onTogglePeriodReminder(
    _TogglePeriodReminder event,
    Emitter<PeriodReminderSettingsState> emit,
  ) {
    _currentSettings = _currentSettings.copyWith(
      isPeriodReminderEnabled: event.enabled,
    );
    emit(PeriodReminderSettingsState.loaded(settings: _currentSettings));
  }

  void _onUpdatePeriodReminderDays(
    _UpdatePeriodReminderDays event,
    Emitter<PeriodReminderSettingsState> emit,
  ) {
    if (event.daysBefore >= 1 && event.daysBefore <= 5) {
      _currentSettings = _currentSettings.copyWith(
        periodReminderDaysBefore: event.daysBefore,
      );
      emit(PeriodReminderSettingsState.loaded(settings: _currentSettings));
    }
  }

  void _onToggleOvulationReminder(
    _ToggleOvulationReminder event,
    Emitter<PeriodReminderSettingsState> emit,
  ) {
    _currentSettings = _currentSettings.copyWith(
      isOvulationReminderEnabled: event.enabled,
    );
    emit(PeriodReminderSettingsState.loaded(settings: _currentSettings));
  }

  void _onUpdateOvulationReminderDays(
    _UpdateOvulationReminderDays event,
    Emitter<PeriodReminderSettingsState> emit,
  ) {
    if (event.daysBefore >= 1 && event.daysBefore <= 5) {
      _currentSettings = _currentSettings.copyWith(
        ovulationReminderDaysBefore: event.daysBefore,
      );
      emit(PeriodReminderSettingsState.loaded(settings: _currentSettings));
    }
  }

  Future<void> _onSaveSettings(
    _SaveSettings event,
    Emitter<PeriodReminderSettingsState> emit,
  ) async {
    emit(const PeriodReminderSettingsState.saving());

    try {
      final result =
          await _PeriodReminderFacade.saveReminderSettings(_currentSettings);
      result.fold(
        onFailure: (failure) => emit(PeriodReminderSettingsState.failure(
          message: failure.toString(),
        )),
        onSuccess: (_) =>
            emit(PeriodReminderSettingsState.saved(settings: _currentSettings)),
      );
    } catch (e) {
      emit(PeriodReminderSettingsState.failure(message: e.toString()));
    }
  }
}
