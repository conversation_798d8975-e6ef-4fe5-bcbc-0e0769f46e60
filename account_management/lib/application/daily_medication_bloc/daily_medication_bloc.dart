import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:doso/doso.dart';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../domain/facade/medication_facade.dart';
import '../../domain/failure/medication_failure.dart';
import '../../domain/model/daily_medication_model.dart';

part 'daily_medication_event.dart';
part 'daily_medication_state.dart';
part 'daily_medication_bloc.freezed.dart';

@injectable
class DailyMedicationBloc
    extends Bloc<DailyMedicationEvent, DailyMedicationState> {
  final MedicationFacade _medicationFacade;

  StreamSubscription<Do<MedicationFailure, List<DailyMedicationModel>>>?
      _medStreamSub;

  // Constructor: Injecting the MedicationFacade to get daily medications
  DailyMedicationBloc(this._medicationFacade)
      : super(const DailyMedicationState.initial()) {
    // Handling the event when fetching daily medications starts
    on<_WatchStarted>(_onWatchStarted);
    // Handling the event when daily medications are received
    on<_DailyMedicationsReceived>(_onDailyMedicationsReceived);
  }

  // Event handler for starting the fetching process
  Future<void> _onWatchStarted(
      _WatchStarted event, Emitter<DailyMedicationState> emit) async {
    emit(const DailyMedicationState.loadInProgress());

    // Cancel the existing subscription if there's one
    await _medStreamSub?.cancel();

    // Listen to the stream of daily medications
    _medStreamSub = _medicationFacade.getDailyMedications().listen(
      (either) {
        // Trigger event to process the result (success or failure)
        add(DailyMedicationEvent.dailyMedicationsReceived(either));
      },
    );
  }

  // Event handler for processing the result of daily medications stream
  void _onDailyMedicationsReceived(
      _DailyMedicationsReceived event, Emitter<DailyMedicationState> emit) {
    event.failureOrMeds.fold(
      // Handling failure case
      onFailure: (failure) => emit(DailyMedicationState.loadFailure(failure)),
      // Handling success case
      onSuccess: (medications) =>
          emit(DailyMedicationState.loadSuccess(medications)),
    );
  }

  // Close the stream subscription to prevent memory leaks when BLoC is disposed
  @override
  Future<void> close() async {
    await _medStreamSub?.cancel();
    return super.close();
  }
}
