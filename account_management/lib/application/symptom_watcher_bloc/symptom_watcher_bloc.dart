import 'dart:async';
import 'package:doso/doso.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../domain/facade/symptom_management_facade.dart';
import '../../domain/failure/symptom_management_failure.dart';
import '../../domain/model/symptom_model.dart';

part 'symptom_watcher_event.dart';
part 'symptom_watcher_state.dart';
part 'symptom_watcher_bloc.freezed.dart';

@injectable
class SymptomWatcherBloc
    extends Bloc<SymptomWatcherEvent, SymptomWatcherState> {
  final SymptomManagementFacade _symptomManagementFacade;
  StreamSubscription<dynamic>? _symptomsSubscription;

  SymptomWatcherBloc(this._symptomManagementFacade)
      : super(const SymptomWatcherState.initial()) {
    on<SymptomWatcherEvent>((event, emit) async {
      await event.when(
        watchStarted: () => _onWatchStarted(emit),
        symptomsReceived: (symptomsByCategory) =>
            _onSymptomsReceived(symptomsByCategory, emit),
        syncRequested: () => _onSyncRequested(emit),
        clearCache: () => _onClearCache(emit),
      );
    });
  }

  Future<void> _onWatchStarted(Emitter<SymptomWatcherState> emit) async {
    emit(const SymptomWatcherState.loading());

    // Check if we should sync with remote first
    final shouldSync = await _symptomManagementFacade.shouldSyncWithRemote();
    if (shouldSync) {
      // Sync with remote data
      final syncResult =
          await _symptomManagementFacade.syncSymptomsFromRemote();

      // Handle sync result
      syncResult.fold(
        onFailure: (failure) {
          // If sync fails, try to load from local cache (but don't await here)
          _loadFromLocalCache(emit);
        },
        onSuccess: (_) {
          // Sync successful, start watching remote data
          _startWatchingRemoteData(emit);
        },
      );
    } else {
      // Load from local cache first, then start watching
      await _loadFromLocalCache(emit);
      _startWatchingRemoteData(emit);
    }
  }

  Future<void> _loadFromLocalCache(Emitter<SymptomWatcherState> emit) async {
    final localResult = await _symptomManagementFacade.getLocalSymptoms();
    localResult.fold(
      onFailure: (failure) =>
          emit(SymptomWatcherState.loadFailure(failure: failure)),
      onSuccess: (symptoms) {
        if (symptoms.isNotEmpty) {
          emit(SymptomWatcherState.loadSuccess(symptomsByCategory: symptoms));
        }
      },
    );
  }

  void _startWatchingRemoteData(Emitter<SymptomWatcherState> emit) {
    _symptomsSubscription?.cancel();
    _symptomsSubscription =
        _symptomManagementFacade.watchSymptomsByCategory().listen((result) {
      result.fold(
        onFailure: (failure) => add(const SymptomWatcherEvent.symptomsReceived(
          symptomsByCategory: {},
        )),
        onSuccess: (symptoms) => add(SymptomWatcherEvent.symptomsReceived(
          symptomsByCategory: symptoms,
        )),
      );
    });
  }

  Future<void> _onSymptomsReceived(
    Map<String, List<SymptomModel>> symptomsByCategory,
    Emitter<SymptomWatcherState> emit,
  ) async {
    if (symptomsByCategory.isEmpty) {
      // Try to load from local cache if remote data is empty
      final localResult = await _symptomManagementFacade.getLocalSymptoms();
      localResult.fold(
        onFailure: (failure) =>
            emit(SymptomWatcherState.loadFailure(failure: failure)),
        onSuccess: (symptoms) =>
            emit(SymptomWatcherState.loadSuccess(symptomsByCategory: symptoms)),
      );
    } else {
      emit(SymptomWatcherState.loadSuccess(
          symptomsByCategory: symptomsByCategory));
    }
  }

  Future<void> _onSyncRequested(Emitter<SymptomWatcherState> emit) async {
    emit(const SymptomWatcherState.loading());

    final syncResult = await _symptomManagementFacade.syncSymptomsFromRemote();
    syncResult.fold(
      onFailure: (failure) =>
          emit(SymptomWatcherState.loadFailure(failure: failure)),
      onSuccess: (_) {
        // After successful sync, the stream will automatically emit new data
        // So we don't need to emit here, just wait for the stream
      },
    );
  }

  Future<void> _onClearCache(Emitter<SymptomWatcherState> emit) async {
    final clearResult = await _symptomManagementFacade.clearLocalCache();
    clearResult.fold(
      onFailure: (failure) =>
          emit(SymptomWatcherState.loadFailure(failure: failure)),
      onSuccess: (_) {
        // After clearing cache, restart watching
        add(const SymptomWatcherEvent.watchStarted());
      },
    );
  }

  @override
  Future<void> close() {
    _symptomsSubscription?.cancel();
    return super.close();
  }
}
