import 'package:account_management/domain/model/medication_model.dart';
import 'package:bloc/bloc.dart';
import 'package:doso/doso.dart';
import 'package:flutter/cupertino.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../domain/core/unit.dart';
import '../../domain/facade/medication_facade.dart';
import '../../domain/failure/medication_failure.dart';

part 'medication_form_event.dart';
part 'medication_form_state.dart';
part 'medication_form_bloc.freezed.dart';

@injectable
class MedicationFormBloc
    extends Bloc<MedicationFormEvent, MedicationFormState> {
  final MedicationFacade _medicationFacade;

  MedicationFormBloc(this._medicationFacade)
      : super(MedicationFormState.initial()) {
    on<MedicationFormEvent>((event, emit) => event.map(
          initialized: (e) => _onInitialized(e, emit),
          nameChanged: (e) => _onNameChanged(e, emit),
          dosageChanged: (e) => _onDosageChanged(e, emit),
          frequencyChanged: (e) => _onFrequencyChanged(e, emit),
          frequencyUnitChanged: (e) => _onFrequencyUnitChanged(e, emit),
          dosageUnitChanged: (e) => _onDosageUnitChanged(e, emit),
          daystoBeNotifiedChanged: (e) => _onDaystoBeNotifiedChanged(e, emit),
          timeofDayChanged: (e) => _onTimeofDayChanged(e, emit),
          monthlyDateToBeNotifiedChanged: (e) =>
              _onMonthlyDateToBeNotifiedChanged(e, emit),
          isNotificationEnabledChanged: (e) =>
              _onIsNotificationEnabledChanged(e, emit),
          notesChanged: (e) => _onNotesChanged(e, emit),
          startDateChanged: (e) => _onStartDateChanged(e, emit),
          save: (e) => _onSave(e, emit),
        ));
  }

  void _onInitialized(_Initialized event, Emitter<MedicationFormState> emit) {
    final initialMedication = event.initialMedicationOption;
    if (initialMedication != null) {
      debugPrint('initialMedication: ${initialMedication.toJson()}');
      emit(state.copyWith(
        medication: initialMedication,
        isEditing: true,
      ));
    } else {
      emit(state.copyWith(
        medication: MedicationModel.empty(),
        isEditing: false,
      ));
    }
  }

  void _onNameChanged(_NameChanged event, Emitter<MedicationFormState> emit) {
    emit(state.copyWith(
      medication: state.medication.copyWith(name: event.nameStr),
    ));
  }

  void _onDosageChanged(
      _DosageChanged event, Emitter<MedicationFormState> emit) {
    emit(state.copyWith(
      medication: state.medication.copyWith(dosage: event.dosageStr),
    ));
  }

  void _onFrequencyChanged(
      _FrequencyChanged event, Emitter<MedicationFormState> emit) {
    emit(state.copyWith(
      medication: state.medication.copyWith(frequency: event.frequencyStr),
    ));
  }

  void _onFrequencyUnitChanged(
      _FrequencyUnitChanged event, Emitter<MedicationFormState> emit) {
    emit(state.copyWith(
      medication:
          state.medication.copyWith(frequencyUnit: event.frequencyUnitStr),
    ));
  }

  void _onDosageUnitChanged(
      _DosageUnitChanged event, Emitter<MedicationFormState> emit) {
    emit(state.copyWith(
      medication: state.medication.copyWith(dosageUnit: event.dosageUnitStr),
    ));
  }

  void _onDaystoBeNotifiedChanged(
      _DaystoBeNotifiedChanged event, Emitter<MedicationFormState> emit) {
    emit(state.copyWith(
      medication:
          state.medication.copyWith(daystoBeNotified: event.daystoBeNotified),
    ));
  }

  void _onTimeofDayChanged(
      _TimeofDayChanged event, Emitter<MedicationFormState> emit) {
    emit(state.copyWith(
      medication: state.medication.copyWith(timeofDay: event.timeofDay),
    ));
  }

  void _onMonthlyDateToBeNotifiedChanged(_MonthlyDateToBeNotifiedChanged event,
      Emitter<MedicationFormState> emit) {
    emit(state.copyWith(
      medication: state.medication
          .copyWith(monthlyDateToBeNotified: event.monthlyDateToBeNotified),
    ));
  }

  void _onIsNotificationEnabledChanged(
      _IsNotificationEnabledChanged event, Emitter<MedicationFormState> emit) {
    emit(state.copyWith(
      medication: state.medication
          .copyWith(isNotificationEnabled: event.isNotificationEnabled),
    ));
  }

  void _onNotesChanged(_NotesChanged event, Emitter<MedicationFormState> emit) {
    emit(state.copyWith(
      medication: state.medication.copyWith(notes: event.notesStr),
    ));
  }

  void _onStartDateChanged(
      _StartDateChanged event, Emitter<MedicationFormState> emit) {
    emit(state.copyWith(
      medication: state.medication.copyWith(startDate: event.startDate),
    ));
  }

  Future<void> _onSave(_Save event, Emitter<MedicationFormState> emit) async {
    emit(state.copyWith(
      isSaving: true,
      saveFailureOrSuccessOption: null,
    ));

    final failureOrSuccess = state.isEditing
        ? await _medicationFacade.update(state.medication)
        : await _medicationFacade.create(state.medication);

    failureOrSuccess.fold(
      onFailure: (failure) => emit(state.copyWith(
        isSaving: false,
        saveFailureOrSuccessOption: failureOrSuccess,
        showErrorMessages: true,
      )),
      onSuccess: (_) => emit(state.copyWith(
        isSaving: false,
        saveFailureOrSuccessOption: failureOrSuccess,
        showErrorMessages: false,
      )),
    );
  }
}
