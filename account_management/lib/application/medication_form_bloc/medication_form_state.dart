part of 'medication_form_bloc.dart';

class MedicationFormState {
  final MedicationModel medication;
  final bool showErrorMessages;
  final bool isEditing;
  final bool isSaving;
  final Do<MedicationFailure, Unit>? saveFailureOrSuccessOption;

  const MedicationFormState({
    required this.medication,
    required this.showErrorMessages,
    required this.isEditing,
    required this.isSaving,
    required this.saveFailureOrSuccessOption,
  });

  factory MedicationFormState.initial() => MedicationFormState(
        medication: MedicationModel.empty(),
        showErrorMessages: false,
        isEditing: false,
        isSaving: false,
        saveFailureOrSuccessOption: null,
      );

  MedicationFormState copyWith({
    MedicationModel? medication,
    bool? showErrorMessages,
    bool? isEditing,
    bool? isSaving,
    Do<MedicationFailure, Unit>? saveFailureOrSuccessOption,
  }) {
    return MedicationFormState(
      medication: medication ?? this.medication,
      showErrorMessages: showErrorMessages ?? this.showErrorMessages,
      isEditing: isEditing ?? this.isEditing,
      isSaving: isSaving ?? this.isSaving,
      saveFailureOrSuccessOption:
          saveFailureOrSuccessOption ?? this.saveFailureOrSuccessOption,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MedicationFormState &&
          runtimeType == other.runtimeType &&
          medication == other.medication &&
          showErrorMessages == other.showErrorMessages &&
          isEditing == other.isEditing &&
          isSaving == other.isSaving &&
          saveFailureOrSuccessOption == other.saveFailureOrSuccessOption;

  @override
  int get hashCode =>
      medication.hashCode ^
      showErrorMessages.hashCode ^
      isEditing.hashCode ^
      isSaving.hashCode ^
      saveFailureOrSuccessOption.hashCode;
}
