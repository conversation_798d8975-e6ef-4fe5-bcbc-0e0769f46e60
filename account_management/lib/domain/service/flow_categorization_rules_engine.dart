import 'package:flutter/foundation.dart';
import '../model/period_tracking_model.dart';

/// Categories for days based on flow and period status
enum DayCategory {
  flowDay, // Has flow level but not considered a period day
  periodFlowDay, // Has flow level and is considered a period day
}

/// Updates that may need to be applied to previous days
enum PreviousDayUpdate {
  toPeriodFlowDay, // Re-categorize previous day to a "period flow day"
  toPeriodDay, // Re-categorize previous day as "just a period day"
}

/// Context data gathered for rule evaluation
class FlowCategorizationContext {
  final DateTime currentDate;
  final DateTime? mostRecentPeriodDate;
  final int dayCount;
  final DateTime previousDay;
  final DateTime dayBeforePrevious;
  final PeriodTrackingModel? previousDayData;
  final PeriodTrackingModel? dayBeforePreviousData;

  FlowCategorizationContext({
    required this.currentDate,
    this.mostRecentPeriodDate,
    required this.dayCount,
    required this.previousDay,
    required this.dayBeforePrevious,
    this.previousDayData,
    this.dayBeforePreviousData,
  });

  @override
  String toString() {
    return 'FlowCategorizationContext('
        'currentDate: $currentDate, '
        'mostRecentPeriodDate: $mostRecentPeriodDate, '
        'dayCount: $dayCount, '
        'previousDay: $previousDay, '
        'dayBeforePrevious: $dayBeforePrevious, '
        'previousDayData: ${previousDayData != null ? "exists" : "null"}, '
        'dayBeforePreviousData: ${dayBeforePreviousData != null ? "exists" : "null"}'
        ')';
  }
}

/// Result of a single rule evaluation
class RuleResult {
  final String ruleId;
  final String ruleName;
  final String description;
  final String condition;
  final bool conditionMet;
  final Map<String, String> contextValues;
  final dynamic outcome;
  final String explanation;

  RuleResult({
    required this.ruleId,
    required this.ruleName,
    required this.description,
    required this.condition,
    required this.conditionMet,
    required this.contextValues,
    required this.outcome,
    required this.explanation,
  });

  @override
  String toString() {
    return 'RuleResult('
        'ruleId: $ruleId, '
        'ruleName: $ruleName, '
        'conditionMet: $conditionMet, '
        'outcome: $outcome, '
        'explanation: $explanation'
        ')';
  }
}

/// Complete result of flow categorization with all rule details
class FlowCategorizationRuleResult {
  final DateTime currentDate;
  final int flowLevel;
  final FlowCategorizationContext context;
  final List<RuleResult> ruleResults;
  final DayCategory finalCurrentDayCategory;
  final PreviousDayUpdate? finalPreviousDayUpdate;

  FlowCategorizationRuleResult({
    required this.currentDate,
    required this.flowLevel,
    required this.context,
    required this.ruleResults,
    required this.finalCurrentDayCategory,
    this.finalPreviousDayUpdate,
  });

  /// Get a summary of all rules that were applied
  String getRulesSummary() {
    final buffer = StringBuffer();
    buffer.writeln('Flow Categorization Rules Summary for $currentDate:');
    buffer.writeln('Flow Level: $flowLevel');
    buffer.writeln('Context: $context');
    buffer.writeln('');

    for (int i = 0; i < ruleResults.length; i++) {
      final rule = ruleResults[i];
      buffer.writeln('${i + 1}. ${rule.ruleName} (${rule.ruleId})');
      buffer.writeln('   Condition: ${rule.condition}');
      buffer.writeln('   Met: ${rule.conditionMet}');
      buffer.writeln('   Outcome: ${rule.outcome}');
      buffer.writeln('   Explanation: ${rule.explanation}');
      if (rule.contextValues.isNotEmpty) {
        buffer.writeln('   Context Values: ${rule.contextValues}');
      }
      buffer.writeln('');
    }

    buffer.writeln('Final Results:');
    buffer.writeln('Current Day Category: $finalCurrentDayCategory');
    buffer.writeln('Previous Day Update: ${finalPreviousDayUpdate ?? "None"}');

    return buffer.toString();
  }

  /// Get only the rules that were actually applied (condition met = true)
  List<RuleResult> getAppliedRules() {
    return ruleResults.where((rule) => rule.conditionMet).toList();
  }

  /// Get rules that failed their conditions
  List<RuleResult> getFailedRules() {
    return ruleResults.where((rule) => !rule.conditionMet).toList();
  }

  @override
  String toString() {
    return 'FlowCategorizationRuleResult('
        'currentDate: $currentDate, '
        'flowLevel: $flowLevel, '
        'finalCurrentDayCategory: $finalCurrentDayCategory, '
        'finalPreviousDayUpdate: $finalPreviousDayUpdate, '
        'rulesApplied: ${getAppliedRules().length}/${ruleResults.length}'
        ')';
  }
}

/// Comprehensive rules engine for flow categorization
/// Acts as a facade above all flow categorization calculations
/// Makes rules transparent, testable, and easy to verify
class FlowCategorizationRulesEngine {
  /// Main entry point for flow categorization
  /// Returns a detailed result with rule explanations
  static FlowCategorizationRuleResult categorizeFlowDay({
    required DateTime currentDate,
    required int flowLevel,
    required Map<String, Map<String, PeriodTrackingModel>> existingData,
  }) {
    debugPrint(
        '🔧 FlowCategorizationRulesEngine: Starting categorization for $currentDate');

    // Step 1: Gather context data
    final context = _gatherContext(currentDate, existingData);

    // Step 2: Apply rules in order
    final ruleResults = <RuleResult>[];

    // Rule Set 1: Determine current day category
    final currentDayRules = _applyCurrentDayRules(context);
    ruleResults.addAll(currentDayRules);

    // Rule Set 2: Determine previous day updates
    final previousDayRules =
        _applyPreviousDayRules(context, currentDayRules.last.outcome);
    ruleResults.addAll(previousDayRules);

    // Step 3: Compile final result
    final finalResult = FlowCategorizationRuleResult(
      currentDate: currentDate,
      flowLevel: flowLevel,
      context: context,
      ruleResults: ruleResults,
      finalCurrentDayCategory: currentDayRules.last.outcome as DayCategory,
      finalPreviousDayUpdate: previousDayRules.isNotEmpty
          ? previousDayRules.last.outcome as PreviousDayUpdate?
          : null,
    );

    debugPrint(
        '🔧 FlowCategorizationRulesEngine: Final result - ${finalResult.finalCurrentDayCategory}');
    return finalResult;
  }

  /// Gather all context data needed for rule evaluation
  static FlowCategorizationContext _gatherContext(
    DateTime currentDate,
    Map<String, Map<String, PeriodTrackingModel>> existingData,
  ) {
    // Find most recent period date
    DateTime? mostRecentPeriodDate;
    for (final monthData in existingData.values) {
      for (final dayData in monthData.values) {
        if (dayData.isPeriodDate == true &&
            dayData.date != null &&
            dayData.date!.isBefore(currentDate)) {
          if (mostRecentPeriodDate == null ||
              dayData.date!.isAfter(mostRecentPeriodDate)) {
            mostRecentPeriodDate = dayData.date;
          }
        }
      }
    }

    // Calculate day count
    final dayCount = mostRecentPeriodDate != null
        ? currentDate.difference(mostRecentPeriodDate).inDays
        : 999;

    // Get previous day data
    final previousDay = currentDate.subtract(const Duration(days: 1));
    final dayBeforePrevious = currentDate.subtract(const Duration(days: 2));

    final previousDayData = _getDataForDate(existingData, previousDay);
    final dayBeforePreviousData =
        _getDataForDate(existingData, dayBeforePrevious);

    return FlowCategorizationContext(
      currentDate: currentDate,
      mostRecentPeriodDate: mostRecentPeriodDate,
      dayCount: dayCount,
      previousDay: previousDay,
      dayBeforePrevious: dayBeforePrevious,
      previousDayData: previousDayData,
      dayBeforePreviousData: dayBeforePreviousData,
    );
  }

  /// Apply rules to determine current day category
  static List<RuleResult> _applyCurrentDayRules(
      FlowCategorizationContext context) {
    final results = <RuleResult>[];

    // Rule 1: Check if within 9 days of previous period
    final withinNineDays = context.dayCount <= 9;
    results.add(RuleResult(
      ruleId: 'R1_PERIOD_PROXIMITY',
      ruleName: 'Check Period Proximity',
      description:
          'Determine if current day is within 9 days of most recent period',
      condition: 'dayCount <= 9',
      conditionMet: withinNineDays,
      contextValues: {
        'dayCount': context.dayCount.toString(),
        'mostRecentPeriodDate':
            context.mostRecentPeriodDate?.toString() ?? 'none',
      },
      outcome: withinNineDays,
      explanation: withinNineDays
          ? 'Current day is within 9 days of previous period (${context.dayCount} days)'
          : 'Current day is more than 9 days from previous period (${context.dayCount} days)',
    ));

    if (withinNineDays) {
      return _applyWithinNineDaysRules(context, results);
    } else {
      return _applyBeyondNineDaysRules(context, results);
    }
  }

  /// Apply rules when current day is within 9 days of previous period
  static List<RuleResult> _applyWithinNineDaysRules(
      FlowCategorizationContext context, List<RuleResult> results) {
    final previousDayIsFlow = context.previousDayData?.isFlowDay == true;

    // Rule 2: Check if previous day is flow day
    results.add(RuleResult(
      ruleId: 'R2_PREVIOUS_DAY_FLOW',
      ruleName: 'Check Previous Day Flow Status',
      description: 'Determine if previous day has flow',
      condition: 'previousDay.isFlowDay == true',
      conditionMet: previousDayIsFlow,
      contextValues: {
        'previousDayFlowLevel':
            context.previousDayData?.flowLevel?.toString() ?? 'null',
        'previousDayIsFlow': previousDayIsFlow.toString(),
      },
      outcome: previousDayIsFlow,
      explanation: previousDayIsFlow
          ? 'Previous day has flow (level ${context.previousDayData?.flowLevel})'
          : 'Previous day has no flow',
    ));

    if (!previousDayIsFlow) {
      return _applyPreviousDayNoFlowRules(context, results);
    } else {
      return _applyPreviousDayHasFlowRules(context, results);
    }
  }

  /// Apply rules when previous day has no flow (within 9 days)
  static List<RuleResult> _applyPreviousDayNoFlowRules(
      FlowCategorizationContext context, List<RuleResult> results) {
    final dayBeforePreviousIsFlow =
        context.dayBeforePreviousData?.isFlowDay == true;

    // Rule 3A: Check if day before previous has flow
    results.add(RuleResult(
      ruleId: 'R3A_DAY_BEFORE_PREVIOUS_FLOW',
      ruleName: 'Check Day Before Previous Flow Status',
      description: 'Determine if day before previous has flow',
      condition: 'dayBeforePrevious.isFlowDay == true',
      conditionMet: dayBeforePreviousIsFlow,
      contextValues: {
        'dayBeforePreviousFlowLevel':
            context.dayBeforePreviousData?.flowLevel?.toString() ?? 'null',
        'dayBeforePreviousIsFlow': dayBeforePreviousIsFlow.toString(),
      },
      outcome: dayBeforePreviousIsFlow,
      explanation: dayBeforePreviousIsFlow
          ? 'Day before previous has flow (level ${context.dayBeforePreviousData?.flowLevel})'
          : 'Day before previous has no flow',
    ));

    if (!dayBeforePreviousIsFlow) {
      // Rule 3A1: Previous day NOT flow + Day before previous NOT flow → Period Flow Day
      results.add(RuleResult(
        ruleId: 'R3A1_BOTH_NO_FLOW',
        ruleName: 'Both Previous Days No Flow → Period Flow Day',
        description:
            'When both previous days have no flow, current day becomes period flow day',
        condition: '!previousDay.isFlowDay && !dayBeforePrevious.isFlowDay',
        conditionMet: true,
        contextValues: {},
        outcome: DayCategory.periodFlowDay,
        explanation:
            'Both previous days have no flow, so current day is categorized as period flow day',
      ));
    } else {
      // Day before previous HAS flow - check if it's a period day
      final dayBeforePreviousIsPeriod =
          context.dayBeforePreviousData?.isPeriodDate == true;

      results.add(RuleResult(
        ruleId: 'R3A2_DAY_BEFORE_PREVIOUS_PERIOD',
        ruleName: 'Check Day Before Previous Period Status',
        description: 'Determine if day before previous is a period day',
        condition: 'dayBeforePrevious.isPeriodDate == true',
        conditionMet: dayBeforePreviousIsPeriod,
        contextValues: {
          'dayBeforePreviousIsPeriod': dayBeforePreviousIsPeriod.toString(),
        },
        outcome: dayBeforePreviousIsPeriod,
        explanation: dayBeforePreviousIsPeriod
            ? 'Day before previous is a period day'
            : 'Day before previous is not a period day',
      ));

      if (dayBeforePreviousIsPeriod) {
        // Rule 3A2A: Previous day NOT flow + Day before previous IS flow + IS period → Period Flow Day
        results.add(RuleResult(
          ruleId: 'R3A2A_FLOW_AND_PERIOD',
          ruleName: 'Day Before Previous Flow+Period → Period Flow Day',
          description:
              'When day before previous has flow and is period day, current day becomes period flow day',
          condition:
              '!previousDay.isFlowDay && dayBeforePrevious.isFlowDay && dayBeforePrevious.isPeriodDate',
          conditionMet: true,
          contextValues: {},
          outcome: DayCategory.periodFlowDay,
          explanation:
              'Day before previous has flow and is period day, so current day is categorized as period flow day',
        ));
      } else {
        // Rule 3A2B: Previous day NOT flow + Day before previous IS flow + NOT period → Flow Day
        results.add(RuleResult(
          ruleId: 'R3A2B_FLOW_NOT_PERIOD',
          ruleName: 'Day Before Previous Flow Only → Flow Day',
          description:
              'When day before previous has flow but is not period day, current day becomes flow day',
          condition:
              '!previousDay.isFlowDay && dayBeforePrevious.isFlowDay && !dayBeforePrevious.isPeriodDate',
          conditionMet: true,
          contextValues: {},
          outcome: DayCategory.flowDay,
          explanation:
              'Day before previous has flow but is not period day, so current day is categorized as flow day',
        ));
      }
    }

    return results;
  }

  /// Apply rules when previous day has flow (within 9 days)
  static List<RuleResult> _applyPreviousDayHasFlowRules(
      FlowCategorizationContext context, List<RuleResult> results) {
    final previousDayIsPeriod = context.previousDayData?.isPeriodDate == true;

    // Rule 3B: Check if previous day is period day
    results.add(RuleResult(
      ruleId: 'R3B_PREVIOUS_DAY_PERIOD',
      ruleName: 'Check Previous Day Period Status',
      description: 'Determine if previous day is a period day',
      condition: 'previousDay.isPeriodDate == true',
      conditionMet: previousDayIsPeriod,
      contextValues: {
        'previousDayIsPeriod': previousDayIsPeriod.toString(),
      },
      outcome: previousDayIsPeriod,
      explanation: previousDayIsPeriod
          ? 'Previous day is a period day'
          : 'Previous day is not a period day',
    ));

    if (previousDayIsPeriod) {
      // Rule 3B1: Previous day IS flow + IS period → Period Flow Day
      results.add(RuleResult(
        ruleId: 'R3B1_FLOW_AND_PERIOD',
        ruleName: 'Previous Day Flow+Period → Period Flow Day',
        description:
            'When previous day has flow and is period day, current day becomes period flow day',
        condition: 'previousDay.isFlowDay && previousDay.isPeriodDate',
        conditionMet: true,
        contextValues: {},
        outcome: DayCategory.periodFlowDay,
        explanation:
            'Previous day has flow and is period day, so current day is categorized as period flow day',
      ));
    } else {
      // Rule 3B2: Previous day IS flow + NOT period → Flow Day
      results.add(RuleResult(
        ruleId: 'R3B2_FLOW_NOT_PERIOD',
        ruleName: 'Previous Day Flow Only → Flow Day',
        description:
            'When previous day has flow but is not period day, current day becomes flow day',
        condition: 'previousDay.isFlowDay && !previousDay.isPeriodDate',
        conditionMet: true,
        contextValues: {},
        outcome: DayCategory.flowDay,
        explanation:
            'Previous day has flow but is not period day, so current day is categorized as flow day',
      ));
    }

    return results;
  }

  /// Apply rules when current day is beyond 9 days from previous period
  static List<RuleResult> _applyBeyondNineDaysRules(
      FlowCategorizationContext context, List<RuleResult> results) {
    final dayBeforePreviousIsFlow =
        context.dayBeforePreviousData?.isFlowDay == true;

    // Rule 4: Check if day before previous has flow (beyond 9 days)
    results.add(RuleResult(
      ruleId: 'R4_DAY_BEFORE_PREVIOUS_FLOW_BEYOND',
      ruleName: 'Check Day Before Previous Flow Status (Beyond 9 Days)',
      description:
          'Determine if day before previous has flow when beyond 9 days from period',
      condition: 'dayBeforePrevious.isFlowDay == true',
      conditionMet: dayBeforePreviousIsFlow,
      contextValues: {
        'dayBeforePreviousFlowLevel':
            context.dayBeforePreviousData?.flowLevel?.toString() ?? 'null',
        'dayBeforePreviousIsFlow': dayBeforePreviousIsFlow.toString(),
      },
      outcome: dayBeforePreviousIsFlow,
      explanation: dayBeforePreviousIsFlow
          ? 'Day before previous has flow (level ${context.dayBeforePreviousData?.flowLevel})'
          : 'Day before previous has no flow',
    ));

    if (!dayBeforePreviousIsFlow) {
      // Rule 4A: Day before previous NOT flow → Flow Day
      results.add(RuleResult(
        ruleId: 'R4A_NO_FLOW_BEYOND',
        ruleName: 'Beyond 9 Days + No Previous Flow → Flow Day',
        description:
            'When beyond 9 days and day before previous has no flow, current day becomes flow day',
        condition: 'dayCount > 9 && !dayBeforePrevious.isFlowDay',
        conditionMet: true,
        contextValues: {},
        outcome: DayCategory.flowDay,
        explanation:
            'Beyond 9 days from period and day before previous has no flow, so current day is categorized as flow day',
      ));
    } else {
      // Day before previous HAS flow - check if it's a period day
      final dayBeforePreviousIsPeriod =
          context.dayBeforePreviousData?.isPeriodDate == true;

      results.add(RuleResult(
        ruleId: 'R4B_DAY_BEFORE_PREVIOUS_PERIOD_BEYOND',
        ruleName: 'Check Day Before Previous Period Status (Beyond 9 Days)',
        description:
            'Determine if day before previous is a period day when beyond 9 days',
        condition: 'dayBeforePrevious.isPeriodDate == true',
        conditionMet: dayBeforePreviousIsPeriod,
        contextValues: {
          'dayBeforePreviousIsPeriod': dayBeforePreviousIsPeriod.toString(),
        },
        outcome: dayBeforePreviousIsPeriod,
        explanation: dayBeforePreviousIsPeriod
            ? 'Day before previous is a period day'
            : 'Day before previous is not a period day',
      ));

      if (dayBeforePreviousIsPeriod) {
        // Rule 4B1: Beyond 9 days + Day before previous IS flow + IS period → Period Flow Day
        results.add(RuleResult(
          ruleId: 'R4B1_FLOW_AND_PERIOD_BEYOND',
          ruleName: 'Beyond 9 Days + Previous Flow+Period → Period Flow Day',
          description:
              'When beyond 9 days but day before previous has flow and is period day, current day becomes period flow day',
          condition:
              'dayCount > 9 && dayBeforePrevious.isFlowDay && dayBeforePrevious.isPeriodDate',
          conditionMet: true,
          contextValues: {},
          outcome: DayCategory.periodFlowDay,
          explanation:
              'Beyond 9 days but day before previous has flow and is period day, so current day is categorized as period flow day',
        ));
      } else {
        // Rule 4B2: Beyond 9 days + Day before previous IS flow + NOT period → Flow Day
        results.add(RuleResult(
          ruleId: 'R4B2_FLOW_NOT_PERIOD_BEYOND',
          ruleName: 'Beyond 9 Days + Previous Flow Only → Flow Day',
          description:
              'When beyond 9 days and day before previous has flow but is not period day, current day becomes flow day',
          condition:
              'dayCount > 9 && dayBeforePrevious.isFlowDay && !dayBeforePrevious.isPeriodDate',
          conditionMet: true,
          contextValues: {},
          outcome: DayCategory.flowDay,
          explanation:
              'Beyond 9 days and day before previous has flow but is not period day, so current day is categorized as flow day',
        ));
      }
    }

    return results;
  }

  /// Apply rules to determine previous day updates
  static List<RuleResult> _applyPreviousDayRules(
      FlowCategorizationContext context, DayCategory currentDayCategory) {
    final results = <RuleResult>[];

    // Rule 5: Check if current day is period flow day and previous day has flow
    final shouldCheckPreviousDayUpdate =
        currentDayCategory == DayCategory.periodFlowDay &&
            context.previousDayData?.isFlowDay == true;

    results.add(RuleResult(
      ruleId: 'R5_CHECK_PREVIOUS_DAY_UPDATE',
      ruleName: 'Check If Previous Day Needs Update',
      description:
          'Determine if previous day needs to be updated based on current day categorization',
      condition: 'currentDay == periodFlowDay && previousDay.isFlowDay',
      conditionMet: shouldCheckPreviousDayUpdate,
      contextValues: {
        'currentDayCategory': currentDayCategory.toString(),
        'previousDayIsFlow':
            (context.previousDayData?.isFlowDay == true).toString(),
      },
      outcome: shouldCheckPreviousDayUpdate,
      explanation: shouldCheckPreviousDayUpdate
          ? 'Current day is period flow day and previous day has flow - checking for updates'
          : 'No previous day update needed',
    ));

    if (shouldCheckPreviousDayUpdate) {
      final dayBeforePreviousIsFlow =
          context.dayBeforePreviousData?.isFlowDay == true;

      // Rule 6: Check if day before previous has flow
      results.add(RuleResult(
        ruleId: 'R6_DAY_BEFORE_PREVIOUS_FLOW_UPDATE',
        ruleName: 'Check Day Before Previous For Update',
        description:
            'Determine if day before previous has flow for update logic',
        condition: 'dayBeforePrevious.isFlowDay == true',
        conditionMet: dayBeforePreviousIsFlow,
        contextValues: {
          'dayBeforePreviousIsFlow': dayBeforePreviousIsFlow.toString(),
        },
        outcome: dayBeforePreviousIsFlow,
        explanation: dayBeforePreviousIsFlow
            ? 'Day before previous has flow - checking period status'
            : 'Day before previous has no flow - no update needed',
      ));

      if (dayBeforePreviousIsFlow) {
        final dayBeforePreviousIsPeriod =
            context.dayBeforePreviousData?.isPeriodDate == true;

        // Rule 7: Check if day before previous is period day
        results.add(RuleResult(
          ruleId: 'R7_DAY_BEFORE_PREVIOUS_PERIOD_UPDATE',
          ruleName: 'Check Day Before Previous Period For Update',
          description: 'Determine if day before previous is period day for update logic',
          condition: 'dayBeforePrevious.isPeriodDate == true',
          conditionMet: dayBeforePreviousIsPeriod,
          contextValues: {
            'dayBeforePreviousIsPeriod': dayBeforePreviousIsPeriod.toString(),
          },
          outcome: dayBeforePreviousIsPeriod,
          explanation: dayBeforePreviousIsPeriod
              ? 'Day before previous is period day'
              : 'Day before previous is not period day',
        ));

        if (dayBeforePreviousIsPeriod) {
          // Rule 7A: Update previous day to period flow day
          results.add(RuleResult(
            ruleId: 'R7A_UPDATE_TO_PERIOD_FLOW_DAY',
            ruleName: 'Update Previous Day → Period Flow Day',
            description:
                'Update previous day to period flow day when day before previous is flow+period',
            condition:
                'dayBeforePrevious.isFlowDay && dayBeforePrevious.isPeriodDate',
            conditionMet: true,
            contextValues: {},
            outcome: PreviousDayUpdate.toPeriodFlowDay,
            explanation:
                'Day before previous has flow and is period day, so update previous day to period flow day',
          ));
        } else {
          // Rule 7B: Update previous day to period day (connecting day)
          results.add(RuleResult(
            ruleId: 'R7B_UPDATE_TO_PERIOD_DAY',
            ruleName: 'Update Previous Day → Period Day',
            description:
                'Update previous day to period day (connecting day) when day before previous is flow only',
            condition:
                'dayBeforePrevious.isFlowDay && !dayBeforePrevious.isPeriodDate',
            conditionMet: true,
            contextValues: {},
            outcome: PreviousDayUpdate.toPeriodDay,
            explanation:
                'Day before previous has flow but is not period day, so update previous day to period day (connecting day)',
          ));
        }
      }
    }

    return results;
  }

  /// Get period tracking data for a specific date
  static PeriodTrackingModel? _getDataForDate(
    Map<String, Map<String, PeriodTrackingModel>> existingData,
    DateTime date,
  ) {
    final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
    final dayKey = date.day.toString().padLeft(2, '0');

    return existingData[monthKey]?[dayKey];
  }
}
