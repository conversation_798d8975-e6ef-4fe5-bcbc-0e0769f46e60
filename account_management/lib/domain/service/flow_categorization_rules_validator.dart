import 'package:flutter/foundation.dart';
import '../model/period_tracking_model.dart';
import 'flow_categorization_rules_engine.dart';

/// Validator for flow categorization rules
/// Provides testing and verification capabilities for the rules engine
class FlowCategorizationRulesValidator {
  /// Validate all rules with predefined test scenarios
  static ValidationReport validateAllRules() {
    debugPrint(
        '🧪 FlowCategorizationRulesValidator: Starting comprehensive rule validation');

    final testScenarios = _createTestScenarios();
    final results = <ValidationResult>[];

    for (final scenario in testScenarios) {
      final result = _validateScenario(scenario);
      results.add(result);

      if (!result.passed) {
        debugPrint('❌ Test failed: ${scenario.name}');
        debugPrint('   Expected: ${scenario.expectedCurrentDayCategory}');
        debugPrint('   Actual: ${result.actualCurrentDayCategory}');
        debugPrint(
            '   Expected Previous Update: ${scenario.expectedPreviousDayUpdate}');
        debugPrint(
            '   Actual Previous Update: ${result.actualPreviousDayUpdate}');
      } else {
        debugPrint('✅ Test passed: ${scenario.name}');
      }
    }

    final report = ValidationReport(
      totalTests: results.length,
      passedTests: results.where((r) => r.passed).length,
      failedTests: results.where((r) => !r.passed).length,
      results: results,
    );

    debugPrint(
        '🧪 Validation complete: ${report.passedTests}/${report.totalTests} tests passed');
    return report;
  }

  /// Validate a specific rule scenario
  static ValidationResult validateScenario(TestScenario scenario) {
    return _validateScenario(scenario);
  }

  /// Create comprehensive test scenarios covering all rules
  static List<TestScenario> _createTestScenarios() {
    return [
      // Rule 1.1: Within 9 days, previous day NOT flow, day before previous NOT flow
      TestScenario(
        name: 'Rule 1.1: Both previous days no flow (within 9 days)',
        description:
            'When within 9 days and both previous days have no flow, current day should be period flow day',
        currentDate: DateTime(2024, 1, 15),
        flowLevel: 2,
        existingData: _createTestData({
          DateTime(2024, 1, 10): PeriodTrackingModel(
            date: DateTime(2024, 1, 10),
            isPeriodDate: true,
            flowLevel: 2,
          ),
          // Previous day (Jan 14) - no data (no flow)
          // Day before previous (Jan 13) - no data (no flow)
        }),
        expectedCurrentDayCategory: DayCategory.periodFlowDay,
        expectedPreviousDayUpdate: null,
      ),

      // Rule 1.2: Within 9 days, previous day NOT flow, day before previous IS flow + period
      TestScenario(
        name: 'Rule 1.2: Day before previous flow+period (within 9 days)',
        description:
            'When within 9 days, previous day no flow, day before previous has flow and is period day',
        currentDate: DateTime(2024, 1, 15),
        flowLevel: 2,
        existingData: _createTestData({
          DateTime(2024, 1, 10): PeriodTrackingModel(
            date: DateTime(2024, 1, 10),
            isPeriodDate: true,
            flowLevel: 2,
          ),
          DateTime(2024, 1, 13): PeriodTrackingModel(
            date: DateTime(2024, 1, 13),
            isPeriodDate: true,
            flowLevel: 2,
          ),
          // Previous day (Jan 14) - no data (no flow)
        }),
        expectedCurrentDayCategory: DayCategory.periodFlowDay,
        expectedPreviousDayUpdate: null,
      ),

      // Rule 1.3: Within 9 days, previous day NOT flow, day before previous IS flow + NOT period
      TestScenario(
        name: 'Rule 1.3: Day before previous flow only (within 9 days)',
        description:
            'When within 9 days, previous day no flow, day before previous has flow but not period day',
        currentDate: DateTime(2024, 1, 15),
        flowLevel: 2,
        existingData: _createTestData({
          DateTime(2024, 1, 10): PeriodTrackingModel(
            date: DateTime(2024, 1, 10),
            isPeriodDate: true,
            flowLevel: 2,
          ),
          DateTime(2024, 1, 13): PeriodTrackingModel(
            date: DateTime(2024, 1, 13),
            isPeriodDate: false,
            flowLevel: 2,
          ),
          // Previous day (Jan 14) - no data (no flow)
        }),
        expectedCurrentDayCategory: DayCategory.flowDay,
        expectedPreviousDayUpdate: null,
      ),

      // Rule 1.4: Within 9 days, previous day IS flow + period
      TestScenario(
        name: 'Rule 1.4: Previous day flow+period (within 9 days)',
        description:
            'When within 9 days and previous day has flow and is period day',
        currentDate: DateTime(2024, 1, 15),
        flowLevel: 2,
        existingData: _createTestData({
          DateTime(2024, 1, 10): PeriodTrackingModel(
            date: DateTime(2024, 1, 10),
            isPeriodDate: true,
            flowLevel: 2,
          ),
          DateTime(2024, 1, 14): PeriodTrackingModel(
            date: DateTime(2024, 1, 14),
            isPeriodDate: true,
            flowLevel: 2,
          ),
        }),
        expectedCurrentDayCategory: DayCategory.periodFlowDay,
        expectedPreviousDayUpdate: null,
      ),

      // Rule 1.5: Within 9 days, previous day IS flow + NOT period
      TestScenario(
        name: 'Rule 1.5: Previous day flow only (within 9 days)',
        description:
            'When within 9 days and previous day has flow but not period day',
        currentDate: DateTime(2024, 1, 15),
        flowLevel: 2,
        existingData: _createTestData({
          DateTime(2024, 1, 10): PeriodTrackingModel(
            date: DateTime(2024, 1, 10),
            isPeriodDate: true,
            flowLevel: 2,
          ),
          DateTime(2024, 1, 14): PeriodTrackingModel(
            date: DateTime(2024, 1, 14),
            isPeriodDate: false,
            flowLevel: 2,
          ),
        }),
        expectedCurrentDayCategory: DayCategory.flowDay,
        expectedPreviousDayUpdate: null,
      ),

      // Rule 2.1: Beyond 9 days, day before previous NOT flow
      TestScenario(
        name: 'Rule 2.1: Beyond 9 days, no previous flow',
        description: 'When beyond 9 days and day before previous has no flow',
        currentDate: DateTime(2024, 1, 25),
        flowLevel: 2,
        existingData: _createTestData({
          DateTime(2024, 1, 10): PeriodTrackingModel(
            date: DateTime(2024, 1, 10),
            isPeriodDate: true,
            flowLevel: 2,
          ),
          // Day before previous (Jan 23) - no data (no flow)
        }),
        expectedCurrentDayCategory: DayCategory.flowDay,
        expectedPreviousDayUpdate: null,
      ),

      // Rule 2.2: Beyond 9 days, day before previous IS flow + period
      TestScenario(
        name: 'Rule 2.2: Beyond 9 days, previous flow+period',
        description:
            'When beyond 9 days and day before previous has flow and is period day',
        currentDate: DateTime(2024, 1, 25),
        flowLevel: 2,
        existingData: _createTestData({
          DateTime(2024, 1, 10): PeriodTrackingModel(
            date: DateTime(2024, 1, 10),
            isPeriodDate: true,
            flowLevel: 2,
          ),
          DateTime(2024, 1, 23): PeriodTrackingModel(
            date: DateTime(2024, 1, 23),
            isPeriodDate: true,
            flowLevel: 2,
          ),
        }),
        expectedCurrentDayCategory: DayCategory.periodFlowDay,
        expectedPreviousDayUpdate: null,
      ),

      // Rule 2.3: Beyond 9 days, day before previous IS flow + NOT period
      TestScenario(
        name: 'Rule 2.3: Beyond 9 days, previous flow only',
        description:
            'When beyond 9 days and day before previous has flow but not period day',
        currentDate: DateTime(2024, 1, 25),
        flowLevel: 2,
        existingData: _createTestData({
          DateTime(2024, 1, 10): PeriodTrackingModel(
            date: DateTime(2024, 1, 10),
            isPeriodDate: true,
            flowLevel: 2,
          ),
          DateTime(2024, 1, 23): PeriodTrackingModel(
            date: DateTime(2024, 1, 23),
            isPeriodDate: false,
            flowLevel: 2,
          ),
        }),
        expectedCurrentDayCategory: DayCategory.flowDay,
        expectedPreviousDayUpdate: null,
      ),

      // Rule 3.1: Previous day update to period flow day
      TestScenario(
        name: 'Rule 3.1: Update previous day to period flow day',
        description:
            'When current day is period flow day, previous day has flow, and day before previous has flow+period',
        currentDate: DateTime(2024, 1, 15),
        flowLevel: 2,
        existingData: _createTestData({
          DateTime(2024, 1, 10): PeriodTrackingModel(
            date: DateTime(2024, 1, 10),
            isPeriodDate: true,
            flowLevel: 2,
          ),
          DateTime(2024, 1, 13): PeriodTrackingModel(
            date: DateTime(2024, 1, 13),
            isPeriodDate: true,
            flowLevel: 2,
          ),
          DateTime(2024, 1, 14): PeriodTrackingModel(
            date: DateTime(2024, 1, 14),
            isPeriodDate: false,
            flowLevel: 2,
          ),
        }),
        expectedCurrentDayCategory: DayCategory.periodFlowDay,
        expectedPreviousDayUpdate: PreviousDayUpdate.toPeriodFlowDay,
      ),

      // Rule 3.2: Previous day update to period day
      TestScenario(
        name: 'Rule 3.2: Update previous day to period day',
        description:
            'When current day is period flow day, previous day has flow, and day before previous has no flow',
        currentDate: DateTime(2024, 1, 15),
        flowLevel: 2,
        existingData: _createTestData({
          DateTime(2024, 1, 10): PeriodTrackingModel(
            date: DateTime(2024, 1, 10),
            isPeriodDate: true,
            flowLevel: 2,
          ),
          DateTime(2024, 1, 14): PeriodTrackingModel(
            date: DateTime(2024, 1, 14),
            isPeriodDate: false,
            flowLevel: 2,
          ),
          // Day before previous (Jan 13) - no data (no flow)
        }),
        expectedCurrentDayCategory: DayCategory.periodFlowDay,
        expectedPreviousDayUpdate: PreviousDayUpdate.toPeriodDay,
      ),
    ];
  }

  /// Validate a single test scenario
  static ValidationResult _validateScenario(TestScenario scenario) {
    try {
      final result = FlowCategorizationRulesEngine.categorizeFlowDay(
        currentDate: scenario.currentDate,
        flowLevel: scenario.flowLevel,
        existingData: scenario.existingData,
      );

      final passed = result.finalCurrentDayCategory ==
              scenario.expectedCurrentDayCategory &&
          result.finalPreviousDayUpdate == scenario.expectedPreviousDayUpdate;

      return ValidationResult(
        scenario: scenario,
        passed: passed,
        actualCurrentDayCategory: result.finalCurrentDayCategory,
        actualPreviousDayUpdate: result.finalPreviousDayUpdate,
        ruleResult: result,
      );
    } catch (e) {
      return ValidationResult(
        scenario: scenario,
        passed: false,
        actualCurrentDayCategory: null,
        actualPreviousDayUpdate: null,
        error: e.toString(),
      );
    }
  }

  /// Create test data in the expected format
  static Map<String, Map<String, PeriodTrackingModel>> _createTestData(
    Map<DateTime, PeriodTrackingModel> dateData,
  ) {
    final result = <String, Map<String, PeriodTrackingModel>>{};

    for (final entry in dateData.entries) {
      final date = entry.key;
      final model = entry.value;

      final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
      final dayKey = date.day.toString().padLeft(2, '0');

      result[monthKey] ??= {};
      result[monthKey]![dayKey] = model;
    }

    return result;
  }
}

/// Test scenario for rule validation
class TestScenario {
  final String name;
  final String description;
  final DateTime currentDate;
  final int flowLevel;
  final Map<String, Map<String, PeriodTrackingModel>> existingData;
  final DayCategory expectedCurrentDayCategory;
  final PreviousDayUpdate? expectedPreviousDayUpdate;

  TestScenario({
    required this.name,
    required this.description,
    required this.currentDate,
    required this.flowLevel,
    required this.existingData,
    required this.expectedCurrentDayCategory,
    this.expectedPreviousDayUpdate,
  });

  @override
  String toString() {
    return 'TestScenario('
        'name: $name, '
        'currentDate: $currentDate, '
        'flowLevel: $flowLevel, '
        'expectedCurrentDayCategory: $expectedCurrentDayCategory, '
        'expectedPreviousDayUpdate: $expectedPreviousDayUpdate'
        ')';
  }
}

/// Result of validating a test scenario
class ValidationResult {
  final TestScenario scenario;
  final bool passed;
  final DayCategory? actualCurrentDayCategory;
  final PreviousDayUpdate? actualPreviousDayUpdate;
  final FlowCategorizationRuleResult? ruleResult;
  final String? error;

  ValidationResult({
    required this.scenario,
    required this.passed,
    this.actualCurrentDayCategory,
    this.actualPreviousDayUpdate,
    this.ruleResult,
    this.error,
  });

  @override
  String toString() {
    return 'ValidationResult('
        'scenario: ${scenario.name}, '
        'passed: $passed, '
        'actualCurrentDayCategory: $actualCurrentDayCategory, '
        'actualPreviousDayUpdate: $actualPreviousDayUpdate'
        '${error != null ? ', error: $error' : ''}'
        ')';
  }
}

/// Complete validation report
class ValidationReport {
  final int totalTests;
  final int passedTests;
  final int failedTests;
  final List<ValidationResult> results;

  ValidationReport({
    required this.totalTests,
    required this.passedTests,
    required this.failedTests,
    required this.results,
  });

  /// Get only the failed test results
  List<ValidationResult> getFailedResults() {
    return results.where((r) => !r.passed).toList();
  }

  /// Get only the passed test results
  List<ValidationResult> getPassedResults() {
    return results.where((r) => r.passed).toList();
  }

  /// Get a summary report as string
  String getSummary() {
    final buffer = StringBuffer();
    buffer.writeln('Flow Categorization Rules Validation Report');
    buffer.writeln('==========================================');
    buffer.writeln('Total Tests: $totalTests');
    buffer.writeln('Passed: $passedTests');
    buffer.writeln('Failed: $failedTests');
    buffer.writeln(
        'Success Rate: ${(passedTests / totalTests * 100).toStringAsFixed(1)}%');
    buffer.writeln('');

    if (failedTests > 0) {
      buffer.writeln('Failed Tests:');
      buffer.writeln('-------------');
      for (final result in getFailedResults()) {
        buffer.writeln('❌ ${result.scenario.name}');
        buffer.writeln(
            '   Expected: ${result.scenario.expectedCurrentDayCategory}');
        buffer.writeln('   Actual: ${result.actualCurrentDayCategory}');
        if (result.scenario.expectedPreviousDayUpdate != null ||
            result.actualPreviousDayUpdate != null) {
          buffer.writeln(
              '   Expected Previous Update: ${result.scenario.expectedPreviousDayUpdate}');
          buffer.writeln(
              '   Actual Previous Update: ${result.actualPreviousDayUpdate}');
        }
        if (result.error != null) {
          buffer.writeln('   Error: ${result.error}');
        }
        buffer.writeln('');
      }
    }

    if (passedTests > 0) {
      buffer.writeln('Passed Tests:');
      buffer.writeln('-------------');
      for (final result in getPassedResults()) {
        buffer.writeln('✅ ${result.scenario.name}');
      }
    }

    return buffer.toString();
  }

  @override
  String toString() {
    return 'ValidationReport(totalTests: $totalTests, passedTests: $passedTests, failedTests: $failedTests)';
  }
}
