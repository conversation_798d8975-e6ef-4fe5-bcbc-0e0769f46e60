import 'package:account_management/domain/failure/health_data_failure.dart';
import 'package:account_management/domain/model/health_data.dart';
import 'package:account_management/domain/model/period_prediction_metadata.dart';
import 'package:doso/doso.dart';
import '../core/unit.dart';


abstract class HealthDataFacade {
  Future<Do<HealthDataFailure, Unit>> updatePeriodLength(int periodLength);
  Future<Do<HealthDataFailure, Unit>> updateCycleLength(int cycleLength);
  Future<Do<HealthDataFailure, Unit>> updateContraceptionType(
      String contraceptionType);
  Future<Do<HealthDataFailure, Unit>> updateOvulationDate(
      DateTime ovulationDate);
  Future<Do<HealthDataFailure, Unit>> updateNextPeriodStartDate(
      DateTime nextPeriodStartDate);
  Future<Do<HealthDataFailure, Unit>> updateNextOvulationDate(
      DateTime nextOvulationDate);
  Future<Do<HealthDataFailure, Unit>> updatePredictionMetadata(
      PeriodPredictionMetadata metadata);
  Stream<Do<HealthDataFailure, HealthDataModel?>> watchHealthData();
}
