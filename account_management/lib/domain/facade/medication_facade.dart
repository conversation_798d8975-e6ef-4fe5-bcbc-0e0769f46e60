import 'package:doso/doso.dart';
import '../core/unit.dart';
import '../failure/medication_failure.dart';
import '../model/daily_medication_model.dart';
import '../model/medication_model.dart';

abstract class MedicationFacade {
  Future<Do<MedicationFailure, Unit>> create(MedicationModel medication);
  Future<Do<MedicationFailure, Unit>> update(MedicationModel medication);
  Future<Do<MedicationFailure, Unit>> delete(MedicationModel medication);
  Stream<Do<MedicationFailure, List<MedicationModel>>> getMedications();
  Stream<Do<MedicationFailure, List<DailyMedicationModel>>>
      getDailyMedications();
}
