import 'package:doso/doso.dart';
import '../failure/period_tracking_failure.dart';
import '../model/period_reminder_settings.dart';
import '../core/unit.dart';

abstract class PeriodReminderFacade {
  // Reminder settings methods
  Future<Do<PeriodTrackingFailure, PeriodReminderSettings>>
      getReminderSettings();

  Future<Do<PeriodTrackingFailure, Unit>> saveReminderSettings(
      PeriodReminderSettings settings);

  Future<Do<PeriodTrackingFailure, Unit>> scheduleNotificationsForSettings(
      PeriodReminderSettings settings);

  Future<Do<PeriodTrackingFailure, Unit>> cancelAllPeriodNotifications();

  // Initialization method
  Future<Do<PeriodTrackingFailure, Unit>> initializePeriodReminders();

  // Helper method to reschedule notifications after period data changes
  Future<void> rescheduleNotificationsAfterPeriodUpdate();
}
