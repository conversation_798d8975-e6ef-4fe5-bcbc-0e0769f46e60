import 'package:analytics/domain/models/therapy_session_event_model.dart';
import 'package:doso/doso.dart';
import 'package:remote/domain/model/device_info_model.dart';
import '../models/device_settings_model.dart';

import '../models/therapy_session_model.dart';

/// Unified facade for all analytics operations
abstract class IAnalyticsFacade {
  Future<Do<String, Object?>> saveDeviceSnapshot({
    required String sessionId,
    required Map<String, dynamic> snapshot,
    DateTime? timestamp,
  });

  // Enhanced therapy session management
  Future<Do<String, String>> startTherapySession();

  Future<Do<String, Object?>> endTherapySession();

  Future<Do<String, Object?>> logSettingChange({
    required TherapySessionEventModel event,
  });

  // Session data retrieval
  Future<Do<String, List<TherapySessionModel>>> getStoredSessions();
  Future<Do<String, List<TherapySessionModel>>> getUnsyncedSessions();
  Future<Do<String, TherapySessionModel?>> getCurrentSession();

  // Sync operations
  Future<Do<String, Object?>> syncSessionsToCloud();
  Future<Do<String, Object?>>
      forceResyncAllSessions(); // For debugging sync issues
  Future<Do<String, Object?>> saveDeviceLog({
    required String sessionId,
    required Map<String, dynamic> log,
    DateTime? timestamp,
  });
  Future<Do<String, List<TherapySessionModel>>> getTherapySessionsLocal();
  Future<Do<String, List<TherapySessionModel>>> getTherapySessionsFromCloud();
  Future<Do<String, List<TherapySessionModel>>>
      getTherapySessionsWithFallback();
  Future<Do<String, Object?>> syncTherapySessionsToCloud();

  // Session management operations

  Future<Do<String, Map<String, dynamic>>> getMostUsedSettings();
  //get current session id
  Future<String?> getCurrentSessionId();

  // === 🎯 Feedback operations ===
  Future<Do<String, Object?>> submitSessionFeedback({
    required String sessionId,
    String? feedbackText,
    int? painLevelBefore,
    int? painLevelAfter,
  });

  Future<Do<String, Object?>> scheduleSessionFeedbackNotification({
    required String sessionId,
    required Duration delay,
  });

  Future<Do<String, Object?>> markFeedbackRequested(String sessionId);

  // === 🎯 Feedback trigger operations ===
  /// Stream that emits session IDs when feedback should be shown
  Stream<String> get feedbackTriggerStream;

  /// Check if notification payload contains a session that needs feedback
  Future<void> checkNotificationPayload(String? payload);

  /// Check if most recent session needs feedback (app opened without notification)
  Future<void> checkMostRecentSession();
}
