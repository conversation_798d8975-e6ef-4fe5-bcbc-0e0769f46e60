import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:analytics/domain/facade/analytics_facade.dart';
import 'package:analytics/domain/models/therapy_session_model.dart';
part 'therapy_feedback_event.dart';
part 'therapy_feedback_state.dart';
part 'therapy_feedback_bloc.freezed.dart';

@injectable
class TherapyFeedbackBloc
    extends Bloc<TherapyFeedbackEvent, TherapyFeedbackState> {
  final IAnalyticsFacade _analyticsFacade;

  TherapyFeedbackBloc(this._analyticsFacade)
      : super(const TherapyFeedbackState.initial()) {
    on<TherapyFeedbackEvent>(_onEvent);
  }

  Future<void> _onEvent(
      TherapyFeedbackEvent event, Emitter<TherapyFeedbackState> emit) async {
    await event.when(
      loadSessionData: (sessionId) => _onLoadSessionData(sessionId, emit),
      submitFeedback: (sessionId, feedbackText, painLevelBefore,
              painLevelAfter) =>
          _onSubmitFeedback(
              sessionId, feedbackText, painLevelBefore, painLevelAfter, emit),
      resetState: () => _onResetState(emit),
    );
  }

  Future<void> _onLoadSessionData(
      String sessionId, Emitter<TherapyFeedbackState> emit) async {
    emit(const TherapyFeedbackState.loading());

    try {
      final sessionsResult = await _analyticsFacade.getTherapySessionsLocal();

      // Handle the Do result manually using fold
      sessionsResult.fold(
        onFailure: (error) => emit(TherapyFeedbackState.error(error)),
        onSuccess: (sessions) {
          final session = sessions
              .where((s) => s.sessionInfo.sessionId == sessionId)
              .firstOrNull;
          if (session != null) {
            emit(TherapyFeedbackState.sessionLoaded(session));
          } else {
            emit(const TherapyFeedbackState.error('Session not found'));
          }
        },
      );
    } catch (e) {
      emit(TherapyFeedbackState.error(e.toString()));
    }
  }

  Future<void> _onSubmitFeedback(
    String sessionId,
    String? feedbackText,
    int? painLevelBefore,
    int? painLevelAfter,
    Emitter<TherapyFeedbackState> emit,
  ) async {
    emit(const TherapyFeedbackState.submitting());

    try {
      final result = await _analyticsFacade.submitSessionFeedback(
        sessionId: sessionId,
        feedbackText: feedbackText,
        painLevelBefore: painLevelBefore,
        painLevelAfter: painLevelAfter,
      );

      // Handle the Do result manually using fold
      result.fold(
        onFailure: (error) => emit(TherapyFeedbackState.error(error)),
        onSuccess: (_) => emit(const TherapyFeedbackState.submitted()),
      );
    } catch (e) {
      emit(TherapyFeedbackState.error(e.toString()));
    }
  }

  Future<void> _onResetState(Emitter<TherapyFeedbackState> emit) async {
    emit(const TherapyFeedbackState.initial());
  }
}
