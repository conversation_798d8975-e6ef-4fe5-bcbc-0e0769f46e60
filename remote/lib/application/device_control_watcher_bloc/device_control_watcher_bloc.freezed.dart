// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_control_watcher_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DeviceControlWatcherEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DeviceControlWatcherEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlWatcherEvent()';
  }
}

/// @nodoc
class $DeviceControlWatcherEventCopyWith<$Res> {
  $DeviceControlWatcherEventCopyWith(
      DeviceControlWatcherEvent _, $Res Function(DeviceControlWatcherEvent) __);
}

/// Adds pattern-matching-related methods to [DeviceControlWatcherEvent].
extension DeviceControlWatcherEventPatterns on DeviceControlWatcherEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_TherapyStateChanged value)? therapyStateChanged,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted(_that);
      case _TherapyStateChanged() when therapyStateChanged != null:
        return therapyStateChanged(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_TherapyStateChanged value) therapyStateChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted():
        return watchAllStarted(_that);
      case _TherapyStateChanged():
        return therapyStateChanged(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_TherapyStateChanged value)? therapyStateChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted(_that);
      case _TherapyStateChanged() when therapyStateChanged != null:
        return therapyStateChanged(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Do<RemoteFailure, bool> failureOrState)?
        therapyStateChanged,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted();
      case _TherapyStateChanged() when therapyStateChanged != null:
        return therapyStateChanged(_that.failureOrState);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(Do<RemoteFailure, bool> failureOrState)
        therapyStateChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted():
        return watchAllStarted();
      case _TherapyStateChanged():
        return therapyStateChanged(_that.failureOrState);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Do<RemoteFailure, bool> failureOrState)?
        therapyStateChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted();
      case _TherapyStateChanged() when therapyStateChanged != null:
        return therapyStateChanged(_that.failureOrState);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _WatchAllStarted implements DeviceControlWatcherEvent {
  const _WatchAllStarted();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _WatchAllStarted);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlWatcherEvent.watchAllStarted()';
  }
}

/// @nodoc

class _TherapyStateChanged implements DeviceControlWatcherEvent {
  const _TherapyStateChanged(this.failureOrState);

  final Do<RemoteFailure, bool> failureOrState;

  /// Create a copy of DeviceControlWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TherapyStateChangedCopyWith<_TherapyStateChanged> get copyWith =>
      __$TherapyStateChangedCopyWithImpl<_TherapyStateChanged>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TherapyStateChanged &&
            (identical(other.failureOrState, failureOrState) ||
                other.failureOrState == failureOrState));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrState);

  @override
  String toString() {
    return 'DeviceControlWatcherEvent.therapyStateChanged(failureOrState: $failureOrState)';
  }
}

/// @nodoc
abstract mixin class _$TherapyStateChangedCopyWith<$Res>
    implements $DeviceControlWatcherEventCopyWith<$Res> {
  factory _$TherapyStateChangedCopyWith(_TherapyStateChanged value,
          $Res Function(_TherapyStateChanged) _then) =
      __$TherapyStateChangedCopyWithImpl;
  @useResult
  $Res call({Do<RemoteFailure, bool> failureOrState});
}

/// @nodoc
class __$TherapyStateChangedCopyWithImpl<$Res>
    implements _$TherapyStateChangedCopyWith<$Res> {
  __$TherapyStateChangedCopyWithImpl(this._self, this._then);

  final _TherapyStateChanged _self;
  final $Res Function(_TherapyStateChanged) _then;

  /// Create a copy of DeviceControlWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureOrState = null,
  }) {
    return _then(_TherapyStateChanged(
      null == failureOrState
          ? _self.failureOrState
          : failureOrState // ignore: cast_nullable_to_non_nullable
              as Do<RemoteFailure, bool>,
    ));
  }
}

/// @nodoc
mixin _$DeviceControlWatcherState {
  bool get isTherapyActive;
  Do<RemoteFailure, Unit>? get failureOrSuccessOption;

  /// Create a copy of DeviceControlWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DeviceControlWatcherStateCopyWith<DeviceControlWatcherState> get copyWith =>
      _$DeviceControlWatcherStateCopyWithImpl<DeviceControlWatcherState>(
          this as DeviceControlWatcherState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DeviceControlWatcherState &&
            (identical(other.isTherapyActive, isTherapyActive) ||
                other.isTherapyActive == isTherapyActive) &&
            (identical(other.failureOrSuccessOption, failureOrSuccessOption) ||
                other.failureOrSuccessOption == failureOrSuccessOption));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, isTherapyActive, failureOrSuccessOption);

  @override
  String toString() {
    return 'DeviceControlWatcherState(isTherapyActive: $isTherapyActive, failureOrSuccessOption: $failureOrSuccessOption)';
  }
}

/// @nodoc
abstract mixin class $DeviceControlWatcherStateCopyWith<$Res> {
  factory $DeviceControlWatcherStateCopyWith(DeviceControlWatcherState value,
          $Res Function(DeviceControlWatcherState) _then) =
      _$DeviceControlWatcherStateCopyWithImpl;
  @useResult
  $Res call(
      {bool isTherapyActive, Do<RemoteFailure, Unit>? failureOrSuccessOption});
}

/// @nodoc
class _$DeviceControlWatcherStateCopyWithImpl<$Res>
    implements $DeviceControlWatcherStateCopyWith<$Res> {
  _$DeviceControlWatcherStateCopyWithImpl(this._self, this._then);

  final DeviceControlWatcherState _self;
  final $Res Function(DeviceControlWatcherState) _then;

  /// Create a copy of DeviceControlWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isTherapyActive = null,
    Object? failureOrSuccessOption = freezed,
  }) {
    return _then(_self.copyWith(
      isTherapyActive: null == isTherapyActive
          ? _self.isTherapyActive
          : isTherapyActive // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessOption: freezed == failureOrSuccessOption
          ? _self.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Do<RemoteFailure, Unit>?,
    ));
  }
}

/// Adds pattern-matching-related methods to [DeviceControlWatcherState].
extension DeviceControlWatcherStatePatterns on DeviceControlWatcherState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DeviceControlWatcherState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DeviceControlWatcherState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DeviceControlWatcherState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceControlWatcherState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DeviceControlWatcherState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceControlWatcherState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool isTherapyActive,
            Do<RemoteFailure, Unit>? failureOrSuccessOption)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DeviceControlWatcherState() when $default != null:
        return $default(_that.isTherapyActive, _that.failureOrSuccessOption);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool isTherapyActive,
            Do<RemoteFailure, Unit>? failureOrSuccessOption)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceControlWatcherState():
        return $default(_that.isTherapyActive, _that.failureOrSuccessOption);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool isTherapyActive,
            Do<RemoteFailure, Unit>? failureOrSuccessOption)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceControlWatcherState() when $default != null:
        return $default(_that.isTherapyActive, _that.failureOrSuccessOption);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _DeviceControlWatcherState implements DeviceControlWatcherState {
  const _DeviceControlWatcherState(
      {required this.isTherapyActive, this.failureOrSuccessOption});

  @override
  final bool isTherapyActive;
  @override
  final Do<RemoteFailure, Unit>? failureOrSuccessOption;

  /// Create a copy of DeviceControlWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DeviceControlWatcherStateCopyWith<_DeviceControlWatcherState>
      get copyWith =>
          __$DeviceControlWatcherStateCopyWithImpl<_DeviceControlWatcherState>(
              this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DeviceControlWatcherState &&
            (identical(other.isTherapyActive, isTherapyActive) ||
                other.isTherapyActive == isTherapyActive) &&
            (identical(other.failureOrSuccessOption, failureOrSuccessOption) ||
                other.failureOrSuccessOption == failureOrSuccessOption));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, isTherapyActive, failureOrSuccessOption);

  @override
  String toString() {
    return 'DeviceControlWatcherState(isTherapyActive: $isTherapyActive, failureOrSuccessOption: $failureOrSuccessOption)';
  }
}

/// @nodoc
abstract mixin class _$DeviceControlWatcherStateCopyWith<$Res>
    implements $DeviceControlWatcherStateCopyWith<$Res> {
  factory _$DeviceControlWatcherStateCopyWith(_DeviceControlWatcherState value,
          $Res Function(_DeviceControlWatcherState) _then) =
      __$DeviceControlWatcherStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isTherapyActive, Do<RemoteFailure, Unit>? failureOrSuccessOption});
}

/// @nodoc
class __$DeviceControlWatcherStateCopyWithImpl<$Res>
    implements _$DeviceControlWatcherStateCopyWith<$Res> {
  __$DeviceControlWatcherStateCopyWithImpl(this._self, this._then);

  final _DeviceControlWatcherState _self;
  final $Res Function(_DeviceControlWatcherState) _then;

  /// Create a copy of DeviceControlWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isTherapyActive = null,
    Object? failureOrSuccessOption = freezed,
  }) {
    return _then(_DeviceControlWatcherState(
      isTherapyActive: null == isTherapyActive
          ? _self.isTherapyActive
          : isTherapyActive // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessOption: freezed == failureOrSuccessOption
          ? _self.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Do<RemoteFailure, Unit>?,
    ));
  }
}

// dart format on
