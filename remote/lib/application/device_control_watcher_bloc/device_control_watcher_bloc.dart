import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:doso/doso.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:remote/domain/core/unit.dart';
import 'package:remote/domain/facade/remote_control_facade.dart';
import 'package:remote/domain/failures/remote_failures.dart';

part 'device_control_watcher_event.dart';
part 'device_control_watcher_state.dart';
part 'device_control_watcher_bloc.freezed.dart';

@injectable
class DeviceControlWatcherBloc
    extends Bloc<DeviceControlWatcherEvent, DeviceControlWatcherState> {
  final RemoteControlFacade _remoteRepository;
  StreamSubscription<Do<RemoteFailure, bool>>? _remoteSubscription;

  DeviceControlWatcherBloc(this._remoteRepository)
      : super(DeviceControlWatcherState.initial()) {
    on<_WatchAllStarted>(_onWatchAllStarted);
    on<_TherapyStateChanged>(_onTherapyStateChanged);
  }

  void _onWatchAllStarted(
    _WatchAllStarted event,
    Emitter<DeviceControlWatcherState> emit,
  ) async {
    // First emit the initial state
    emit(DeviceControlWatcherState.initial());

    await _remoteSubscription?.cancel();
    _remoteSubscription = _remoteRepository.watchTherapyState().listen(
      (failureOrTherapyState) {
        if (isClosed) return; // Prevent adding events after close
        add(DeviceControlWatcherEvent.therapyStateChanged(
            failureOrTherapyState));
      },
    );
  }

  void _onTherapyStateChanged(
    _TherapyStateChanged event,
    Emitter<DeviceControlWatcherState> emit,
  ) {
    event.failureOrState.fold(
      onFailure: (failure) => emit(state.copyWith(
        failureOrSuccessOption: Do.failure(failure),
      )),
      onSuccess: (isActive) => emit(state.copyWith(
        isTherapyActive: isActive,
        failureOrSuccessOption: Do.success(Unit()),
      )),
    );
  }

  @override
  Future<void> close() async {
    await _remoteSubscription?.cancel();
    return super.close();
  }
}
