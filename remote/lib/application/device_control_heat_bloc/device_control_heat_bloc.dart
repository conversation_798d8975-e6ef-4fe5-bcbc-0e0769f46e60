import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:remote/domain/facade/remote_control_facade.dart';
import 'package:remote/domain/failures/remote_failures.dart';

part 'device_control_heat_event.dart';
part 'device_control_heat_state.dart';
part 'device_control_heat_bloc.freezed.dart';

@injectable
class DeviceControlHeatBloc
    extends Bloc<DeviceControlHeatEvent, DeviceControlHeatState> {
  final RemoteControlFacade _remoteRepository;

  DeviceControlHeatBloc(this._remoteRepository)
      : super(DeviceControlHeatState.initial()) {
    on<IncreaseHeat>(_onIncreaseHeat);
    on<DecreaseHeat>(_onDecreaseHeat);
  }

  Future<void> _onIncreaseHeat(
    IncreaseHeat event,
    Emitter<DeviceControlHeatState> emit,
  ) async {
    final failureOrSuccess = await _remoteRepository.increaseHeat();
    failureOrSuccess.fold(
      onFailure: (failure) =>
          emit(DeviceControlHeatState.changeHeatLevelFailure(failure)),
      onSuccess: (_) => emit(DeviceControlHeatState.changeHeatLevelSuccess()),
    );
  }

  Future<void> _onDecreaseHeat(
    DecreaseHeat event,
    Emitter<DeviceControlHeatState> emit,
  ) async {
    final failureOrSuccess = await _remoteRepository.decreaseHeat();
    failureOrSuccess.fold(
      onFailure: (failure) =>
          emit(DeviceControlHeatState.changeHeatLevelFailure(failure)),
      onSuccess: (_) => emit(DeviceControlHeatState.changeHeatLevelSuccess()),
    );
  }
}
