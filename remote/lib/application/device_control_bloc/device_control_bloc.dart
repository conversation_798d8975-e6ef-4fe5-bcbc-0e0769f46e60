import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:remote/domain/facade/remote_control_facade.dart';
import 'package:remote/domain/failures/remote_failures.dart';
import 'package:doso/doso.dart';

part 'device_control_event.dart';
part 'device_control_state.dart';
part 'device_control_bloc.freezed.dart';

@injectable
class DeviceControlBloc extends Bloc<DeviceControlEvent, DeviceControlState> {
  final RemoteControlFacade _remoteRepository;
  StreamSubscription<Do<RemoteFailure, bool>>? _therapyStateSubscription;

  DeviceControlBloc(this._remoteRepository)
      : super(const DeviceControlState.initial()) {
    on<PowerOff>(_onPowerOff);
    on<ToggleTherapy>(_onToggleTherapy);
    on<UnpairDeviceEvent>(_onUnpairDevice);
    on<TherapyStateChangedEvent>(_onTherapyStateChanged);
    on<ActionFailureEvent>(_onActionFailure);

    _watchTherapyState();
  }

  void _watchTherapyState() {
    _therapyStateSubscription?.cancel();
    _therapyStateSubscription = _remoteRepository.watchTherapyState().listen(
      (failureOrState) {
        failureOrState.fold(
          onFailure: (failure) => add(ActionFailureEvent(failure)),
          onSuccess: (isActive) => add(TherapyStateChangedEvent(isActive)),
        );
      },
    );
  }

  Future<void> _onPowerOff(
    PowerOff event,
    Emitter<DeviceControlState> emit,
  ) async {
    final failureOrSuccess = await _remoteRepository.powerOffDevice();
    failureOrSuccess.fold(
      onFailure: (failure) => emit(DeviceControlState.actionFailure(failure)),
      onSuccess: (_) => emit(const DeviceControlState.actionSuccess()),
    );
  }

  Future<void> _onToggleTherapy(
    ToggleTherapy event,
    Emitter<DeviceControlState> emit,
  ) async {
    final failureOrSuccess = await _remoteRepository.toggleTherapy();
    failureOrSuccess.fold(
      onFailure: (failure) => emit(DeviceControlState.actionFailure(failure)),
      onSuccess: (_) => emit(const DeviceControlState.actionSuccess()),
    );
  }

  void _onTherapyStateChanged(
    TherapyStateChangedEvent event,
    Emitter<DeviceControlState> emit,
  ) {
    emit(DeviceControlState.therapyStateChanged(event.isActive));
  }

  void _onActionFailure(
    ActionFailureEvent event,
    Emitter<DeviceControlState> emit,
  ) {
    emit(DeviceControlState.actionFailure(event.failure));
  }

  Future<void> _onUnpairDevice(
    UnpairDeviceEvent event,
    Emitter<DeviceControlState> emit,
  ) async {
    print('🎯 DeviceControlBloc: Starting unpair process');
    emit(const DeviceControlState.unpairingDevice());

    final result = await _remoteRepository.unpairDevice();
    result.fold(
      onFailure: (failure) {
        print('❌ DeviceControlBloc: Unpair failed with failure: $failure');
        emit(DeviceControlState.actionFailure(failure));
      },
      onSuccess: (_) {
        print(
            '✅ DeviceControlBloc: Unpair successful, emitting DeviceUnpaired state');
        emit(const DeviceControlState.deviceUnpaired());
      },
    );
  }

  @override
  Future<void> close() {
    _therapyStateSubscription?.cancel();
    return super.close();
  }
}
