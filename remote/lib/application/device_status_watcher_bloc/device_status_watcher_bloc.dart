import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:doso/doso.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:remote/domain/core/unit.dart';
import 'package:remote/domain/facade/remote_control_facade.dart';
import 'package:remote/domain/failures/remote_failures.dart';
import 'package:remote/domain/model/battery_level_model.dart';
import 'package:remote/domain/model/device_info_model.dart';
import 'package:remote/domain/model/device_model.dart';

part 'device_status_watcher_event.dart';
part 'device_status_watcher_state.dart';
part 'device_status_watcher_bloc.freezed.dart';

@injectable
class DeviceStatusWatcherBloc
    extends Bloc<DeviceStatusWatcherEvent, DeviceStatusWatcherState> {
  final RemoteControlFacade _deviceStatusFacade;
  StreamSubscription<Do<RemoteFailure, DeviceModel>>?
      _deviceStatusStreamSubscription;
  DeviceStatusWatcherBloc(this._deviceStatusFacade)
      : super(DeviceStatusWatcherState.initial()) {
    on<_WatchAllStarted>(_onStarted);
    on<_DataReceived>(_onDataReceived);
  }
  Future<void> _onStarted(
      _WatchAllStarted event, Emitter<DeviceStatusWatcherState> emit) async {
    await _deviceStatusStreamSubscription?.cancel();
    _deviceStatusStreamSubscription =
        _deviceStatusFacade.getDeviceStatus().listen(
      (failureOrDeviceModel) {
        add(DeviceStatusWatcherEvent.dataReceived(failureOrDeviceModel));
      },
    );
  }

  void _onDataReceived(
      _DataReceived event, Emitter<DeviceStatusWatcherState> emit) {
    event.failureOrDeviceModel.fold(
      onFailure: (failure) => emit(DeviceStatusWatcherState.initial()),
      onSuccess: (deviceModel) {
        final newState = state.copyWith(
          deviceInformation: deviceModel.deviceInfo,
          isDeviceConnected: deviceModel.isDeviceConnected ?? false,
          isDeviceOn: deviceModel.isDeviceOn,
          isDeviceReady: deviceModel.isDeviceReady ?? false,
          isDeviceError: deviceModel.isDeviceError ?? false,
          isDeviceOff: deviceModel.isDeviceOff ?? false,
          isDeviceDisconnected: deviceModel.isDeviceDisconnected ?? false,
          failureOrSuccessOption: Do.success(Unit()),
        );
        emit(newState);
      },
    );
  }

  @override
  Future<void> close() async {
    await _deviceStatusStreamSubscription?.cancel();
    return super.close();
  }
}
