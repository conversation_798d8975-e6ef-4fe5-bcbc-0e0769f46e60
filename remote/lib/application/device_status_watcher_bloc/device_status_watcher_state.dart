part of 'device_status_watcher_bloc.dart';

@freezed
abstract class DeviceStatusWatcherState with _$DeviceStatusWatcherState {
  const factory DeviceStatusWatcherState({
    required DeviceInfoModel deviceInformation,
    required bool isDeviceConnected,
    required bool isDeviceOn,
    required bool isDeviceReady,
    required bool isDeviceError,
    required bool isDeviceBusy,
    required bool isDeviceOff,
    required bool isDeviceDisconnected,
    Do<RemoteFailure, Unit>? failureOrSuccessOption,
  }) = _DeviceStatusWatcherState;

  factory DeviceStatusWatcherState.initial() => DeviceStatusWatcherState(
        deviceInformation: DeviceInfoModel(
          deviceName: '',
          deviceAddress: '',
          deviceType: '',
          deviceId: '',
          batteryLevel: BatteryLevelModel(batteryLevel: 0),
        ),
        isDeviceConnected: false,
        isDeviceOn: false,
        isDeviceReady: false,
        isDeviceError: false,
        isDeviceBusy: false,
        isDeviceOff: false,
        isDeviceDisconnected: false,
        failureOrSuccessOption: null,
      );
}
