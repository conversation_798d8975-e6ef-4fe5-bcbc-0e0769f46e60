// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_control_heat_watcher_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DeviceControlHeatWatcherEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DeviceControlHeatWatcherEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlHeatWatcherEvent()';
  }
}

/// @nodoc
class $DeviceControlHeatWatcherEventCopyWith<$Res> {
  $DeviceControlHeatWatcherEventCopyWith(DeviceControlHeatWatcherEvent _,
      $Res Function(DeviceControlHeatWatcherEvent) __);
}

/// Adds pattern-matching-related methods to [DeviceControlHeatWatcherEvent].
extension DeviceControlHeatWatcherEventPatterns
    on DeviceControlHeatWatcherEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_HeatLevelChanged value)? heatLevelChanged,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted(_that);
      case _HeatLevelChanged() when heatLevelChanged != null:
        return heatLevelChanged(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_HeatLevelChanged value) heatLevelChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted():
        return watchAllStarted(_that);
      case _HeatLevelChanged():
        return heatLevelChanged(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_HeatLevelChanged value)? heatLevelChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted(_that);
      case _HeatLevelChanged() when heatLevelChanged != null:
        return heatLevelChanged(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Do<RemoteFailure, HeatLevelModel> failureOrHeatLevel)?
        heatLevelChanged,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted();
      case _HeatLevelChanged() when heatLevelChanged != null:
        return heatLevelChanged(_that.failureOrHeatLevel);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(
            Do<RemoteFailure, HeatLevelModel> failureOrHeatLevel)
        heatLevelChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted():
        return watchAllStarted();
      case _HeatLevelChanged():
        return heatLevelChanged(_that.failureOrHeatLevel);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Do<RemoteFailure, HeatLevelModel> failureOrHeatLevel)?
        heatLevelChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted();
      case _HeatLevelChanged() when heatLevelChanged != null:
        return heatLevelChanged(_that.failureOrHeatLevel);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _WatchAllStarted implements DeviceControlHeatWatcherEvent {
  const _WatchAllStarted();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _WatchAllStarted);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlHeatWatcherEvent.watchAllStarted()';
  }
}

/// @nodoc

class _HeatLevelChanged implements DeviceControlHeatWatcherEvent {
  const _HeatLevelChanged(this.failureOrHeatLevel);

  final Do<RemoteFailure, HeatLevelModel> failureOrHeatLevel;

  /// Create a copy of DeviceControlHeatWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$HeatLevelChangedCopyWith<_HeatLevelChanged> get copyWith =>
      __$HeatLevelChangedCopyWithImpl<_HeatLevelChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _HeatLevelChanged &&
            (identical(other.failureOrHeatLevel, failureOrHeatLevel) ||
                other.failureOrHeatLevel == failureOrHeatLevel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrHeatLevel);

  @override
  String toString() {
    return 'DeviceControlHeatWatcherEvent.heatLevelChanged(failureOrHeatLevel: $failureOrHeatLevel)';
  }
}

/// @nodoc
abstract mixin class _$HeatLevelChangedCopyWith<$Res>
    implements $DeviceControlHeatWatcherEventCopyWith<$Res> {
  factory _$HeatLevelChangedCopyWith(
          _HeatLevelChanged value, $Res Function(_HeatLevelChanged) _then) =
      __$HeatLevelChangedCopyWithImpl;
  @useResult
  $Res call({Do<RemoteFailure, HeatLevelModel> failureOrHeatLevel});
}

/// @nodoc
class __$HeatLevelChangedCopyWithImpl<$Res>
    implements _$HeatLevelChangedCopyWith<$Res> {
  __$HeatLevelChangedCopyWithImpl(this._self, this._then);

  final _HeatLevelChanged _self;
  final $Res Function(_HeatLevelChanged) _then;

  /// Create a copy of DeviceControlHeatWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureOrHeatLevel = null,
  }) {
    return _then(_HeatLevelChanged(
      null == failureOrHeatLevel
          ? _self.failureOrHeatLevel
          : failureOrHeatLevel // ignore: cast_nullable_to_non_nullable
              as Do<RemoteFailure, HeatLevelModel>,
    ));
  }
}

/// @nodoc
mixin _$DeviceControlHeatWatcherState {
  int get selectedHeatLevel;
  int get actualHeatLevel;
  Do<RemoteFailure, Unit>? get failureOrSuccessOption;

  /// Create a copy of DeviceControlHeatWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DeviceControlHeatWatcherStateCopyWith<DeviceControlHeatWatcherState>
      get copyWith => _$DeviceControlHeatWatcherStateCopyWithImpl<
              DeviceControlHeatWatcherState>(
          this as DeviceControlHeatWatcherState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DeviceControlHeatWatcherState &&
            (identical(other.selectedHeatLevel, selectedHeatLevel) ||
                other.selectedHeatLevel == selectedHeatLevel) &&
            (identical(other.actualHeatLevel, actualHeatLevel) ||
                other.actualHeatLevel == actualHeatLevel) &&
            (identical(other.failureOrSuccessOption, failureOrSuccessOption) ||
                other.failureOrSuccessOption == failureOrSuccessOption));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, selectedHeatLevel, actualHeatLevel, failureOrSuccessOption);

  @override
  String toString() {
    return 'DeviceControlHeatWatcherState(selectedHeatLevel: $selectedHeatLevel, actualHeatLevel: $actualHeatLevel, failureOrSuccessOption: $failureOrSuccessOption)';
  }
}

/// @nodoc
abstract mixin class $DeviceControlHeatWatcherStateCopyWith<$Res> {
  factory $DeviceControlHeatWatcherStateCopyWith(
          DeviceControlHeatWatcherState value,
          $Res Function(DeviceControlHeatWatcherState) _then) =
      _$DeviceControlHeatWatcherStateCopyWithImpl;
  @useResult
  $Res call(
      {int selectedHeatLevel,
      int actualHeatLevel,
      Do<RemoteFailure, Unit>? failureOrSuccessOption});
}

/// @nodoc
class _$DeviceControlHeatWatcherStateCopyWithImpl<$Res>
    implements $DeviceControlHeatWatcherStateCopyWith<$Res> {
  _$DeviceControlHeatWatcherStateCopyWithImpl(this._self, this._then);

  final DeviceControlHeatWatcherState _self;
  final $Res Function(DeviceControlHeatWatcherState) _then;

  /// Create a copy of DeviceControlHeatWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedHeatLevel = null,
    Object? actualHeatLevel = null,
    Object? failureOrSuccessOption = freezed,
  }) {
    return _then(_self.copyWith(
      selectedHeatLevel: null == selectedHeatLevel
          ? _self.selectedHeatLevel
          : selectedHeatLevel // ignore: cast_nullable_to_non_nullable
              as int,
      actualHeatLevel: null == actualHeatLevel
          ? _self.actualHeatLevel
          : actualHeatLevel // ignore: cast_nullable_to_non_nullable
              as int,
      failureOrSuccessOption: freezed == failureOrSuccessOption
          ? _self.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Do<RemoteFailure, Unit>?,
    ));
  }
}

/// Adds pattern-matching-related methods to [DeviceControlHeatWatcherState].
extension DeviceControlHeatWatcherStatePatterns
    on DeviceControlHeatWatcherState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DeviceControlHeatWatcherState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DeviceControlHeatWatcherState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DeviceControlHeatWatcherState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceControlHeatWatcherState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DeviceControlHeatWatcherState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceControlHeatWatcherState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(int selectedHeatLevel, int actualHeatLevel,
            Do<RemoteFailure, Unit>? failureOrSuccessOption)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DeviceControlHeatWatcherState() when $default != null:
        return $default(_that.selectedHeatLevel, _that.actualHeatLevel,
            _that.failureOrSuccessOption);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(int selectedHeatLevel, int actualHeatLevel,
            Do<RemoteFailure, Unit>? failureOrSuccessOption)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceControlHeatWatcherState():
        return $default(_that.selectedHeatLevel, _that.actualHeatLevel,
            _that.failureOrSuccessOption);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(int selectedHeatLevel, int actualHeatLevel,
            Do<RemoteFailure, Unit>? failureOrSuccessOption)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceControlHeatWatcherState() when $default != null:
        return $default(_that.selectedHeatLevel, _that.actualHeatLevel,
            _that.failureOrSuccessOption);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _DeviceControlHeatWatcherState extends DeviceControlHeatWatcherState {
  const _DeviceControlHeatWatcherState(
      {required this.selectedHeatLevel,
      required this.actualHeatLevel,
      required this.failureOrSuccessOption})
      : super._();

  @override
  final int selectedHeatLevel;
  @override
  final int actualHeatLevel;
  @override
  final Do<RemoteFailure, Unit>? failureOrSuccessOption;

  /// Create a copy of DeviceControlHeatWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DeviceControlHeatWatcherStateCopyWith<_DeviceControlHeatWatcherState>
      get copyWith => __$DeviceControlHeatWatcherStateCopyWithImpl<
          _DeviceControlHeatWatcherState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DeviceControlHeatWatcherState &&
            (identical(other.selectedHeatLevel, selectedHeatLevel) ||
                other.selectedHeatLevel == selectedHeatLevel) &&
            (identical(other.actualHeatLevel, actualHeatLevel) ||
                other.actualHeatLevel == actualHeatLevel) &&
            (identical(other.failureOrSuccessOption, failureOrSuccessOption) ||
                other.failureOrSuccessOption == failureOrSuccessOption));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, selectedHeatLevel, actualHeatLevel, failureOrSuccessOption);

  @override
  String toString() {
    return 'DeviceControlHeatWatcherState(selectedHeatLevel: $selectedHeatLevel, actualHeatLevel: $actualHeatLevel, failureOrSuccessOption: $failureOrSuccessOption)';
  }
}

/// @nodoc
abstract mixin class _$DeviceControlHeatWatcherStateCopyWith<$Res>
    implements $DeviceControlHeatWatcherStateCopyWith<$Res> {
  factory _$DeviceControlHeatWatcherStateCopyWith(
          _DeviceControlHeatWatcherState value,
          $Res Function(_DeviceControlHeatWatcherState) _then) =
      __$DeviceControlHeatWatcherStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int selectedHeatLevel,
      int actualHeatLevel,
      Do<RemoteFailure, Unit>? failureOrSuccessOption});
}

/// @nodoc
class __$DeviceControlHeatWatcherStateCopyWithImpl<$Res>
    implements _$DeviceControlHeatWatcherStateCopyWith<$Res> {
  __$DeviceControlHeatWatcherStateCopyWithImpl(this._self, this._then);

  final _DeviceControlHeatWatcherState _self;
  final $Res Function(_DeviceControlHeatWatcherState) _then;

  /// Create a copy of DeviceControlHeatWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedHeatLevel = null,
    Object? actualHeatLevel = null,
    Object? failureOrSuccessOption = freezed,
  }) {
    return _then(_DeviceControlHeatWatcherState(
      selectedHeatLevel: null == selectedHeatLevel
          ? _self.selectedHeatLevel
          : selectedHeatLevel // ignore: cast_nullable_to_non_nullable
              as int,
      actualHeatLevel: null == actualHeatLevel
          ? _self.actualHeatLevel
          : actualHeatLevel // ignore: cast_nullable_to_non_nullable
              as int,
      failureOrSuccessOption: freezed == failureOrSuccessOption
          ? _self.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Do<RemoteFailure, Unit>?,
    ));
  }
}

// dart format on
