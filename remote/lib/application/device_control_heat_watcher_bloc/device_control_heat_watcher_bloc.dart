import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:doso/doso.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:remote/domain/core/unit.dart';
import 'package:remote/domain/facade/remote_control_facade.dart';
import 'package:remote/domain/failures/remote_failures.dart';
import 'package:remote/domain/model/heat_level.dart';

part 'device_control_heat_watcher_event.dart';
part 'device_control_heat_watcher_state.dart';
part 'device_control_heat_watcher_bloc.freezed.dart';

@injectable
class DeviceControlHeatWatcherBloc
    extends Bloc<DeviceControlHeatWatcherEvent, DeviceControlHeatWatcherState> {
  final RemoteControlFacade _remoteRepository;
  StreamSubscription<Do<RemoteFailure, HeatLevelModel>>? _remoteSubscription;

  DeviceControlHeatWatcherBloc(this._remoteRepository)
      : super(DeviceControlHeatWatcherState.initial()) {
    on<_WatchAllStarted>(_onWatchAllStarted);
    on<_HeatLevelChanged>(_onHeatLevelChanged);
  }

  void _onWatchAllStarted(_WatchAllStarted event,
      Emitter<DeviceControlHeatWatcherState> emit) async {
    // First emit the initial state with None
    emit(DeviceControlHeatWatcherState.initial());

    await _remoteSubscription?.cancel();
    _remoteSubscription = _remoteRepository.watchHeatLevel().listen(
      (failureOrHeatLevel) {
        if (isClosed) return; // Prevent adding events after close
        add(DeviceControlHeatWatcherEvent.heatLevelChanged(failureOrHeatLevel));
      },
    );
  }

  void _onHeatLevelChanged(
      _HeatLevelChanged event, Emitter<DeviceControlHeatWatcherState> emit) {
    event.failureOrHeatLevel.fold(
      onFailure: (failure) => emit(state.copyWith(
        failureOrSuccessOption: Do.failure(failure),
      )),
      onSuccess: (heatLevel) => emit(state.copyWith(
        actualHeatLevel: heatLevel.actualHeatLevel,
        selectedHeatLevel: heatLevel.selectedHeatLevel,
        failureOrSuccessOption: Do.success(unit),
      )),
    );
  }

  @override
  Future<void> close() async {
    await _remoteSubscription?.cancel(); // Ensure subscription cleanup
    return super.close();
  }
}
