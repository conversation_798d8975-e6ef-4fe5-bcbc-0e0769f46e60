import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../domain/facade/remote_control_facade.dart';
import '../../domain/failures/remote_failures.dart';

part 'device_control_tens_event.dart';
part 'device_control_tens_state.dart';
part 'device_control_tens_bloc.freezed.dart';

@injectable
class DeviceControlTensBloc
    extends Bloc<DeviceControlTensEvent, DeviceControlTensState> {
  final RemoteControlFacade _remoteRepository;

  DeviceControlTensBloc(this._remoteRepository)
      : super(DeviceControlTensState.initial()) {
    on<IncreaseTens>(_onIncreaseTens);
    on<DecreaseTens>(_onDecreaseTens);
    on<ChangeMode>(_onChangeMode);
  }

  Future<void> _onIncreaseTens(
    IncreaseTens event,
    Emitter<DeviceControlTensState> emit,
  ) async {
    final failureOrSuccess = await _remoteRepository.increaseTens();
    failureOrSuccess.fold(
      onFailure: (failure) =>
          emit(DeviceControlTensState.changeTensLevelFailure(failure)),
      onSuccess: (_) => emit(DeviceControlTensState.changeTensLevelSuccess()),
    );
  }

  Future<void> _onDecreaseTens(
    DecreaseTens event,
    Emitter<DeviceControlTensState> emit,
  ) async {
    final failureOrSuccess = await _remoteRepository.decreaseTens();
    failureOrSuccess.fold(
      onFailure: (failure) =>
          emit(DeviceControlTensState.changeTensLevelFailure(failure)),
      onSuccess: (_) => emit(DeviceControlTensState.changeTensLevelSuccess()),
    );
  }

  Future<void> _onChangeMode(
    ChangeMode event,
    Emitter<DeviceControlTensState> emit,
  ) async {
    final failureOrSuccess = await _remoteRepository.changeMode(event.mode);
    failureOrSuccess.fold(
      onFailure: (failure) =>
          emit(DeviceControlTensState.changeModeFailure(failure)),
      onSuccess: (_) => emit(DeviceControlTensState.changeModeSuccess()),
    );
  }
}
