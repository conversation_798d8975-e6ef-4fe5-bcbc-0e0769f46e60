part of 'device_control_tens_watcher_bloc.dart';

@freezed
abstract class DeviceControlTensWatcherState
    with _$DeviceControlTensWatcherState {
  const factory DeviceControlTensWatcherState({
    required int selectedTensLevel,
    required int actualTensLevel,
    required int selectedMode,
    int? applyingLevel,
    Do<RemoteFailure, Unit>? failureOrSuccessOption,
  }) = _DeviceControlTensWatcherState;

  factory DeviceControlTensWatcherState.initial() =>
      DeviceControlTensWatcherState(
        selectedTensLevel: 0,
        actualTensLevel: 0,
        selectedMode: 1,
        applyingLevel: null,
        failureOrSuccessOption: null,
      );
}
