// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_control_tens_watcher_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DeviceControlTensWatcherEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DeviceControlTensWatcherEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlTensWatcherEvent()';
  }
}

/// @nodoc
class $DeviceControlTensWatcherEventCopyWith<$Res> {
  $DeviceControlTensWatcherEventCopyWith(DeviceControlTensWatcherEvent _,
      $Res Function(DeviceControlTensWatcherEvent) __);
}

/// Adds pattern-matching-related methods to [DeviceControlTensWatcherEvent].
extension DeviceControlTensWatcherEventPatterns
    on DeviceControlTensWatcherEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_TensLevelChanged value)? tensLevelChanged,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted(_that);
      case _TensLevelChanged() when tensLevelChanged != null:
        return tensLevelChanged(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_TensLevelChanged value) tensLevelChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted():
        return watchAllStarted(_that);
      case _TensLevelChanged():
        return tensLevelChanged(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_TensLevelChanged value)? tensLevelChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted(_that);
      case _TensLevelChanged() when tensLevelChanged != null:
        return tensLevelChanged(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Do<RemoteFailure, TensLevelModel> failureOrTensLevel)?
        tensLevelChanged,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted();
      case _TensLevelChanged() when tensLevelChanged != null:
        return tensLevelChanged(_that.failureOrTensLevel);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(
            Do<RemoteFailure, TensLevelModel> failureOrTensLevel)
        tensLevelChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted():
        return watchAllStarted();
      case _TensLevelChanged():
        return tensLevelChanged(_that.failureOrTensLevel);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Do<RemoteFailure, TensLevelModel> failureOrTensLevel)?
        tensLevelChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted();
      case _TensLevelChanged() when tensLevelChanged != null:
        return tensLevelChanged(_that.failureOrTensLevel);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _WatchAllStarted implements DeviceControlTensWatcherEvent {
  const _WatchAllStarted();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _WatchAllStarted);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlTensWatcherEvent.watchAllStarted()';
  }
}

/// @nodoc

class _TensLevelChanged implements DeviceControlTensWatcherEvent {
  const _TensLevelChanged(this.failureOrTensLevel);

  final Do<RemoteFailure, TensLevelModel> failureOrTensLevel;

  /// Create a copy of DeviceControlTensWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TensLevelChangedCopyWith<_TensLevelChanged> get copyWith =>
      __$TensLevelChangedCopyWithImpl<_TensLevelChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TensLevelChanged &&
            (identical(other.failureOrTensLevel, failureOrTensLevel) ||
                other.failureOrTensLevel == failureOrTensLevel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrTensLevel);

  @override
  String toString() {
    return 'DeviceControlTensWatcherEvent.tensLevelChanged(failureOrTensLevel: $failureOrTensLevel)';
  }
}

/// @nodoc
abstract mixin class _$TensLevelChangedCopyWith<$Res>
    implements $DeviceControlTensWatcherEventCopyWith<$Res> {
  factory _$TensLevelChangedCopyWith(
          _TensLevelChanged value, $Res Function(_TensLevelChanged) _then) =
      __$TensLevelChangedCopyWithImpl;
  @useResult
  $Res call({Do<RemoteFailure, TensLevelModel> failureOrTensLevel});
}

/// @nodoc
class __$TensLevelChangedCopyWithImpl<$Res>
    implements _$TensLevelChangedCopyWith<$Res> {
  __$TensLevelChangedCopyWithImpl(this._self, this._then);

  final _TensLevelChanged _self;
  final $Res Function(_TensLevelChanged) _then;

  /// Create a copy of DeviceControlTensWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureOrTensLevel = null,
  }) {
    return _then(_TensLevelChanged(
      null == failureOrTensLevel
          ? _self.failureOrTensLevel
          : failureOrTensLevel // ignore: cast_nullable_to_non_nullable
              as Do<RemoteFailure, TensLevelModel>,
    ));
  }
}

/// @nodoc
mixin _$DeviceControlTensWatcherState {
  int get selectedTensLevel;
  int get actualTensLevel;
  int get selectedMode;
  int? get applyingLevel;
  Do<RemoteFailure, Unit>? get failureOrSuccessOption;

  /// Create a copy of DeviceControlTensWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DeviceControlTensWatcherStateCopyWith<DeviceControlTensWatcherState>
      get copyWith => _$DeviceControlTensWatcherStateCopyWithImpl<
              DeviceControlTensWatcherState>(
          this as DeviceControlTensWatcherState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DeviceControlTensWatcherState &&
            (identical(other.selectedTensLevel, selectedTensLevel) ||
                other.selectedTensLevel == selectedTensLevel) &&
            (identical(other.actualTensLevel, actualTensLevel) ||
                other.actualTensLevel == actualTensLevel) &&
            (identical(other.selectedMode, selectedMode) ||
                other.selectedMode == selectedMode) &&
            (identical(other.applyingLevel, applyingLevel) ||
                other.applyingLevel == applyingLevel) &&
            (identical(other.failureOrSuccessOption, failureOrSuccessOption) ||
                other.failureOrSuccessOption == failureOrSuccessOption));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedTensLevel,
      actualTensLevel, selectedMode, applyingLevel, failureOrSuccessOption);

  @override
  String toString() {
    return 'DeviceControlTensWatcherState(selectedTensLevel: $selectedTensLevel, actualTensLevel: $actualTensLevel, selectedMode: $selectedMode, applyingLevel: $applyingLevel, failureOrSuccessOption: $failureOrSuccessOption)';
  }
}

/// @nodoc
abstract mixin class $DeviceControlTensWatcherStateCopyWith<$Res> {
  factory $DeviceControlTensWatcherStateCopyWith(
          DeviceControlTensWatcherState value,
          $Res Function(DeviceControlTensWatcherState) _then) =
      _$DeviceControlTensWatcherStateCopyWithImpl;
  @useResult
  $Res call(
      {int selectedTensLevel,
      int actualTensLevel,
      int selectedMode,
      int? applyingLevel,
      Do<RemoteFailure, Unit>? failureOrSuccessOption});
}

/// @nodoc
class _$DeviceControlTensWatcherStateCopyWithImpl<$Res>
    implements $DeviceControlTensWatcherStateCopyWith<$Res> {
  _$DeviceControlTensWatcherStateCopyWithImpl(this._self, this._then);

  final DeviceControlTensWatcherState _self;
  final $Res Function(DeviceControlTensWatcherState) _then;

  /// Create a copy of DeviceControlTensWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedTensLevel = null,
    Object? actualTensLevel = null,
    Object? selectedMode = null,
    Object? applyingLevel = freezed,
    Object? failureOrSuccessOption = freezed,
  }) {
    return _then(_self.copyWith(
      selectedTensLevel: null == selectedTensLevel
          ? _self.selectedTensLevel
          : selectedTensLevel // ignore: cast_nullable_to_non_nullable
              as int,
      actualTensLevel: null == actualTensLevel
          ? _self.actualTensLevel
          : actualTensLevel // ignore: cast_nullable_to_non_nullable
              as int,
      selectedMode: null == selectedMode
          ? _self.selectedMode
          : selectedMode // ignore: cast_nullable_to_non_nullable
              as int,
      applyingLevel: freezed == applyingLevel
          ? _self.applyingLevel
          : applyingLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      failureOrSuccessOption: freezed == failureOrSuccessOption
          ? _self.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Do<RemoteFailure, Unit>?,
    ));
  }
}

/// Adds pattern-matching-related methods to [DeviceControlTensWatcherState].
extension DeviceControlTensWatcherStatePatterns
    on DeviceControlTensWatcherState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DeviceControlTensWatcherState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DeviceControlTensWatcherState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DeviceControlTensWatcherState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceControlTensWatcherState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DeviceControlTensWatcherState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceControlTensWatcherState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            int selectedTensLevel,
            int actualTensLevel,
            int selectedMode,
            int? applyingLevel,
            Do<RemoteFailure, Unit>? failureOrSuccessOption)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DeviceControlTensWatcherState() when $default != null:
        return $default(
            _that.selectedTensLevel,
            _that.actualTensLevel,
            _that.selectedMode,
            _that.applyingLevel,
            _that.failureOrSuccessOption);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            int selectedTensLevel,
            int actualTensLevel,
            int selectedMode,
            int? applyingLevel,
            Do<RemoteFailure, Unit>? failureOrSuccessOption)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceControlTensWatcherState():
        return $default(
            _that.selectedTensLevel,
            _that.actualTensLevel,
            _that.selectedMode,
            _that.applyingLevel,
            _that.failureOrSuccessOption);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            int selectedTensLevel,
            int actualTensLevel,
            int selectedMode,
            int? applyingLevel,
            Do<RemoteFailure, Unit>? failureOrSuccessOption)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceControlTensWatcherState() when $default != null:
        return $default(
            _that.selectedTensLevel,
            _that.actualTensLevel,
            _that.selectedMode,
            _that.applyingLevel,
            _that.failureOrSuccessOption);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _DeviceControlTensWatcherState implements DeviceControlTensWatcherState {
  const _DeviceControlTensWatcherState(
      {required this.selectedTensLevel,
      required this.actualTensLevel,
      required this.selectedMode,
      this.applyingLevel,
      this.failureOrSuccessOption});

  @override
  final int selectedTensLevel;
  @override
  final int actualTensLevel;
  @override
  final int selectedMode;
  @override
  final int? applyingLevel;
  @override
  final Do<RemoteFailure, Unit>? failureOrSuccessOption;

  /// Create a copy of DeviceControlTensWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DeviceControlTensWatcherStateCopyWith<_DeviceControlTensWatcherState>
      get copyWith => __$DeviceControlTensWatcherStateCopyWithImpl<
          _DeviceControlTensWatcherState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DeviceControlTensWatcherState &&
            (identical(other.selectedTensLevel, selectedTensLevel) ||
                other.selectedTensLevel == selectedTensLevel) &&
            (identical(other.actualTensLevel, actualTensLevel) ||
                other.actualTensLevel == actualTensLevel) &&
            (identical(other.selectedMode, selectedMode) ||
                other.selectedMode == selectedMode) &&
            (identical(other.applyingLevel, applyingLevel) ||
                other.applyingLevel == applyingLevel) &&
            (identical(other.failureOrSuccessOption, failureOrSuccessOption) ||
                other.failureOrSuccessOption == failureOrSuccessOption));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedTensLevel,
      actualTensLevel, selectedMode, applyingLevel, failureOrSuccessOption);

  @override
  String toString() {
    return 'DeviceControlTensWatcherState(selectedTensLevel: $selectedTensLevel, actualTensLevel: $actualTensLevel, selectedMode: $selectedMode, applyingLevel: $applyingLevel, failureOrSuccessOption: $failureOrSuccessOption)';
  }
}

/// @nodoc
abstract mixin class _$DeviceControlTensWatcherStateCopyWith<$Res>
    implements $DeviceControlTensWatcherStateCopyWith<$Res> {
  factory _$DeviceControlTensWatcherStateCopyWith(
          _DeviceControlTensWatcherState value,
          $Res Function(_DeviceControlTensWatcherState) _then) =
      __$DeviceControlTensWatcherStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int selectedTensLevel,
      int actualTensLevel,
      int selectedMode,
      int? applyingLevel,
      Do<RemoteFailure, Unit>? failureOrSuccessOption});
}

/// @nodoc
class __$DeviceControlTensWatcherStateCopyWithImpl<$Res>
    implements _$DeviceControlTensWatcherStateCopyWith<$Res> {
  __$DeviceControlTensWatcherStateCopyWithImpl(this._self, this._then);

  final _DeviceControlTensWatcherState _self;
  final $Res Function(_DeviceControlTensWatcherState) _then;

  /// Create a copy of DeviceControlTensWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedTensLevel = null,
    Object? actualTensLevel = null,
    Object? selectedMode = null,
    Object? applyingLevel = freezed,
    Object? failureOrSuccessOption = freezed,
  }) {
    return _then(_DeviceControlTensWatcherState(
      selectedTensLevel: null == selectedTensLevel
          ? _self.selectedTensLevel
          : selectedTensLevel // ignore: cast_nullable_to_non_nullable
              as int,
      actualTensLevel: null == actualTensLevel
          ? _self.actualTensLevel
          : actualTensLevel // ignore: cast_nullable_to_non_nullable
              as int,
      selectedMode: null == selectedMode
          ? _self.selectedMode
          : selectedMode // ignore: cast_nullable_to_non_nullable
              as int,
      applyingLevel: freezed == applyingLevel
          ? _self.applyingLevel
          : applyingLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      failureOrSuccessOption: freezed == failureOrSuccessOption
          ? _self.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Do<RemoteFailure, Unit>?,
    ));
  }
}

// dart format on
