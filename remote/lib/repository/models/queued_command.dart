import 'dart:async';

import 'package:doso/doso.dart';
import 'package:remote/domain/core/unit.dart';
import 'package:remote/domain/failures/remote_failures.dart';
import 'package:remote/domain/model/commands_model.dart';

/// Represents a command waiting to be processed by the Bluetooth system
///
/// Contains the command object, completion handler, and retry tracking
class QueuedCommand {
  /// The command to be sent to the device
  final Commands command;

  /// Completer to signal when the command has been processed
  final Completer<Do<RemoteFailure, Unit>> completer;

  /// Number of times this command has been retried
  // int retryCount = 0;

  QueuedCommand(this.command, this.completer);

  /// Creates a queued command with a new completer
  static QueuedCommand create(Commands command) {
    return QueuedCommand(command, Completer<Do<RemoteFailure, Unit>>());
  }
}
