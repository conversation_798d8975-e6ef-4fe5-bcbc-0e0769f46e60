import 'package:bluetooth/domain/facade/bluetooth_facade.dart';
// ...existing imports
import 'package:doso/doso.dart';
import 'package:remote/domain/model/commands_model.dart';
import 'package:remote/domain/model/heat_level.dart';
import 'package:remote/repository/utils/bluetooth_data_utils.dart';

import '../../domain/failures/remote_failures.dart';
import '../../domain/model/tens_level_model.dart';

/// Handles fetching data from the Bluetooth device
///
/// Responsible for reading characteristics and parsing the results
class DeviceDataFetcher {
  final IBluetoothFacade _bluetoothFacade;

  // Error handling
  static const int _maxRetries = 3;
  final Map<String, int> _retryCount = {};

  DeviceDataFetcher(this._bluetoothFacade);

  /// Fetch the current heat level from the device
  Future<Do<RemoteFailure, HeatLevelModel>> fetchHeatLevel() async {
    try {
      final currHeatCommand = Commands(
          value: 0, commandType: 'currentTemp', commandTime: DateTime.now());
      final targetHeatLevel = Commands(
          value: 0, commandType: 'targetTemp', commandTime: DateTime.now());

      final result = await _retryOperation(() async {
        final results = await Future.wait([
          _bluetoothFacade.readDeviceCharacteristics(currHeatCommand),
          _bluetoothFacade.readDeviceCharacteristics(targetHeatLevel),
        ]);
        return results;
      }, 'heat_level');

      // Then map the Celsius value to a heat level
      print('Fetched heat level results: ${result}');
      for (final res in result) {
        bool hasFailure = false;
        res.fold(onFailure: (f) => hasFailure = true, onSuccess: (_) {});
        if (hasFailure) {
          return Do.failure(RemoteFailure.heatLevelFailure('------'));
        }
      }

      final heatLevel =
          result[0].fold(onFailure: (_) => [-1], onSuccess: (v) => v).first;
      final targetHeat =
          result[1].fold(onFailure: (_) => [-1], onSuccess: (v) => v).first;

      // print(
      //     "Mapped to heat level====================================: $heatLevel,targetLevel: $targetHeat");
      return Do.success(HeatLevelModel(
          selectedHeatLevel: targetHeat, actualHeatLevel: heatLevel));
    } catch (e) {
      print('Error fetching heat level: $e');
      rethrow;
    }
  }

  /// Fetch the current TENS level and mode from the device
  Future<Do<RemoteFailure, TensLevelModel>> fetchTensLevelAndMode() async {
    try {
      final currTensCommand = Commands(
          value: 0, commandType: 'activeTens', commandTime: DateTime.now());
      final tensModeCommand = Commands(
          value: 0, commandType: 'setTensMode', commandTime: DateTime.now());
      final targetTensCommand = Commands(
          value: 0, commandType: 'targetTens', commandTime: DateTime.now());

      final result = await _retryOperation(() async {
        final results = await Future.wait([
          _bluetoothFacade.readDeviceCharacteristics(currTensCommand),
          _bluetoothFacade.readDeviceCharacteristics(tensModeCommand),
          _bluetoothFacade.readDeviceCharacteristics(targetTensCommand),
        ]);

        return results;
      }, 'tens_level');

      // Check for any failure
      // Check each result for failure using getLeft().toNullable()
      for (final res in result) {
        bool hasFailure = false;
        res.fold(onFailure: (f) => hasFailure = true, onSuccess: (_) {});
        if (hasFailure) {
          return Do.failure(RemoteFailure.tensLevelFailure('------'));
        }
      }

      final actualTensLevel =
          result[0].fold(onFailure: (_) => [-1], onSuccess: (v) => v).first;
      final mode =
          result[1].fold(onFailure: (_) => [-1], onSuccess: (v) => v).first;
      final targetTensLevel =
          result[2].fold(onFailure: (_) => [-1], onSuccess: (v) => v).first;

      // print(
      //     "=====================================================Actual:$actualTensLevel,Mode:$mode,targetTensLevel$targetTensLevel");

      // Return success with TensLevelModel
      return Do.success(TensLevelModel(
          actualTensLevel: actualTensLevel,
          mode: mode,
          selectedTensLevel: targetTensLevel));
    } catch (e) {
      print('Error fetching TENS level: $e');
      return Do.failure(RemoteFailure.tensLevelFailure(
          'Failed to fetch TENS level and mode'));
    }
  }

  /// Generic retry mechanism for device operations
  Future<T> _retryOperation<T>(
      Future<T> Function() operation, String operationKey) async {
    int retries = _retryCount[operationKey] ?? 0;

    try {
      final result = await operation();
      _resetRetryCount(operationKey);
      return result;
    } catch (e) {
      retries++;
      _retryCount[operationKey] = retries;

      if (retries < _maxRetries) {
        // Wait with exponential backoff
        await Future.delayed(Duration(milliseconds: 200 * retries));
        return _retryOperation(operation, operationKey);
      } else {
        // Max retries reached, reset counter and rethrow
        _resetRetryCount(operationKey);
        throw e;
      }
    }
  }

  /// Reset the retry counter for a specific operation
  void _resetRetryCount(String operationKey) {
    _retryCount[operationKey] = 0;
  }

  // fetch the tens mA Intensity from the device
  Future<int> fetchTensIntensity() async {
    try {
      final tensIntensityCommand = Commands(
          value: 0,
          commandType: 'measuredIntensity',
          commandTime: DateTime.now());

      final result = await _retryOperation(() async {
        return await _bluetoothFacade
            .readDeviceCharacteristics(tensIntensityCommand);
      }, 'measured_intensity');

      // Check for failure
      bool hasFailure = false;
      result.fold(onFailure: (f) => hasFailure = true, onSuccess: (_) {});
      if (hasFailure) {
        return 0; // Return 0 on failure
      }

      final intensity =
          listToUint32(result.fold(onFailure: (_) => [], onSuccess: (v) => v));
      // print("Fetched TENS intensity: $intensity");
      return intensity;
    } catch (e) {
      print('Error fetching TENS intensity: $e');
      rethrow;
    }
  }

  /// Fetch the current battery level from the device
  Future<Do<RemoteFailure, int>> fetchBatteryLevel() async {
    try {
      final batteryCommand = Commands(
          value: 0, commandType: 'batteryLevel', commandTime: DateTime.now());

      final result = await _retryOperation(() async {
        return await _bluetoothFacade.readDeviceCharacteristics(batteryCommand);
      }, 'battery_level');

      // Check for failure
      bool hasFailure = false;
      result.fold(onFailure: (f) => hasFailure = true, onSuccess: (_) {});
      if (hasFailure) {
        return Do.failure(
            RemoteFailure.serverError('Failed to read battery level'));
      }

      final batteryData =
          result.fold(onFailure: (_) => [], onSuccess: (v) => v);
      if (batteryData.isEmpty) {
        return Do.failure(
            RemoteFailure.serverError('Empty battery level data'));
      }

      final batteryLevel = batteryData.first;

      // Validate battery level range
      if (batteryLevel < 0 || batteryLevel > 100) {
        print('Invalid battery level received: $batteryLevel');
        return Do.failure(RemoteFailure.serverError('Invalid battery level'));
      }

      print("Fetched battery level: $batteryLevel%");
      return Do.success(batteryLevel);
    } catch (e) {
      print('Error fetching battery level: $e');
      return Do.failure(
          RemoteFailure.serverError('Error fetching battery level: $e'));
    }
  }

  /// Fetch the current therapy state from the device
  /// Returns Right(true) when therapy is active, Right(false) when paused
  Future<Do<RemoteFailure, bool>> fetchTherapyState() async {
    try {
      final therapyCommand = Commands(
          value: 0, commandType: 'therapyState', commandTime: DateTime.now());

      final result = await _retryOperation(() async {
        return await _bluetoothFacade.readDeviceCharacteristics(therapyCommand);
      }, 'therapy_state');

      // Check for failure
      bool hasFailure = false;
      result.fold(onFailure: (f) => hasFailure = true, onSuccess: (_) {});
      if (hasFailure) {
        return Do.failure(
            RemoteFailure.serverError('Failed to read therapy state'));
      }

      final data = result.fold(onFailure: (_) => [], onSuccess: (v) => v);
      if (data.isEmpty) {
        return Do.failure(
            RemoteFailure.serverError('Empty therapy state response'));
      }

      // Convention: first byte 0x01 => active, 0x00 => paused
      final isActive = data[0] == 0x01;
      return Do.success(isActive);
    } catch (e) {
      print('Error fetching therapy state: $e');
      return Do.failure(
          RemoteFailure.serverError('Error fetching therapy state: $e'));
    }
  }
}
