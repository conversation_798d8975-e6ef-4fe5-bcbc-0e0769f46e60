import 'dart:async';
import 'package:doso/doso.dart';
import 'package:bluetooth/domain/facade/bluetooth_facade.dart';
import 'package:bluetooth/domain/failure/bluetooth_failure.dart';
import 'package:flutter/foundation.dart';
import 'package:remote/domain/model/commands_model.dart';
import 'package:remote/repository/managers/command_processor.dart';

/// Manages Bluetooth indications for device communication
///
/// Handles the setup and processing of indications for both TENS and heat features
class IndicationManager {
  final IBluetoothFacade _bluetoothFacade;
  final CommandProcessor _commandProcessor;

  // Indication subscription management
  StreamSubscription<Do<BluetoothFailure, List<int>>>?
      _tensIndicationSubscription;
  StreamSubscription<Do<BluetoothFailure, List<int>>>?
      _heatIndicationSubscription;
  StreamSubscription<Do<BluetoothFailure, List<int>>>?
      _tensModeIndicationSubscription;
  StreamSubscription<Do<BluetoothFailure, List<int>>>?
      _batteryIndicationSubscription;

  // Track if listeners are active
  bool _isTensIndicationListenerActive = false;

  /// Track if heat indication listener is active
  bool _isHeatIndicationListenerActive = false;

  bool _isModeIndicationListenerActive = false;
  bool _isBatteryIndicationListenerActive = false;

  // Track if setup is in progress to prevent duplicate setups
  bool _isTensSetupInProgress = false;
  bool _isHeatSetupInProgress = false;

  // Debounce mechanism for TENS indications
  int _lastTensIndicationValue = -1;
  DateTime _lastTensIndicationTime =
      DateTime.now().subtract(Duration(minutes: 1));
  static const _debounceTimeMs = 500; // milliseconds

  IndicationManager(this._bluetoothFacade, this._commandProcessor);

  /// Initialize the TENS indication listener
  Future<void> initTensIndicationListener({
    required Function(int) onIndicationReceived,
    required int currentLevel,
  }) async {
    // If already active or setup in progress, don't duplicate
    if (_isTensIndicationListenerActive || _isTensSetupInProgress) {
      debugPrint('TENS indication listener already active or being set up');
      return;
    }

    _isTensSetupInProgress = true;

    try {
      // Cancel any existing subscription first
      cancelTensIndicationListener();

      debugPrint('Initializing TENS indication listener');
      Commands command = Commands(
          value: 0, commandType: 'activeTens', commandTime: DateTime.now());

      // Start listening to indications
      _tensIndicationSubscription =
          _bluetoothFacade.listenToIndication(command).listen((indication) {
        try {
          final indicationData =
              indication.fold(onFailure: (_) => [], onSuccess: (v) => v);
          if (indicationData.isNotEmpty) {
            // Process the indication with our improved utility
            final indicationValue = indicationData.first;

            // Apply debouncing to avoid rapid identical updates
            final now = DateTime.now();
            final timeSinceLastIndication =
                now.difference(_lastTensIndicationTime).inMilliseconds;

            if (indicationValue != _lastTensIndicationValue ||
                timeSinceLastIndication > _debounceTimeMs) {
              _lastTensIndicationValue = indicationValue;
              _lastTensIndicationTime = now;

              // Update command processor and notify caller
              _commandProcessor.handleTensIndication(
                  indicationValue, currentLevel);

              try {
                onIndicationReceived(indicationValue);
              } catch (e, st) {
                debugPrint('Error in onIndicationReceived callback: $e');
                debugPrint(st.toString().split('\n').take(5).join('\n'));
              }
            }
          }
        } catch (e, st) {
          debugPrint('Exception handling TENS indication: $e');
          debugPrint(st.toString().split('\n').take(5).join('\n'));
        }
      }, onError: (error) {
        debugPrint('Error in TENS indication listener: $error');
        _isTensIndicationListenerActive = false;
      }, onDone: () {
        debugPrint('TENS indication listener closed');
        _isTensIndicationListenerActive = false;
      });

      _isTensIndicationListenerActive = true;
    } catch (e) {
      debugPrint('Failed to initialize TENS indication listener: $e');
      _isTensIndicationListenerActive = false;
    } finally {
      _isTensSetupInProgress = false;
    }
  }

  /// Cancel the TENS indication listener
  void cancelTensIndicationListener() {
    _tensIndicationSubscription?.cancel();
    _tensIndicationSubscription = null;
    _isTensIndicationListenerActive = false;
    _commandProcessor.resetExpectedTensLevel();
    _lastTensIndicationValue = -1;
  }

  /// Initialize the heat indication listener
  Future<void> initHeatIndicationListener({
    required Function(int) onIndicationReceived,
    required int currentLevel,
  }) async {
    // If already active or setup in progress, don't duplicate
    if (_isHeatIndicationListenerActive || _isHeatSetupInProgress) {
      debugPrint('Heat indication listener already active or being set up');
      return;
    }

    _isHeatSetupInProgress = true;

    try {
      // Cancel any existing subscription first
      cancelHeatIndicationListener();

      Commands command = Commands(
          value: 0, commandType: 'currentTemp', commandTime: DateTime.now());

      // Start listening to indications for heat
      _heatIndicationSubscription =
          _bluetoothFacade.listenToIndication(command).listen((indication) {
        try {
          final indicationData =
              indication.fold(onFailure: (_) => [], onSuccess: (v) => v);
          if (indicationData.isNotEmpty) {
            final heatLevel = indicationData.first;

            // Validate heat level range
            if (heatLevel < 0 || heatLevel > 3) {
              debugPrint(
                  'Heat level out of valid range: $heatLevel - ignoring');
              return;
            }

            // Notify caller and update command processor
            try {
              onIndicationReceived(heatLevel);
            } catch (e, st) {
              debugPrint('Error in onIndicationReceived callback (heat): $e');
              debugPrint(st.toString().split('\n').take(5).join('\n'));
            }

            _commandProcessor.handleHeatIndication(heatLevel, currentLevel);
          }
        } catch (e, st) {
          debugPrint('Exception handling HEAT indication: $e');
          debugPrint(st.toString().split('\n').take(5).join('\n'));
        }
      }, onError: (error) {
        debugPrint('Error in HEAT indication listener: $error');
        _isHeatIndicationListenerActive = false;
      }, onDone: () {
        debugPrint('HEAT indication listener closed');
        _isHeatIndicationListenerActive = false;
      });

      _isHeatIndicationListenerActive = true;
    } catch (e) {
      debugPrint('Failed to initialize HEAT indication listener: $e');
      _isHeatIndicationListenerActive = false;
    } finally {
      _isHeatSetupInProgress = false;
    }
  }

  ///initialize the mode indication listener
  Future<void> initModeIndicationListener({
    required Function(int) onIndicationReceived,
  }) async {
    // If already active or setup in progress, don't duplicate
    if (_isModeIndicationListenerActive) {
      debugPrint('Mode indication listener already active or being set up');
      return;
    }

    try {
      // Cancel any existing subscription first
      cancelTensModeIndicationListener();

      Commands command = Commands(
          value: 0, commandType: 'setTensMode', commandTime: DateTime.now());

      // Start listening to indications for mode
      _tensModeIndicationSubscription =
          _bluetoothFacade.listenToIndication(command).listen((indication) {
        try {
          final indicationData =
              indication.fold(onFailure: (_) => [], onSuccess: (v) => v);
          if (indicationData.isNotEmpty) {
            debugPrint('Received mode indication value: $indicationData');
            final indicationValue = indicationData.first;

            try {
              onIndicationReceived(indicationValue);
            } catch (e, st) {
              debugPrint('Error in onIndicationReceived callback (mode): $e');
              debugPrint(st.toString().split('\n').take(5).join('\n'));
            }
          }
        } catch (e, st) {
          debugPrint('Exception handling MODE indication: $e');
          debugPrint(st.toString().split('\n').take(5).join('\n'));
        }
      }, onError: (error) {
        debugPrint('Error in mode indication listener: $error');
        _isModeIndicationListenerActive = false;
      }, onDone: () {
        debugPrint('Mode indication listener closed');
        _isModeIndicationListenerActive = false;
      });

      _isModeIndicationListenerActive = true;
    } catch (e) {
      debugPrint('Failed to initialize mode indication listener: $e');
      _isModeIndicationListenerActive = false;
    } finally {}
  }

  /// Cancel the heat indication listener
  void cancelHeatIndicationListener() {
    _heatIndicationSubscription?.cancel();
    _heatIndicationSubscription = null;
    _isHeatIndicationListenerActive = false;
    _commandProcessor.resetExpectedHeatLevel();
  }

  /// Cancel the mode indication listener
  void cancelTensModeIndicationListener() {
    _tensModeIndicationSubscription?.cancel();
    _tensModeIndicationSubscription = null;
    _isModeIndicationListenerActive = false;
  }

  /// Initialize the battery indication listener
  Future<void> initBatteryIndicationListener({
    required Function(int) onIndicationReceived,
  }) async {
    // If already active, don't duplicate
    if (_isBatteryIndicationListenerActive) {
      debugPrint('Battery indication listener already active');
      return;
    }

    try {
      // Cancel any existing subscription first
      cancelBatteryIndicationListener();

      Commands command = Commands(
          value: 0, commandType: 'batteryLevel', commandTime: DateTime.now());

      // Start listening to indications for battery level
      _batteryIndicationSubscription =
          _bluetoothFacade.listenToIndication(command).listen((indication) {
        final indicationData =
            indication.fold(onFailure: (_) => [], onSuccess: (v) => v);
        if (indicationData.isNotEmpty) {
          // Log the raw data for debugging
          debugPrint('Received battery indication value: $indicationData');

          // Process the indication
          final batteryLevel = indicationData.first;
          debugPrint('Received battery indication: $batteryLevel%');

          // Additional validation for battery level
          if (batteryLevel < 0 || batteryLevel > 100) {
            debugPrint(
                "Battery level out of valid range: $batteryLevel - ignoring");
            return;
          }

          // Notify caller about the new indication value
          onIndicationReceived(batteryLevel);
        }
      }, onError: (error) {
        debugPrint('Error in battery indication listener: $error');
        _isBatteryIndicationListenerActive = false;
      }, onDone: () {
        debugPrint('Battery indication listener closed');
        _isBatteryIndicationListenerActive = false;
      });

      _isBatteryIndicationListenerActive = true;
    } catch (e) {
      debugPrint('Failed to initialize battery indication listener: $e');
      _isBatteryIndicationListenerActive = false;
    }
  }

  /// Cancel the battery indication listener
  void cancelBatteryIndicationListener() {
    _batteryIndicationSubscription?.cancel();
    _batteryIndicationSubscription = null;
    _isBatteryIndicationListenerActive = false;
  }

  /// Check if TENS indication listener is active
  bool get isTensIndicationActive => _isTensIndicationListenerActive;

  /// Check if heat indication listener is active
  bool get isHeatIndicationActive => _isHeatIndicationListenerActive;

  /// Check if battery indication listener is active
  bool get isBatteryIndicationActive => _isBatteryIndicationListenerActive;

  /// Cleanup all resources
  void dispose() {
    cancelTensIndicationListener();
    cancelHeatIndicationListener();
    cancelTensModeIndicationListener();
    cancelBatteryIndicationListener();
  }
}
