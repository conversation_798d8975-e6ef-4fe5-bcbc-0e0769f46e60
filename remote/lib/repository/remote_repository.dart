import 'dart:async';

import 'package:analytics/domain/models/therapy_session_event_model.dart';
import 'package:bluetooth/domain/facade/bluetooth_facade.dart';
import 'package:doso/doso.dart';
import 'package:injectable/injectable.dart';
import 'package:remote/domain/facade/remote_control_facade.dart';
import 'package:remote/domain/failures/remote_failures.dart';
import 'package:remote/domain/model/commands_model.dart';
import 'package:remote/domain/model/device_model.dart';
import 'package:remote/domain/model/heat_level.dart';
import 'package:remote/domain/model/tens_level_model.dart';
import 'package:remote/repository/managers/command_processor.dart';
import 'package:remote/repository/managers/connection_manager.dart';
import 'package:remote/repository/managers/device_data_fetcher.dart';
import 'package:remote/repository/managers/device_state_manager.dart';
import 'package:remote/repository/managers/indication_manager.dart';
import 'package:remote/repository/managers/polling_manager.dart';
import 'package:remote/repository/utils/update_states.dart';
import 'package:remote/domain/core/unit.dart';
import 'package:uuid/uuid.dart';

@LazySingleton(as: RemoteControlFacade)
class RemoteRepository implements RemoteControlFacade {
  // Dependencies
  final IBluetoothFacade _bluetoothFacade;

  // Component managers
  late final DeviceStateManager _stateManager;
  late final CommandProcessor _commandProcessor;
  late final IndicationManager _indicationManager;
  late ConnectionManager _connectionManager;
  late final PollingManager _pollingManager;
  late final DeviceDataFetcher _dataFetcher;

  // Connection handling
  bool _setupInProgress = false;
  bool _isConnected = false;

  // Device state tracking
  bool _isTherapyActive = false;
  // late final String _currentTherapySessionId;

  // Therapy state controller
  final _therapyStateController =
      StreamController<Do<RemoteFailure, bool>>.broadcast();

  // Setting change events controller
  final _settingChangeController =
      StreamController<TherapySessionEventModel>.broadcast();

  // Getter for setting change events stream
  @override
  Stream<TherapySessionEventModel> get settingChangeStream =>
      _settingChangeController.stream;

  // Helper method to emit setting change events
  void _emitSettingChangeEvent(TherapySessionEventModel event) {
    _settingChangeController.add(event);
  }

  /// Fetch therapy state from device and propagate into local state and controller
  Future<void> _updateTherapyState() async {
    if (!_isConnected) return;

    try {
      final result = await _dataFetcher.fetchTherapyState();
      result.fold(
        onFailure: (failure) {
          if (_isConnected) {
            _stateManager.signalError('Error updating therapy state: $failure');
          }
        },
        onSuccess: (isActive) {
          // Only update if changed
          if (_isTherapyActive != isActive) {
            _isTherapyActive = isActive;
            _therapyStateController.add(Do.success(_isTherapyActive));
          } else {
            // ensure controller emits latest state at least once
            _therapyStateController.add(Do.success(_isTherapyActive));
          }
        },
      );
    } catch (e) {
      print('Error updating therapy state: $e');
      if (_isConnected) {
        _stateManager.signalError('Error updating therapy state: $e');
      }
    }
  }

  // Characteristic UUIDs
  static const String POWER_STATE_CHARACTERISTIC = "0xA5E1DC01";
  static const String THERAPY_STATE_CHARACTERISTIC = "0xA5E1DC02";

  // Reconnection handling
  Timer? _reconnectDebounceTimer;
  Timer? _reconnectAllowTimer;
  static const _reconnectDebounceTime = Duration(milliseconds: 500);
  static const _reconnectAllowDelay = Duration(seconds: 3);

  RemoteRepository(this._bluetoothFacade) {
    // Initialize managers
    _stateManager = DeviceStateManager();
    _commandProcessor = CommandProcessor(_bluetoothFacade, _stateManager);
    _indicationManager = IndicationManager(_bluetoothFacade, _commandProcessor);
    _dataFetcher = DeviceDataFetcher(_bluetoothFacade);

    // Initialize connection manager with callbacks
    _connectionManager = ConnectionManager(
      bluetoothFacade: _bluetoothFacade,
      onDeviceConnected: _handleDeviceConnected,
      onDeviceDisconnected: _handleDeviceDisconnected,
    );

    // Initialize polling manager with callbacks
    _pollingManager = PollingManager(
      onPollHeat: _updateHeatLevel,
      onPollTens: _updateTensLevel,
      onPollDeviceStatus: _updateDeviceStatus,
    );
  }

  //get current session ID

  // Device connection event handlers
  Future<void> _handleDeviceConnected() async {
    // Cancel any pending reconnect debounce timer
    _reconnectDebounceTimer?.cancel();

    // Prevent duplicate connections in quick succession
    _reconnectDebounceTimer = Timer(_reconnectDebounceTime, () async {
      // Prevent simultaneous setup
      if (_setupInProgress || _isConnected) {
        print(
            'Connection setup already in progress or already connected, skipping');
        return;
      }

      _setupInProgress = true;

      try {
        print('Handling device connection');
        _isConnected = true;

        // Clear command queues on reconnection to prevent stale commands from executing
        _commandProcessor.clearAllCommandQueues();

        // Signal device connection to state manager
        _stateManager.signalDeviceConnected();

        // Reset first read flags to ensure proper synchronization
        _stateManager.resetFirstReadFlags();

        // Fetch initial device state - this will initialize actual and selected levels to match
        await Future.wait([
          _updateHeatLevel(isInitialRead: true),
          _updateTensLevel(updateState: UpdateState.initial),
          _updateDeviceStatus(),
          _updateTherapyState(),
          _updateBatteryLevel(),
        ]);

        // Start indication listeners
        await _setupIndicationListeners();

        // Reset polling error count and trigger immediate poll to ensure data freshness
        _pollingManager.resetAndRestart();
        _pollingManager.requestImmediatePoll();
      } catch (e) {
        print('Error during connection setup: $e');
        _stateManager.signalError('Error setting up device connection: $e');
      } finally {
        _setupInProgress = false;
      }
    });
  }

  void _handleDeviceDisconnected() {
    _isConnected = false;

    // Signal the state manager about disconnection
    _stateManager.signalDeviceDisconnected();

    // Stop processing and clear all command queues immediately
    print(
        'Stopping and clearing all command queues due to device disconnection');
    _commandProcessor.clearAllCommandQueues();
    _pollingManager.stopAllPolling();

    // Cancel indication listeners on disconnect
    _indicationManager.cancelTensIndicationListener();
    _indicationManager.cancelHeatIndicationListener();

    // Update device state with error message
    _stateManager.signalError('Device disconnected',
        isHeatError: true, isTensError: true);

    // Allow reconnection after a delay to prevent immediate reconnection loops
    _reconnectAllowTimer?.cancel();
    _reconnectAllowTimer = Timer(_reconnectAllowDelay, () {
      print('Allowing reconnection attempts after disconnection delay');
      _connectionManager.allowReconnection();
    });

    print('Device disconnection handling complete');
  }

  // Setup indication listeners
  Future<void> _setupIndicationListeners() async {
    print('Setting up indication listeners');

    // Setup TENS indication listener
    await _indicationManager.initTensIndicationListener(
      onIndicationReceived: (level) {
        final currentLevel =
            _stateManager.deviceModel.tensLevel?.actualTensLevel ?? 0;

        // Track analytics for device-initiated TENS level change
        if (level != currentLevel) {
          TherapySessionEventModel event = TherapySessionEventModel(
            eventType: 'tens_level_change',
            eventId: Uuid().v4(),
            eventData: {
              'oldValue': currentLevel,
              'newValue': level,
              'direction': level > currentLevel ? 'increase' : 'decrease',
            },
            isDeviceInitiated: true,
            timestamp: DateTime.now(),
          );
          _emitSettingChangeEvent(event);
        }
        // print(
        // '03 [TENS_DEBUG] onIndicationReceived called with level=$level, actual level:${_stateManager.deviceModel.tensLevel!.actualTensLevel}currentSelected=${_stateManager.deviceModel.tensLevel?.selectedTensLevel} queueLenght${_commandProcessor.tensCommandQueueLength}');
        _stateManager.updateTensLevel(
            actualLevel: level,
            selectedLevel:
                _stateManager.deviceModel.tensLevel?.selectedTensLevel,
            calledFrom: 'IndicationManager',
            updateState: UpdateState.indicate);
      },
      currentLevel: _stateManager.deviceModel.tensLevel!.actualTensLevel,
    );

    // Setup heat indication listener
    await _indicationManager.initHeatIndicationListener(
      onIndicationReceived: (level) {
        final currentLevel =
            _stateManager.deviceModel.heatLevel?.actualHeatLevel ?? 0;

        // Track analytics for device-initiated heat level change
        if (level != currentLevel) {
          TherapySessionEventModel event = TherapySessionEventModel(
            eventType: 'heat_level_change',
            eventId: Uuid().v4(),
            eventData: {
              'oldValue': currentLevel,
              'newValue': level,
              'direction': level > currentLevel ? 'increase' : 'decrease',
            },
            isDeviceInitiated: true,
            timestamp: DateTime.now(),
          );
          _emitSettingChangeEvent(event);
        }

        _stateManager.updateHeatLevel(actualLevel: level);
      },
      currentLevel: _stateManager.deviceModel.heatLevel?.actualHeatLevel ?? 0,
    );

    // Setup connection manager to listen for mode changes
    await _indicationManager.initModeIndicationListener(
      onIndicationReceived: (mode) {
        final currentMode = _stateManager.deviceModel.tensLevel?.mode ?? 0;

        // Track analytics for device-initiated mode change
        if (mode != currentMode) {
          TherapySessionEventModel event = TherapySessionEventModel(
            eventType: 'mode_change',
            eventId: Uuid().v4(),
            eventData: {
              'oldValue': currentMode,
              'newValue': mode,
            },
            isDeviceInitiated: true,
            timestamp: DateTime.now(),
          );
          _emitSettingChangeEvent(event);
        }

        // print('Mode changed indication received: $mode');
        _stateManager.changeMode(mode);
      },
    );

    // Setup battery indication listener
    await _indicationManager.initBatteryIndicationListener(
      onIndicationReceived: (batteryLevel) {
        final currentBatteryLevel =
            _stateManager.deviceModel.deviceInfo.batteryLevel?.batteryLevel ??
                0;

        // Track analytics for device-initiated battery level change
        if (batteryLevel != currentBatteryLevel) {
          TherapySessionEventModel event = TherapySessionEventModel(
            eventType: 'battery_level_change',
            eventId: Uuid().v4(),
            eventData: {
              'oldValue': currentBatteryLevel,
              'newValue': batteryLevel,
            },
            isDeviceInitiated: true,
            timestamp: DateTime.now(),
          );
          _emitSettingChangeEvent(event);
        }

        // Update battery level in device state
        _stateManager.updateBatteryLevel(batteryLevel: batteryLevel);
      },
    );
  }

  // Device data update methods
  Future<void> _updateHeatLevel({bool isInitialRead = false}) async {
    if (!_isConnected) return;

    try {
      final result = await _dataFetcher.fetchHeatLevel();
      result.fold(
        onFailure: (failure) {
          if (_isConnected) {
            _stateManager.signalError('Error updating heat level: $failure',
                isHeatError: true);
          }
        },
        onSuccess: (heatLevelModel) {
          // Update state with actual and selected heat levels
          // print(
          //     '01 _updateHeatLevel called: actualLevel=${heatLevelModel.actualHeatLevel}, selectedLevel=${heatLevelModel.selectedHeatLevel}, isInitialRead=$isInitialRead');
          _stateManager.updateHeatLevel(
            actualLevel: heatLevelModel.actualHeatLevel,
            selectedLevel: heatLevelModel.selectedHeatLevel,
            isInitialRead: isInitialRead,
          );
        },
      );
    } catch (e) {
      print('Error updating heat level: $e');
      if (_isConnected) {
        _stateManager.signalError('Error updating heat level: $e',
            isHeatError: true);
      }
    }
  }

  Future<void> _updateTensLevel(
      {UpdateState updateState = UpdateState.read}) async {
    if (!_isConnected) return;

    try {
      final result = await _dataFetcher.fetchTensLevelAndMode();
      result.fold(
        onFailure: (failure) {
          if (_isConnected) {
            _stateManager.signalError('Error updating TENS level: $failure',
                isTensError: true);
          }
        },
        onSuccess: (tensLevelModel) {
          // Update state with actual TENS level and mode
          // print(
          //     '02 _updateTensLevel called: actualLevel=${tensLevelModel.actualTensLevel}, mode=${tensLevelModel.mode}, isInitialRead=${updateState}, currentSelected=${_stateManager.deviceModel.tensLevel?.selectedTensLevel}');
          // // Update state manager with actual TENS level and mode

          if (_commandProcessor.tensCommandQueueLength > 0) {
            print(
                '[TENS_DEBUG] _updateTensLevel called with commands in queue==========================: ${_commandProcessor.tensCommandQueueLength}');
            _stateManager.updateTensLevel(
              actualLevel: tensLevelModel.actualTensLevel,
              mode: tensLevelModel.mode,
              updateState: updateState,
            );
          } else {
            // When no commands in queue, check if this is initial read or device-initiated change
            _stateManager.updateTensLevel(
              actualLevel: tensLevelModel.actualTensLevel,
              selectedLevel: tensLevelModel.selectedTensLevel,
              mode: tensLevelModel.mode,
              updateState: UpdateState.indicate,
            );
          }
          // print(
          //     "currentSelected${_stateManager.deviceModel.tensLevel?.selectedTensLevel}");
          // print(
          //     '[TENS_DEBUG] _updateTensLevel called: currentSelected=$currentSelectedLevel, actualLevel=${tensLevelModel.actualTensLevel}, mode=${tensLevelModel.mode}, isInitialRead=$updateState');
        },
      );
    } catch (e) {
      print('Error updating TENS level: $e');
      if (_isConnected) {
        _stateManager.signalError('Error updating TENS level: $e',
            isTensError: true);
      }
    }
  }

  /// Update battery level from device
  Future<void> _updateBatteryLevel() async {
    if (!_isConnected) return;

    try {
      final result = await _dataFetcher.fetchBatteryLevel();
      result.fold(
        onFailure: (failure) {
          if (_isConnected) {
            _stateManager.signalError('Error updating battery level: $failure');
          }
        },
        onSuccess: (batteryLevel) {
          _stateManager.updateBatteryLevel(batteryLevel: batteryLevel);
        },
      );
    } catch (e) {
      print('Error updating battery level: $e');
      if (_isConnected) {
        _stateManager.signalError('Error updating battery level: $e');
      }
    }
  }

  Future<void> _updateDeviceStatus() async {
    if (!_isConnected) return;

    try {
      // Update battery level as part of device status
      await _updateBatteryLevel();

      // Also update therapy state during device status polling to keep play/pause accurate
      await _updateTherapyState();

      final deviceInfo = await _bluetoothFacade.getDeviceInformation().first;

      deviceInfo.fold(
        onFailure: (failure) {
          if (_isConnected) {
            _stateManager.signalError('Error getting device status: $failure');
          }
        },
        onSuccess: (deviceModel) {
          _stateManager.updateDeviceModel(deviceModel);
        },
      );
    } catch (e) {
      print('Error getting device status: $e');
      if (_isConnected) {
        _stateManager.signalError('Error getting device status: $e');
      }
    }
  }

  // RemoteControlFacade implementations
  @override
  Future<Do<RemoteFailure, Unit>> decreaseHeat() async {
    if (!_isConnected) {
      return Do.failure(RemoteFailure.serverError('Device not connected'));
    }

    try {
      // Calculate target level with bounds checking
      final int currentLevel =
          _stateManager.deviceModel.heatLevel?.selectedHeatLevel ?? 0;
      final int targetLevel = currentLevel > 0 ? currentLevel - 1 : 0;

      // No change needed if already at minimum
      if (targetLevel >= currentLevel) {
        return Do.success(Unit());
      }

      // Track analytics for heat level change
      TherapySessionEventModel event = TherapySessionEventModel(
        eventType: 'heat_level_change',
        eventId: Uuid().v4(),
        eventData: {
          'oldValue': currentLevel,
          'newValue': targetLevel,
          'direction': 'decrease',
        },
        isDeviceInitiated: false,
        timestamp: DateTime.now(),
      );
      _emitSettingChangeEvent(event);

      // Create command
      final command = Commands(
        value: targetLevel,
        commandType: 'decreaseHeat',
        commandTime: DateTime.now(),
      );

      // Update state optimistically
      _stateManager.updateHeatLevel(selectedLevel: targetLevel);

      // Mark activity for adaptive polling
      _pollingManager.markHeatActivity();

      // Send command
      return _commandProcessor.enqueueHeatCommand(command).then(
            (result) => result.fold(
              onFailure: (failure) => Do.failure(failure),
              onSuccess: (_) => Do.success(unit),
            ),
          );
    } catch (e) {
      return Do.failure(
          RemoteFailure.serverError('Error decreasing heat level: $e'));
    }
  }

  @override
  Future<Do<RemoteFailure, Unit>> increaseHeat() async {
    if (!_isConnected) {
      return Do.failure(RemoteFailure.serverError('Device not connected'));
    }

    try {
      // Calculate target level with bounds checking
      final int currentLevel =
          _stateManager.deviceModel.heatLevel?.selectedHeatLevel ?? 0;
      final int targetLevel = currentLevel < 3 ? currentLevel + 1 : 3;

      // No change needed if already at maximum
      if (targetLevel <= currentLevel) {
        return Do.success(unit);
      }

      // Track analytics for heat level change

      TherapySessionEventModel event = TherapySessionEventModel(
        eventType: 'heat_level_change',
        eventId: Uuid().v4(),
        eventData: {
          'oldValue': currentLevel,
          'newValue': targetLevel,
          'direction': 'increase',
        },
        isDeviceInitiated: false,
        timestamp: DateTime.now(),
      );
      _emitSettingChangeEvent(event);

      // Create command
      final command = Commands(
        value: targetLevel,
        commandType: 'increaseHeat',
        commandTime: DateTime.now(),
      );

      // Update state optimistically
      _stateManager.updateHeatLevel(selectedLevel: targetLevel);

      // Mark activity for adaptive polling
      _pollingManager.markHeatActivity();

      // Send command
      return _commandProcessor.enqueueHeatCommand(command).then(
            (result) => result.fold(
              onFailure: (failure) => Do.failure(failure),
              onSuccess: (_) => Do.success(unit),
            ),
          );
    } catch (e) {
      return Do.failure(
          RemoteFailure.serverError('Error increasing heat level: $e'));
    }
  }

  @override
  Stream<Do<RemoteFailure, HeatLevelModel>> watchHeatLevel() {
    _updateHeatLevel();
    return _stateManager.heatLevelStream;
  }

  @override
  Future<Do<RemoteFailure, Unit>> decreaseTens() async {
    if (!_isConnected) {
      return Do.failure(RemoteFailure.serverError('Device not connected'));
    }

    try {
      final currentLevel =
          _stateManager.deviceModel.tensLevel?.selectedTensLevel ?? 0;
      final targetLevel = currentLevel > 1 ? currentLevel - 1 : 1;

      // Track analytics for TENS level change

      TherapySessionEventModel event = TherapySessionEventModel(
        eventType: 'tens_level_change',
        eventId: Uuid().v4(),
        eventData: {
          'oldValue': currentLevel,
          'newValue': targetLevel,
          'direction': 'decrease',
        },
        isDeviceInitiated: false,
        timestamp: DateTime.now(),
      );
      _emitSettingChangeEvent(event);

// Handle special case: if ramping up and user decides to decrease
      if (_commandProcessor.hasTensCommandsOfType('increaseTens')) {
        // Cancel any pending increase commands
        _commandProcessor.clearTensCommandQueue();

        // Get current actual level (not selected level)
        final int actualLevel =
            _stateManager.deviceModel.tensLevel?.actualTensLevel ?? 0;
        final int selectedLevel =
            _stateManager.deviceModel.tensLevel?.selectedTensLevel ?? 0;

        // If already at minimum, nothing to do
        if (selectedLevel <= 1) {
          return Do.success(unit);
        }

        // Calculate the first target level to stop the ongoing ramp-up
        final int firstTargetLevel = actualLevel - 1;

        // Enqueue the first command to stop the ramp-up
        final firstCommand = Commands(
          value: actualLevel,
          commandType: 'decreaseTens',
          commandTime: DateTime.now(),
        );
        _commandProcessor.enqueueTensCommand(firstCommand);

        // Enqueue the second command to decrease further
        if (firstTargetLevel >= 1) {
          final secondCommand = Commands(
            value: firstTargetLevel,
            commandType: 'decreaseTens',
            commandTime: DateTime.now(),
          );
          _commandProcessor.enqueueTensCommand(secondCommand);
        }
        // print(
        //     '04: [TENS_DEBUG] decreaseTens called: firstTargetLevel=$firstTargetLevel, actualLevel=$actualLevel');

        // Update state optimistically to reflect the second target level
        _stateManager.updateTensLevel(
          selectedLevel: firstTargetLevel >= 1 ? firstTargetLevel : actualLevel,
          calledFrom: 'CommandProcessor',
          updateState: UpdateState.write,
        );

        // Mark activity for adaptive polling
        _pollingManager.markTensActivity();

        // Return success
        return Do.success(unit);
      } else {
        // Normal case: decrease from current selected level
        final int currentLevel =
            _stateManager.deviceModel.tensLevel?.selectedTensLevel ?? 0;

        // If already at minimum, nothing to do
        if (currentLevel <= 1) {
          return Do.success(unit);
        }

        // Calculate target level
        final int targetLevel = currentLevel - 1;

        // Create command
        final command = Commands(
          value: targetLevel,
          commandType: 'decreaseTens',
          commandTime: DateTime.now(),
        );

        // Update state optimistically
        _stateManager.updateTensLevel(
            selectedLevel: targetLevel,
            calledFrom: 'CommandProcessor2',
            updateState: UpdateState.write);

        // Mark activity for adaptive polling
        _pollingManager.markTensActivity();

        // Send command
        return _commandProcessor.enqueueTensCommand(command).then(
              (result) => result.fold(
                onFailure: (failure) => Do.failure(failure),
                onSuccess: (_) => Do.success(unit),
              ),
            );
      }
    } catch (e) {
      return Do.failure(
          RemoteFailure.serverError('Error decreasing TENS level: $e'));
    }
  }

  @override
  Future<Do<RemoteFailure, Unit>> increaseTens() async {
    if (!_isConnected) {
      return Do.failure(RemoteFailure.serverError('Device not connected'));
    }

    try {
      // Calculate target level with bounds checking
      final int currentLevel =
          _stateManager.deviceModel.tensLevel?.selectedTensLevel ?? 0;
      final int targetLevel = currentLevel < 10 ? currentLevel + 1 : 10;

      // No change needed if already at maximum
      if (targetLevel <= currentLevel) {
        return Do.success(unit);
      }

      // Track analytics for TENS level change

      TherapySessionEventModel event = TherapySessionEventModel(
        eventType: 'tens_level_change',
        eventId: Uuid().v4(),
        eventData: {
          'oldValue': currentLevel,
          'newValue': targetLevel,
          'direction': 'increase',
        },
        isDeviceInitiated: false,
        timestamp: DateTime.now(),
      );
      _emitSettingChangeEvent(event);

      // Create command
      final command = Commands(
        value: targetLevel,
        commandType: 'increaseTens',
        commandTime: DateTime.now(),
      );

      // Update state optimistically
      // If we're ramping up and user decides to increase, we need to clear any pending decrease commands
      // print(
      //     '05: [TENS_DEBUG] increaseTens called: targetLevel=$targetLevel, currentLevel=$currentLevel');
      _stateManager.updateTensLevel(
          selectedLevel: targetLevel,
          calledFrom: 'CommandProcessor3',
          updateState: UpdateState.write);

      // Mark activity for adaptive polling
      _pollingManager.markTensActivity();

      // Send command
      return _commandProcessor.enqueueTensCommand(command).then(
            (result) => result.fold(
              onFailure: (failure) => Do.failure(failure),
              onSuccess: (_) => Do.success(unit),
            ),
          );
    } catch (e) {
      return Do.failure(
          RemoteFailure.serverError('Error increasing TENS level: $e'));
    }
  }

  @override
  Stream<Do<RemoteFailure, TensLevelModel>> watchTensLevel() {
    _updateTensLevel(updateState: UpdateState.initial);
    return _stateManager.tensLevelStream;
  }

  @override
  Future<Do<RemoteFailure, Unit>> changeMode(int mode) async {
    if (!_isConnected) {
      return Do.failure(RemoteFailure.serverError('Device not connected'));
    }

    try {
      final currentMode = _stateManager.deviceModel.tensLevel?.mode ?? 0;

      TherapySessionEventModel event = TherapySessionEventModel(
        eventType: 'mode_change',
        eventId: Uuid().v4(),
        eventData: {
          'oldValue': currentMode,
          'newValue': mode,
        },
        isDeviceInitiated: false,
        timestamp: DateTime.now(),
      );
      // Track analytics for mode change
      _emitSettingChangeEvent(event);

      // Create command for mode change
      final command = Commands(
        value: mode,
        commandType: 'setTensMode',
        commandTime: DateTime.now(),
      );

      // // Make sure indication listener is active
      if (!_indicationManager.isTensIndicationActive) {
        _setupIndicationListeners();
      }

      // // Mark activity for adaptive polling
      // _pollingManager.markTensActivity();

      // Send command
      return _bluetoothFacade.sendCommand(command).then(
        (result) {
          // Update local model if successful
          result.fold(
            onFailure: (_) {},
            onSuccess: (_) {
              _stateManager.changeMode(mode);
              _commandProcessor.clearTensCommandQueue();
              _stateManager.updateTensLevel(
                  updateState: UpdateState.modeUpdate);
            },
          );

          final failure =
              result.fold(onFailure: (f) => f, onSuccess: (_) => null);
          if (failure != null) {
            return Do.failure(
                RemoteFailure.serverError('Error changing mode: $failure'));
          } else {
            return Do.success(Unit());
          }
        },
      );
    } catch (e) {
      return Do.failure(RemoteFailure.serverError('Error changing mode: $e'));
    }
  }

  @override
  Stream<Do<RemoteFailure, DeviceModel>> getDeviceStatus() {
    _updateDeviceStatus();
    return _stateManager.deviceModelStream;
  }

  @override
  Future<Do<RemoteFailure, DeviceModel>> getDeviceInformation() async {
    return Do.success(_stateManager.deviceModel);
  }

  /// Allow reconnection attempts after disconnection
  /// This should be called when the app wants to enable reconnection
  /// (e.g., when user manually tries to reconnect or after a delay)
  void allowReconnection() {
    _connectionManager.allowReconnection();
  }

  @override
  Future<Do<RemoteFailure, Unit>> powerOffDevice() async {
    if (!_isConnected) {
      return Do.failure(RemoteFailure.serverError('Device not connected'));
    }

    try {
      // Create power off command
      final command = Commands(
        value: 0,
        commandType: 'powerOff',
        commandTime: DateTime.now(),
      );

      // Send the command through bluetooth facade
      return await _bluetoothFacade.sendCommand(command).then(
        (result) {
          final failure =
              result.fold(onFailure: (f) => f, onSuccess: (_) => null);
          if (failure != null) {
            return Do.failure(RemoteFailure.serverError(failure.toString()));
          } else {
            return Do.success(Unit());
          }
        },
      );
    } catch (e) {
      return Do.failure(
          RemoteFailure.serverError('Error powering off device: $e'));
    }
  }

  @override
  Future<Do<RemoteFailure, Unit>> toggleTherapy() async {
    if (!_isConnected) {
      return Do.failure(RemoteFailure.serverError('Device not connected'));
    }

    try {
      // Toggle therapy state
      final newState = !_isTherapyActive;
      final value = newState ? 0x01 : 0x00;

      // Track analytics for therapy control

      TherapySessionEventModel event = TherapySessionEventModel(
        eventType: newState ? 'start_therapy' : 'pause_therapy',
        eventId: Uuid().v4(),
        eventData: {
          'action': newState ? 'start' : 'pause',
        },
        isDeviceInitiated: false,
        timestamp: DateTime.now(),
      );
      _emitSettingChangeEvent(event);

      // Create therapy state command
      final command = Commands(
        value: value,
        commandType: newState ? 'startTherapy' : 'pauseTherapy',
        commandTime: DateTime.now(),
      );

      // Send the command through bluetooth facade
      final sendResult = await _bluetoothFacade.sendCommand(command);

      // If send failed, return the failure
      final sendFailure =
          sendResult.fold(onFailure: (f) => f, onSuccess: (_) => null);
      if (sendFailure != null) {
        return Do.failure(RemoteFailure.serverError(sendFailure.toString()));
      }

      // Send succeeded - attempt to read back therapy state to confirm
      try {
        await Future.delayed(Duration(milliseconds: 300));
        final readResult = await _dataFetcher.fetchTherapyState();
        final readFailure =
            readResult.fold(onFailure: (f) => f, onSuccess: (_) => null);
        if (readFailure != null) {
          // Read failed - fall back to optimistic update but still consider write succeeded
          _isTherapyActive = newState;
          _updateTensLevel();
          _therapyStateController.add(Do.success(_isTherapyActive));
          // Surface an error through state manager but return success to caller
          _stateManager
              .signalError('Failed to confirm therapy state: $readFailure');
          return Do.success(unit);
        }

        final isActive =
            readResult.fold(onFailure: (_) => false, onSuccess: (s) => s);
        _isTherapyActive = isActive;
        _updateTensLevel();
        _therapyStateController.add(Do.success(_isTherapyActive));
        return Do.success(unit);
      } catch (e) {
        // Unexpected error during read - fallback to optimistic update
        _isTherapyActive = newState;
        _updateTensLevel();
        _therapyStateController.add(Do.success(_isTherapyActive));
        _stateManager.signalError('Error confirming therapy state: $e');
        return Do.success(unit);
      }
    } catch (e) {
      return Do.failure(
          RemoteFailure.serverError('Error toggling therapy: $e'));
    }
  }

  @override
  Stream<Do<RemoteFailure, bool>> watchTherapyState() {
    // Create a command for reading therapy state
    final command = Commands(
      value: 0,
      commandType: 'therapyState',
      commandTime: DateTime.now(),
    );

    // Listen to indications for therapy state changes
    _bluetoothFacade.listenToIndication(command).listen(
      (result) {
        result.fold(
          onFailure: (failure) => _therapyStateController.add(
            Do.failure(RemoteFailure.serverError(failure.toString())),
          ),
          onSuccess: (value) {
            if (value.isNotEmpty) {
              _isTherapyActive = value[0] == 0x01;
              _therapyStateController.add(Do.success(_isTherapyActive));
            }
          },
        );
      },
      onError: (e) {
        _therapyStateController
            .add(Do.failure(RemoteFailure.serverError(e.toString())));
      },
    );

    return _therapyStateController.stream;
  }

  @override
  Future<Do<RemoteFailure, Unit>> unpairDevice() async {
    try {
      print('🔄 Starting device unpair process from remote repository');

      // First, get the currently connected device
      final deviceResult = await _bluetoothFacade.getConnectedDevice();

      // Check if we got a device
      final failure =
          deviceResult.fold(onFailure: (f) => f, onSuccess: (_) => null);
      if (failure != null) {
        print('❌ No device connected to unpair');
        return Do.failure(RemoteFailure.serverError('No device connected'));
      }

      final device = deviceResult.fold(
          onFailure: (_) => throw Exception('Device not found'),
          onSuccess: (d) => d);

      print('🛑 Stopping all remote monitoring and connections');

      // Stop all connection monitoring and polling FIRST
      _connectionManager.dispose();
      _pollingManager.stopAllPolling();

      // Cancel any pending reconnection timers
      _reconnectDebounceTimer?.cancel();
      _reconnectAllowTimer?.cancel();

      // Clear connection state
      _isConnected = false;
      _setupInProgress = false;

      print('🔌 Proceeding with device unpair via bluetooth facade');

      // Now call the bluetooth facade to unpair the device
      final unpairResult = await _bluetoothFacade.unpairDevice(device);

      // Handle the unpair result
      final unpairFailure =
          unpairResult.fold(onFailure: (f) => f, onSuccess: (_) => null);
      if (unpairFailure != null) {
        print('❌ Unpair failed: $unpairFailure');
        return Do.failure(RemoteFailure.serverError('Device unpair failed'));
      }

      print('✅ Device unpaired successfully');

      // Re-initialize connection manager after unpair to ensure it's ready for future connections
      print('🔄 Re-initializing connection manager for future connections');
      _connectionManager = ConnectionManager(
        bluetoothFacade: _bluetoothFacade,
        onDeviceConnected: _handleDeviceConnected,
        onDeviceDisconnected: _handleDeviceDisconnected,
      );

      return Do.success(unit);
    } catch (e) {
      print('❌ Unexpected error during unpair: $e');
      return Do.failure(RemoteFailure.unexpectedFailure('Unpair failed: $e'));
    }
  }

  // No need for override since dispose is not in RemoteControlFacade
  void dispose() {
    _reconnectDebounceTimer?.cancel();
    _reconnectAllowTimer?.cancel();
    _stateManager.dispose();
    _commandProcessor.dispose();
    _indicationManager.dispose();
    _connectionManager.dispose();
    _pollingManager.dispose();
    _therapyStateController.close();
    _settingChangeController.close();
  }
}
