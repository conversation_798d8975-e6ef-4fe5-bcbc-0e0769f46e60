import 'package:doso/doso.dart';
import 'package:remote/domain/failures/settings_prediction_failure.dart';
import 'package:remote/domain/model/settings_prediction_request_model.dart';
import 'package:remote/domain/model/settings_prediction_response_model.dart';

abstract class SettingsPredictionFacade {
  Future<Do<SettingsPredictionFailure, SettingsPredictionResponseModel>>
      predictSettings(SettingsPredictionRequestModel request);
}
