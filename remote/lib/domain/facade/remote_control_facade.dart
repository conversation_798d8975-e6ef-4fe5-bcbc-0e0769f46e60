import 'package:analytics/domain/models/therapy_session_event_model.dart';
import 'package:doso/doso.dart';

import 'package:remote/domain/core/unit.dart';
import 'package:remote/domain/failures/remote_failures.dart';
import 'package:remote/domain/model/heat_level.dart';

import '../model/device_model.dart';
import '../model/tens_level_model.dart';

abstract class RemoteControlFacade {
  Future<Do<RemoteFailure, Unit>> increaseHeat();
  Future<Do<RemoteFailure, Unit>> decreaseHeat();
  Stream<Do<RemoteFailure, HeatLevelModel>> watchHeatLevel();
  Future<Do<RemoteFailure, Unit>> increaseTens();
  Future<Do<RemoteFailure, Unit>> decreaseTens();
  Stream<Do<RemoteFailure, TensLevelModel>> watchTensLevel();
  Future<Do<RemoteFailure, Unit>> changeMode(int mode);
  Stream<Do<RemoteFailure, DeviceModel>> getDeviceStatus();
  Future<Do<RemoteFailure, DeviceModel>> getDeviceInformation();

  // New functions for device control
  Future<Do<RemoteFailure, Unit>> powerOffDevice();
  Future<Do<RemoteFailure, Unit>> toggleTherapy();
  Stream<Do<RemoteFailure, bool>> watchTherapyState();

  // Setting change events stream for analytics
  Stream<TherapySessionEventModel> get settingChangeStream;

  // Device management methods
  Future<Do<RemoteFailure, Unit>> unpairDevice();
}
