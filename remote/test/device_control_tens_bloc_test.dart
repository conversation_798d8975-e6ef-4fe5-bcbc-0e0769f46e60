// import 'package:bloc_test/bloc_test.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:mocktail/mocktail.dart';
// import 'package:fpdart/fpdart.dart';
// import 'package:remote/application/device_control_Tens_bloc/device_control_tens_bloc.dart';
// import 'package:remote/domain/failures/remote_failures.dart';
//
// import 'mock_classes/facade_mock_class.dart';
//
// void main() {
//   late MockRemoteControlFacade mockRemoteRepository;
//   late DeviceControlTensBloc bloc;
//
//   setUp(() {
//     mockRemoteRepository = MockRemoteControlFacade();
//     bloc = DeviceControlTensBloc(mockRemoteRepository);
//   });
//
//   group('DeviceControlTensBloc', () {
//     const remoteFailure = RemoteFailure.unexpectedFailure('Unexpected Failure');
//
//     blocTest<DeviceControlTensBloc, DeviceControlTensState>(
//       'emits [changeTensLevelSuccess] when increaseTens succeeds',
//       build: () {
//         when(() => mockRemoteRepository.increaseTens())
//             .thenAnswer((_) async => const Right(unit));
//         return bloc;
//       },
//       act: (bloc) => bloc.add(const DeviceControlTensEvent.increaseTens()),
//       expect: () => [const DeviceControlTensState.changeTensLevelSuccess()],
//       verify: (_) {
//         verify(() => mockRemoteRepository.increaseTens()).called(1);
//       },
//     );
//
//     blocTest<DeviceControlTensBloc, DeviceControlTensState>(
//       'emits [changeTensLevelFailure] when increaseTens fails',
//       build: () {
//         when(() => mockRemoteRepository.increaseTens())
//             .thenAnswer((_) async => const Left(remoteFailure));
//         return bloc;
//       },
//       act: (bloc) => bloc.add(const DeviceControlTensEvent.increaseTens()),
//       expect: () => [
//         const DeviceControlTensState.changeTensLevelFailure(remoteFailure)
//       ],
//       verify: (_) {
//         verify(() => mockRemoteRepository.increaseTens()).called(1);
//       },
//     );
//
//     blocTest<DeviceControlTensBloc, DeviceControlTensState>(
//       'emits [changeTensLevelSuccess] when decreaseTens succeeds',
//       build: () {
//         when(() => mockRemoteRepository.decreaseTens())
//             .thenAnswer((_) async => const Right(unit));
//         return bloc;
//       },
//       act: (bloc) => bloc.add(const DeviceControlTensEvent.decreaseTens()),
//       expect: () => [const DeviceControlTensState.changeTensLevelSuccess()],
//       verify: (_) {
//         verify(() => mockRemoteRepository.decreaseTens()).called(1);
//       },
//     );
//
//     blocTest<DeviceControlTensBloc, DeviceControlTensState>(
//       'emits [changeTensLevelFailure] when decreaseTens fails',
//       build: () {
//         when(() => mockRemoteRepository.decreaseTens())
//             .thenAnswer((_) async => const Left(remoteFailure));
//         return bloc;
//       },
//       act: (bloc) => bloc.add(const DeviceControlTensEvent.decreaseTens()),
//       expect: () => [
//         const DeviceControlTensState.changeTensLevelFailure(remoteFailure)
//       ],
//       verify: (_) {
//         verify(() => mockRemoteRepository.decreaseTens()).called(1);
//       },
//     );
//   });
// }
