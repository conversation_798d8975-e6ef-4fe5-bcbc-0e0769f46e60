// import 'package:bloc_test/bloc_test.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:mocktail/mocktail.dart';
// import 'package:fpdart/fpdart.dart';
// import 'package:remote/application/device_control_heat_watcher_bloc/device_control_heat_watcher_bloc.dart';
// import 'package:remote/domain/failures/remote_failures.dart';
// import 'package:remote/domain/model/heat_level.dart';
// import 'package:remote/domain/facade/remote_control_facade.dart';
//
// class MockRemoteControlFacade extends Mock implements RemoteControlFacade {}
//
// void main() {
//   late MockRemoteControlFacade mockRemoteRepository;
//   late DeviceControlHeatWatcherBloc bloc;
//
//   setUp(() {
//     mockRemoteRepository = MockRemoteControlFacade();
//     bloc = DeviceControlHeatWatcherBloc(mockRemoteRepository);
//   });
//
//   group('DeviceControlHeatWatcherBloc', () {
//     final heatLevel = HeatLevelModel(selectedHeatLevel: 5, actualHeatLevel: 4);
//     const remoteFailure = RemoteFailure.unexpectedFailure('Unexpected Failure');
//
//     Stream<Either<RemoteFailure, HeatLevelModel>> mockHeatLevelStream(
//         List<Either<RemoteFailure, HeatLevelModel>> responses) {
//       return Stream.fromIterable(responses);
//     }
//
//     blocTest<DeviceControlHeatWatcherBloc, DeviceControlHeatWatcherState>(
//       'emits failure state when stream fails',
//       build: () {
//         when(() => mockRemoteRepository.watchHeatLevel())
//             .thenAnswer((_) => mockHeatLevelStream([const Left(remoteFailure)]));
//         return bloc;
//       },
//       act: (bloc) => bloc.add(const DeviceControlHeatWatcherEvent.watchAllStarted()),
//       expect: () => [
//         DeviceControlHeatWatcherState.initial(),
//         DeviceControlHeatWatcherState(
//           selectedHeatLevel: 0,
//           actualHeatLevel: 0,
//           failureOrSuccessOption: Some(const Left(remoteFailure)),
//         ),
//       ],
//     );
//
//     blocTest<DeviceControlHeatWatcherBloc, DeviceControlHeatWatcherState>(
//       'emits updated state when heat level changes successfully',
//       build: () {
//         when(() => mockRemoteRepository.watchHeatLevel())
//             .thenAnswer((_) => mockHeatLevelStream([Right(heatLevel)]));
//         return bloc;
//       },
//       act: (bloc) => bloc.add(const DeviceControlHeatWatcherEvent.watchAllStarted()),
//       expect: () => [
//         DeviceControlHeatWatcherState.initial(),
//         DeviceControlHeatWatcherState(
//           selectedHeatLevel: heatLevel.selectedHeatLevel,
//           actualHeatLevel: heatLevel.actualHeatLevel,
//           failureOrSuccessOption: Some(Right(unit)),
//         ),
//       ],
//     );
//
//     blocTest<DeviceControlHeatWatcherBloc, DeviceControlHeatWatcherState>(
//       'updates state when heat level changes multiple times',
//       build: () {
//         when(() => mockRemoteRepository.watchHeatLevel()).thenAnswer(
//               (_) => mockHeatLevelStream([
//             Right(HeatLevelModel(selectedHeatLevel: 3, actualHeatLevel: 3)),
//             Right(HeatLevelModel(selectedHeatLevel: 4, actualHeatLevel: 4)),
//           ]),
//         );
//         return bloc;
//       },
//       act: (bloc) => bloc.add(const DeviceControlHeatWatcherEvent.watchAllStarted()),
//       expect: () => [
//         DeviceControlHeatWatcherState.initial(),
//         DeviceControlHeatWatcherState(
//           selectedHeatLevel: 3,
//           actualHeatLevel: 3,
//           failureOrSuccessOption: Some(Right(unit)),
//         ),
//         DeviceControlHeatWatcherState(
//           selectedHeatLevel: 4,
//           actualHeatLevel: 4,
//           failureOrSuccessOption: Some(Right(unit)),
//         ),
//       ],
//     );
//
//     blocTest<DeviceControlHeatWatcherBloc, DeviceControlHeatWatcherState>(
//       'cancels subscription on close',
//       build: () {
//         when(() => mockRemoteRepository.watchHeatLevel())
//             .thenAnswer((_) => mockHeatLevelStream([Right(heatLevel)]));
//         return bloc;
//       },
//       act: (bloc) {
//         bloc.add(const DeviceControlHeatWatcherEvent.watchAllStarted());
//       },
//       wait: const Duration(milliseconds: 100), // Add a small delay to ensure event processing
//       verify: (_) {
//         verify(() => mockRemoteRepository.watchHeatLevel()).called(1);
//       },
//       tearDown: () {
//         bloc.close(); // Move bloc closing to tearDown
//       },
//     );
//   });
// }
