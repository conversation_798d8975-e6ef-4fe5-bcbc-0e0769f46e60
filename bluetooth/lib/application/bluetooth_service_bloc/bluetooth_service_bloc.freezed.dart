// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bluetooth_service_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BluetoothServiceEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is BluetoothServiceEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothServiceEvent()';
  }
}

/// @nodoc
class $BluetoothServiceEventCopyWith<$Res> {
  $BluetoothServiceEventCopyWith(
      BluetoothServiceEvent _, $Res Function(BluetoothServiceEvent) __);
}

/// @nodoc

class CheckBluetooth implements BluetoothServiceEvent {
  const CheckBluetooth();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CheckBluetooth);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothServiceEvent.checkBluetooth()';
  }
}

/// @nodoc

class CheckRecentDevice implements BluetoothServiceEvent {
  const CheckRecentDevice();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CheckRecentDevice);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothServiceEvent.checkRecentDevice()';
  }
}

/// @nodoc

class SavedDevicesReceived implements BluetoothServiceEvent {
  const SavedDevicesReceived(this.failureOrDevices);

  final Do<BluetoothFailure, List<DeviceModel>> failureOrDevices;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SavedDevicesReceivedCopyWith<SavedDevicesReceived> get copyWith =>
      _$SavedDevicesReceivedCopyWithImpl<SavedDevicesReceived>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SavedDevicesReceived &&
            (identical(other.failureOrDevices, failureOrDevices) ||
                other.failureOrDevices == failureOrDevices));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrDevices);

  @override
  String toString() {
    return 'BluetoothServiceEvent.savedDevicesReceived(failureOrDevices: $failureOrDevices)';
  }
}

/// @nodoc
abstract mixin class $SavedDevicesReceivedCopyWith<$Res>
    implements $BluetoothServiceEventCopyWith<$Res> {
  factory $SavedDevicesReceivedCopyWith(SavedDevicesReceived value,
          $Res Function(SavedDevicesReceived) _then) =
      _$SavedDevicesReceivedCopyWithImpl;
  @useResult
  $Res call({Do<BluetoothFailure, List<DeviceModel>> failureOrDevices});
}

/// @nodoc
class _$SavedDevicesReceivedCopyWithImpl<$Res>
    implements $SavedDevicesReceivedCopyWith<$Res> {
  _$SavedDevicesReceivedCopyWithImpl(this._self, this._then);

  final SavedDevicesReceived _self;
  final $Res Function(SavedDevicesReceived) _then;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureOrDevices = null,
  }) {
    return _then(SavedDevicesReceived(
      null == failureOrDevices
          ? _self.failureOrDevices
          : failureOrDevices // ignore: cast_nullable_to_non_nullable
              as Do<BluetoothFailure, List<DeviceModel>>,
    ));
  }
}

/// @nodoc

class StartSearch implements BluetoothServiceEvent {
  const StartSearch(this.isBackgroundSearch);

  final bool isBackgroundSearch;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $StartSearchCopyWith<StartSearch> get copyWith =>
      _$StartSearchCopyWithImpl<StartSearch>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is StartSearch &&
            (identical(other.isBackgroundSearch, isBackgroundSearch) ||
                other.isBackgroundSearch == isBackgroundSearch));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isBackgroundSearch);

  @override
  String toString() {
    return 'BluetoothServiceEvent.startSearch(isBackgroundSearch: $isBackgroundSearch)';
  }
}

/// @nodoc
abstract mixin class $StartSearchCopyWith<$Res>
    implements $BluetoothServiceEventCopyWith<$Res> {
  factory $StartSearchCopyWith(
          StartSearch value, $Res Function(StartSearch) _then) =
      _$StartSearchCopyWithImpl;
  @useResult
  $Res call({bool isBackgroundSearch});
}

/// @nodoc
class _$StartSearchCopyWithImpl<$Res> implements $StartSearchCopyWith<$Res> {
  _$StartSearchCopyWithImpl(this._self, this._then);

  final StartSearch _self;
  final $Res Function(StartSearch) _then;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isBackgroundSearch = null,
  }) {
    return _then(StartSearch(
      null == isBackgroundSearch
          ? _self.isBackgroundSearch
          : isBackgroundSearch // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class ConnectToDevice implements BluetoothServiceEvent {
  const ConnectToDevice(this.deviceId, {this.initial});

  final String deviceId;
  final bool? initial;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ConnectToDeviceCopyWith<ConnectToDevice> get copyWith =>
      _$ConnectToDeviceCopyWithImpl<ConnectToDevice>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ConnectToDevice &&
            (identical(other.deviceId, deviceId) ||
                other.deviceId == deviceId) &&
            (identical(other.initial, initial) || other.initial == initial));
  }

  @override
  int get hashCode => Object.hash(runtimeType, deviceId, initial);

  @override
  String toString() {
    return 'BluetoothServiceEvent.connectToDevice(deviceId: $deviceId, initial: $initial)';
  }
}

/// @nodoc
abstract mixin class $ConnectToDeviceCopyWith<$Res>
    implements $BluetoothServiceEventCopyWith<$Res> {
  factory $ConnectToDeviceCopyWith(
          ConnectToDevice value, $Res Function(ConnectToDevice) _then) =
      _$ConnectToDeviceCopyWithImpl;
  @useResult
  $Res call({String deviceId, bool? initial});
}

/// @nodoc
class _$ConnectToDeviceCopyWithImpl<$Res>
    implements $ConnectToDeviceCopyWith<$Res> {
  _$ConnectToDeviceCopyWithImpl(this._self, this._then);

  final ConnectToDevice _self;
  final $Res Function(ConnectToDevice) _then;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? deviceId = null,
    Object? initial = freezed,
  }) {
    return _then(ConnectToDevice(
      null == deviceId
          ? _self.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String,
      initial: freezed == initial
          ? _self.initial
          : initial // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class DeviceDisconnected implements BluetoothServiceEvent {
  const DeviceDisconnected(this.device);

  final BluetoothDevice device;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DeviceDisconnectedCopyWith<DeviceDisconnected> get copyWith =>
      _$DeviceDisconnectedCopyWithImpl<DeviceDisconnected>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DeviceDisconnected &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  @override
  String toString() {
    return 'BluetoothServiceEvent.deviceDisconnected(device: $device)';
  }
}

/// @nodoc
abstract mixin class $DeviceDisconnectedCopyWith<$Res>
    implements $BluetoothServiceEventCopyWith<$Res> {
  factory $DeviceDisconnectedCopyWith(
          DeviceDisconnected value, $Res Function(DeviceDisconnected) _then) =
      _$DeviceDisconnectedCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class _$DeviceDisconnectedCopyWithImpl<$Res>
    implements $DeviceDisconnectedCopyWith<$Res> {
  _$DeviceDisconnectedCopyWithImpl(this._self, this._then);

  final DeviceDisconnected _self;
  final $Res Function(DeviceDisconnected) _then;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
  }) {
    return _then(DeviceDisconnected(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class DisconnectDevice implements BluetoothServiceEvent {
  const DisconnectDevice(this.device);

  final BluetoothDevice device;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DisconnectDeviceCopyWith<DisconnectDevice> get copyWith =>
      _$DisconnectDeviceCopyWithImpl<DisconnectDevice>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DisconnectDevice &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  @override
  String toString() {
    return 'BluetoothServiceEvent.disconnectDevice(device: $device)';
  }
}

/// @nodoc
abstract mixin class $DisconnectDeviceCopyWith<$Res>
    implements $BluetoothServiceEventCopyWith<$Res> {
  factory $DisconnectDeviceCopyWith(
          DisconnectDevice value, $Res Function(DisconnectDevice) _then) =
      _$DisconnectDeviceCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class _$DisconnectDeviceCopyWithImpl<$Res>
    implements $DisconnectDeviceCopyWith<$Res> {
  _$DisconnectDeviceCopyWithImpl(this._self, this._then);

  final DisconnectDevice _self;
  final $Res Function(DisconnectDevice) _then;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
  }) {
    return _then(DisconnectDevice(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class UnpairDevice implements BluetoothServiceEvent {
  const UnpairDevice(this.device);

  final BluetoothDevice device;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UnpairDeviceCopyWith<UnpairDevice> get copyWith =>
      _$UnpairDeviceCopyWithImpl<UnpairDevice>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UnpairDevice &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  @override
  String toString() {
    return 'BluetoothServiceEvent.unpairDevice(device: $device)';
  }
}

/// @nodoc
abstract mixin class $UnpairDeviceCopyWith<$Res>
    implements $BluetoothServiceEventCopyWith<$Res> {
  factory $UnpairDeviceCopyWith(
          UnpairDevice value, $Res Function(UnpairDevice) _then) =
      _$UnpairDeviceCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class _$UnpairDeviceCopyWithImpl<$Res> implements $UnpairDeviceCopyWith<$Res> {
  _$UnpairDeviceCopyWithImpl(this._self, this._then);

  final UnpairDevice _self;
  final $Res Function(UnpairDevice) _then;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
  }) {
    return _then(UnpairDevice(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class ListenToDevice implements BluetoothServiceEvent {
  const ListenToDevice(this.device);

  final BluetoothDevice device;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ListenToDeviceCopyWith<ListenToDevice> get copyWith =>
      _$ListenToDeviceCopyWithImpl<ListenToDevice>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ListenToDevice &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  @override
  String toString() {
    return 'BluetoothServiceEvent.listenToDevice(device: $device)';
  }
}

/// @nodoc
abstract mixin class $ListenToDeviceCopyWith<$Res>
    implements $BluetoothServiceEventCopyWith<$Res> {
  factory $ListenToDeviceCopyWith(
          ListenToDevice value, $Res Function(ListenToDevice) _then) =
      _$ListenToDeviceCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class _$ListenToDeviceCopyWithImpl<$Res>
    implements $ListenToDeviceCopyWith<$Res> {
  _$ListenToDeviceCopyWithImpl(this._self, this._then);

  final ListenToDevice _self;
  final $Res Function(ListenToDevice) _then;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
  }) {
    return _then(ListenToDevice(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class CheckConnection implements BluetoothServiceEvent {
  const CheckConnection();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CheckConnection);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothServiceEvent.checkConnection()';
  }
}

/// @nodoc

class ReconnectDevice implements BluetoothServiceEvent {
  const ReconnectDevice(this.device);

  final BluetoothDevice device;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ReconnectDeviceCopyWith<ReconnectDevice> get copyWith =>
      _$ReconnectDeviceCopyWithImpl<ReconnectDevice>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ReconnectDevice &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  @override
  String toString() {
    return 'BluetoothServiceEvent.reconnectDevice(device: $device)';
  }
}

/// @nodoc
abstract mixin class $ReconnectDeviceCopyWith<$Res>
    implements $BluetoothServiceEventCopyWith<$Res> {
  factory $ReconnectDeviceCopyWith(
          ReconnectDevice value, $Res Function(ReconnectDevice) _then) =
      _$ReconnectDeviceCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class _$ReconnectDeviceCopyWithImpl<$Res>
    implements $ReconnectDeviceCopyWith<$Res> {
  _$ReconnectDeviceCopyWithImpl(this._self, this._then);

  final ReconnectDevice _self;
  final $Res Function(ReconnectDevice) _then;

  /// Create a copy of BluetoothServiceEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
  }) {
    return _then(ReconnectDevice(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class MoveToLandingPage implements BluetoothServiceEvent {
  const MoveToLandingPage();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is MoveToLandingPage);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothServiceEvent.moveToLandingPage()';
  }
}

/// @nodoc
mixin _$BluetoothServiceState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is BluetoothServiceState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothServiceState()';
  }
}

/// @nodoc
class $BluetoothServiceStateCopyWith<$Res> {
  $BluetoothServiceStateCopyWith(
      BluetoothServiceState _, $Res Function(BluetoothServiceState) __);
}

/// @nodoc

class Initial implements BluetoothServiceState {
  const Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothServiceState.initial()';
  }
}

/// @nodoc

class BluetoothOn implements BluetoothServiceState {
  const BluetoothOn();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is BluetoothOn);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothServiceState.bluetoothOn()';
  }
}

/// @nodoc

class BluetoothOff implements BluetoothServiceState {
  const BluetoothOff();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is BluetoothOff);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothServiceState.bluetoothOff()';
  }
}

/// @nodoc

class CheckingRecentDevice implements BluetoothServiceState {
  const CheckingRecentDevice();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CheckingRecentDevice);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothServiceState.checkingRecentDevice()';
  }
}

/// @nodoc

class FetchingSavedDevices implements BluetoothServiceState {
  const FetchingSavedDevices();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is FetchingSavedDevices);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothServiceState.fetchingSavedDevices()';
  }
}

/// @nodoc

class BluetoothSearching implements BluetoothServiceState {
  const BluetoothSearching();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is BluetoothSearching);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothServiceState.bluetoothSearching()';
  }
}

/// @nodoc

class Connecting implements BluetoothServiceState {
  const Connecting();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Connecting);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothServiceState.connecting()';
  }
}

/// @nodoc

class Reconnecting implements BluetoothServiceState {
  const Reconnecting(this.device);

  final BluetoothDevice device;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ReconnectingCopyWith<Reconnecting> get copyWith =>
      _$ReconnectingCopyWithImpl<Reconnecting>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Reconnecting &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  @override
  String toString() {
    return 'BluetoothServiceState.reconnecting(device: $device)';
  }
}

/// @nodoc
abstract mixin class $ReconnectingCopyWith<$Res>
    implements $BluetoothServiceStateCopyWith<$Res> {
  factory $ReconnectingCopyWith(
          Reconnecting value, $Res Function(Reconnecting) _then) =
      _$ReconnectingCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class _$ReconnectingCopyWithImpl<$Res> implements $ReconnectingCopyWith<$Res> {
  _$ReconnectingCopyWithImpl(this._self, this._then);

  final Reconnecting _self;
  final $Res Function(Reconnecting) _then;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
  }) {
    return _then(Reconnecting(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class SavedDevices implements BluetoothServiceState {
  const SavedDevices(final List<DeviceModel> devices, {this.version = 0})
      : _devices = devices;

  final List<DeviceModel> _devices;
  List<DeviceModel> get devices {
    if (_devices is EqualUnmodifiableListView) return _devices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_devices);
  }

  @JsonKey()
  final int version;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SavedDevicesCopyWith<SavedDevices> get copyWith =>
      _$SavedDevicesCopyWithImpl<SavedDevices>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SavedDevices &&
            const DeepCollectionEquality().equals(other._devices, _devices) &&
            (identical(other.version, version) || other.version == version));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_devices), version);

  @override
  String toString() {
    return 'BluetoothServiceState.savedDevices(devices: $devices, version: $version)';
  }
}

/// @nodoc
abstract mixin class $SavedDevicesCopyWith<$Res>
    implements $BluetoothServiceStateCopyWith<$Res> {
  factory $SavedDevicesCopyWith(
          SavedDevices value, $Res Function(SavedDevices) _then) =
      _$SavedDevicesCopyWithImpl;
  @useResult
  $Res call({List<DeviceModel> devices, int version});
}

/// @nodoc
class _$SavedDevicesCopyWithImpl<$Res> implements $SavedDevicesCopyWith<$Res> {
  _$SavedDevicesCopyWithImpl(this._self, this._then);

  final SavedDevices _self;
  final $Res Function(SavedDevices) _then;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? devices = null,
    Object? version = null,
  }) {
    return _then(SavedDevices(
      null == devices
          ? _self._devices
          : devices // ignore: cast_nullable_to_non_nullable
              as List<DeviceModel>,
      version: null == version
          ? _self.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class BluetoothAvailableTypeDevices implements BluetoothServiceState {
  const BluetoothAvailableTypeDevices(final List<BluetoothDevice?>? devices)
      : _devices = devices;

  final List<BluetoothDevice?>? _devices;
  List<BluetoothDevice?>? get devices {
    final value = _devices;
    if (value == null) return null;
    if (_devices is EqualUnmodifiableListView) return _devices;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $BluetoothAvailableTypeDevicesCopyWith<BluetoothAvailableTypeDevices>
      get copyWith => _$BluetoothAvailableTypeDevicesCopyWithImpl<
          BluetoothAvailableTypeDevices>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is BluetoothAvailableTypeDevices &&
            const DeepCollectionEquality().equals(other._devices, _devices));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_devices));

  @override
  String toString() {
    return 'BluetoothServiceState.bluetoothAvailableTypeDevices(devices: $devices)';
  }
}

/// @nodoc
abstract mixin class $BluetoothAvailableTypeDevicesCopyWith<$Res>
    implements $BluetoothServiceStateCopyWith<$Res> {
  factory $BluetoothAvailableTypeDevicesCopyWith(
          BluetoothAvailableTypeDevices value,
          $Res Function(BluetoothAvailableTypeDevices) _then) =
      _$BluetoothAvailableTypeDevicesCopyWithImpl;
  @useResult
  $Res call({List<BluetoothDevice?>? devices});
}

/// @nodoc
class _$BluetoothAvailableTypeDevicesCopyWithImpl<$Res>
    implements $BluetoothAvailableTypeDevicesCopyWith<$Res> {
  _$BluetoothAvailableTypeDevicesCopyWithImpl(this._self, this._then);

  final BluetoothAvailableTypeDevices _self;
  final $Res Function(BluetoothAvailableTypeDevices) _then;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? devices = freezed,
  }) {
    return _then(BluetoothAvailableTypeDevices(
      freezed == devices
          ? _self._devices
          : devices // ignore: cast_nullable_to_non_nullable
              as List<BluetoothDevice?>?,
    ));
  }
}

/// @nodoc

class Connected implements BluetoothServiceState {
  const Connected(this.device);

  final BluetoothDevice device;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ConnectedCopyWith<Connected> get copyWith =>
      _$ConnectedCopyWithImpl<Connected>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Connected &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  @override
  String toString() {
    return 'BluetoothServiceState.connected(device: $device)';
  }
}

/// @nodoc
abstract mixin class $ConnectedCopyWith<$Res>
    implements $BluetoothServiceStateCopyWith<$Res> {
  factory $ConnectedCopyWith(Connected value, $Res Function(Connected) _then) =
      _$ConnectedCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class _$ConnectedCopyWithImpl<$Res> implements $ConnectedCopyWith<$Res> {
  _$ConnectedCopyWithImpl(this._self, this._then);

  final Connected _self;
  final $Res Function(Connected) _then;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
  }) {
    return _then(Connected(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class Disconnected implements BluetoothServiceState {
  const Disconnected(this.device);

  final BluetoothDevice device;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DisconnectedCopyWith<Disconnected> get copyWith =>
      _$DisconnectedCopyWithImpl<Disconnected>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Disconnected &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  @override
  String toString() {
    return 'BluetoothServiceState.disconnected(device: $device)';
  }
}

/// @nodoc
abstract mixin class $DisconnectedCopyWith<$Res>
    implements $BluetoothServiceStateCopyWith<$Res> {
  factory $DisconnectedCopyWith(
          Disconnected value, $Res Function(Disconnected) _then) =
      _$DisconnectedCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class _$DisconnectedCopyWithImpl<$Res> implements $DisconnectedCopyWith<$Res> {
  _$DisconnectedCopyWithImpl(this._self, this._then);

  final Disconnected _self;
  final $Res Function(Disconnected) _then;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
  }) {
    return _then(Disconnected(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class Unpairing implements BluetoothServiceState {
  const Unpairing(this.device);

  final BluetoothDevice device;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UnpairingCopyWith<Unpairing> get copyWith =>
      _$UnpairingCopyWithImpl<Unpairing>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Unpairing &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  @override
  String toString() {
    return 'BluetoothServiceState.unpairing(device: $device)';
  }
}

/// @nodoc
abstract mixin class $UnpairingCopyWith<$Res>
    implements $BluetoothServiceStateCopyWith<$Res> {
  factory $UnpairingCopyWith(Unpairing value, $Res Function(Unpairing) _then) =
      _$UnpairingCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class _$UnpairingCopyWithImpl<$Res> implements $UnpairingCopyWith<$Res> {
  _$UnpairingCopyWithImpl(this._self, this._then);

  final Unpairing _self;
  final $Res Function(Unpairing) _then;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
  }) {
    return _then(Unpairing(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class DeviceUnpaired implements BluetoothServiceState {
  const DeviceUnpaired(this.device);

  final BluetoothDevice device;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DeviceUnpairedCopyWith<DeviceUnpaired> get copyWith =>
      _$DeviceUnpairedCopyWithImpl<DeviceUnpaired>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DeviceUnpaired &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  @override
  String toString() {
    return 'BluetoothServiceState.deviceUnpaired(device: $device)';
  }
}

/// @nodoc
abstract mixin class $DeviceUnpairedCopyWith<$Res>
    implements $BluetoothServiceStateCopyWith<$Res> {
  factory $DeviceUnpairedCopyWith(
          DeviceUnpaired value, $Res Function(DeviceUnpaired) _then) =
      _$DeviceUnpairedCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class _$DeviceUnpairedCopyWithImpl<$Res>
    implements $DeviceUnpairedCopyWith<$Res> {
  _$DeviceUnpairedCopyWithImpl(this._self, this._then);

  final DeviceUnpaired _self;
  final $Res Function(DeviceUnpaired) _then;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
  }) {
    return _then(DeviceUnpaired(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class BluetoothError implements BluetoothServiceState {
  const BluetoothError(this.failure);

  final BluetoothFailure failure;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $BluetoothErrorCopyWith<BluetoothError> get copyWith =>
      _$BluetoothErrorCopyWithImpl<BluetoothError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is BluetoothError &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'BluetoothServiceState.bluetoothError(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class $BluetoothErrorCopyWith<$Res>
    implements $BluetoothServiceStateCopyWith<$Res> {
  factory $BluetoothErrorCopyWith(
          BluetoothError value, $Res Function(BluetoothError) _then) =
      _$BluetoothErrorCopyWithImpl;
  @useResult
  $Res call({BluetoothFailure failure});

  $BluetoothFailureCopyWith<$Res> get failure;
}

/// @nodoc
class _$BluetoothErrorCopyWithImpl<$Res>
    implements $BluetoothErrorCopyWith<$Res> {
  _$BluetoothErrorCopyWithImpl(this._self, this._then);

  final BluetoothError _self;
  final $Res Function(BluetoothError) _then;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(BluetoothError(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as BluetoothFailure,
    ));
  }

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BluetoothFailureCopyWith<$Res> get failure {
    return $BluetoothFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

/// @nodoc

class ConnectionIntro implements BluetoothServiceState {
  const ConnectionIntro(this.device);

  final BluetoothDevice device;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ConnectionIntroCopyWith<ConnectionIntro> get copyWith =>
      _$ConnectionIntroCopyWithImpl<ConnectionIntro>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ConnectionIntro &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  @override
  String toString() {
    return 'BluetoothServiceState.connectionIntro(device: $device)';
  }
}

/// @nodoc
abstract mixin class $ConnectionIntroCopyWith<$Res>
    implements $BluetoothServiceStateCopyWith<$Res> {
  factory $ConnectionIntroCopyWith(
          ConnectionIntro value, $Res Function(ConnectionIntro) _then) =
      _$ConnectionIntroCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class _$ConnectionIntroCopyWithImpl<$Res>
    implements $ConnectionIntroCopyWith<$Res> {
  _$ConnectionIntroCopyWithImpl(this._self, this._then);

  final ConnectionIntro _self;
  final $Res Function(ConnectionIntro) _then;

  /// Create a copy of BluetoothServiceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
  }) {
    return _then(ConnectionIntro(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class LandingPageState implements BluetoothServiceState {
  const LandingPageState();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is LandingPageState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothServiceState.landingPageState()';
  }
}

// dart format on
