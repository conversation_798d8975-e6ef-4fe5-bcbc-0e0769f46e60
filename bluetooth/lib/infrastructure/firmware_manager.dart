import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:archive/archive.dart';

import '../domain/facade/ota_firmware_facade.dart';
import '../domain/model/ota_firmware_model.dart';
import 'ota_firmware_facade_impl.dart';

/// Firmware Manager for handling real DFU application packages
/// Now supports both legacy asset-based loading and new Firebase Storage integration
class FirmwareManager {
  static const String _firmwareAssetPath =
      'assets/firmware/dfu_application_2.zip';
  static const String _firmwareFileName = 'dfu_application_2.zip';

  // Firebase-based firmware facade for new OTA system
  static IOtaFirmwareFacade? _otaFirmwareFacade;

  /// Initialize the firmware manager with Firebase integration
  static void initialize(IOtaFirmwareFacade otaFirmwareFacade) {
    _otaFirmwareFacade = otaFirmwareFacade;
  }

  /// Load the DFU application package from assets
  static Future<Uint8List> loadFirmwarePackage() async {
    try {
      print('🔍 [FIRMWARE DEBUG] Loading firmware from assets...');
      print('🔍 [FIRMWARE DEBUG] Asset path: $_firmwareAssetPath');

      // Try to load from assets first
      final ByteData data = await rootBundle.load(_firmwareAssetPath);
      final bytes = data.buffer.asUint8List();
      print(
          '✅ [FIRMWARE DEBUG] Successfully loaded ${bytes.length} bytes from app assets!');
      print(
          '🎉 [FIRMWARE DEBUG] Asset loading successful - firmware is bundled with app');
      return bytes;
    } catch (e) {
      print('❌ [FIRMWARE DEBUG] Asset loading failed: $e');
      print('🔍 [FIRMWARE DEBUG] Trying to load from package directory...');

      // If asset loading fails, try to read from the bluetooth package directory
      return await _loadFromPackageDirectory();
    }
  }

  /// Load firmware from the bluetooth package directory
  static Future<Uint8List> _loadFromPackageDirectory() async {
    try {
      print('🔍 [FIRMWARE DEBUG] Loading from package directory...');
      final currentDir = Directory.current;
      print('🔍 [FIRMWARE DEBUG] Current directory: "${currentDir.path}"');
      print(
          '🔍 [FIRMWARE DEBUG] Current directory absolute: "${currentDir.absolute.path}"');

      // Try multiple possible paths based on different execution contexts
      final possiblePaths = [
        // When running from app root
        '${currentDir.path}/bluetooth/juno_fw_distributions-cybersec-test/$_firmwareFileName',
        // When running from different contexts
        '${currentDir.absolute.path}/bluetooth/juno_fw_distributions-cybersec-test/$_firmwareFileName',
        // Relative paths
        './bluetooth/juno_fw_distributions-cybersec-test/$_firmwareFileName',
        '../bluetooth/juno_fw_distributions-cybersec-test/$_firmwareFileName',
        // Direct path from project root
        'bluetooth/juno_fw_distributions-cybersec-test/$_firmwareFileName',
      ];

      print(
          '🔍 [FIRMWARE DEBUG] Trying ${possiblePaths.length} possible paths...');

      for (int i = 0; i < possiblePaths.length; i++) {
        final firmwarePath = possiblePaths[i];
        print('🔍 [FIRMWARE DEBUG] Path ${i + 1}: $firmwarePath');

        final firmwareFile = File(firmwarePath);
        final fileExists = await firmwareFile.exists();
        print('🔍 [FIRMWARE DEBUG] File exists: $fileExists');

        if (fileExists) {
          print('✅ [FIRMWARE DEBUG] Loading firmware file...');
          final bytes = await firmwareFile.readAsBytes();
          print('✅ [FIRMWARE DEBUG] Loaded ${bytes.length} bytes');
          return bytes;
        }
      }

      print(
          '❌ [FIRMWARE DEBUG] Firmware file not found in any of the attempted paths');
      throw Exception('Firmware file not found in any of the attempted paths');
    } catch (e) {
      throw Exception('Failed to load firmware package: $e');
    }
  }

  /// Extract and validate the DFU package contents
  static Future<DfuPackageInfo> extractPackageInfo(
      Uint8List packageData) async {
    try {
      final archive = ZipDecoder().decodeBytes(packageData);

      String? manifestContent;
      Uint8List? applicationData;

      for (final file in archive) {
        if (file.name == 'manifest.json') {
          manifestContent = String.fromCharCodes(file.content as List<int>);
        } else if (file.name.endsWith('.bin') || file.name.endsWith('.hex')) {
          applicationData = Uint8List.fromList(file.content as List<int>);
        }
      }

      if (manifestContent == null) {
        throw Exception('Manifest file not found in DFU package');
      }

      if (applicationData == null) {
        throw Exception('Application data not found in DFU package');
      }

      return DfuPackageInfo(
        manifestContent: manifestContent,
        applicationData: applicationData,
        packageSize: packageData.length,
      );
    } catch (e) {
      throw Exception('Failed to extract DFU package: $e');
    }
  }

  /// Get firmware version from the current device firmware files
  static Future<String> getCurrentFirmwareVersion() async {
    try {
      // In a real implementation, this would read version info from the device
      // For now, we'll return a mock version based on the hex files
      return '1.0.0'; // This should be read from device or manifest
    } catch (e) {
      return 'Unknown';
    }
  }

  /// Get the latest available firmware version from the DFU package
  static Future<String> getLatestFirmwareVersion() async {
    try {
      print('🔍 [FIRMWARE DEBUG] Getting latest firmware version...');
      final packageData = await loadFirmwarePackage();
      print('✅ [FIRMWARE DEBUG] Package loaded for version check');

      final packageInfo = await extractPackageInfo(packageData);
      print(
          '🔍 [FIRMWARE DEBUG] Package info extracted: ${packageInfo.isValid}');

      // Parse manifest to get version info
      // This is a simplified version - in reality you'd parse the JSON manifest
      // For now, return a version that's different from the device version (1.0)
      const latestVersion = '2.0.0';
      print('✅ [FIRMWARE DEBUG] Latest version determined: $latestVersion');
      return latestVersion;
    } catch (e) {
      print('❌ [FIRMWARE DEBUG] Error getting latest version: $e');
      return 'Unknown';
    }
  }

  /// Validate firmware compatibility with the device
  static Future<bool> validateFirmwareCompatibility(
    Uint8List firmwareData,
    String deviceHardwareVersion,
  ) async {
    try {
      final packageInfo = await extractPackageInfo(firmwareData);

      // In a real implementation, this would:
      // 1. Parse the manifest.json for hardware compatibility
      // 2. Verify ECDSA P-256 signature
      // 3. Check MCUboot header
      // 4. Validate checksums

      // For now, we'll do basic validation
      if (packageInfo.applicationData.length < 1024) {
        return false; // Too small to be valid firmware
      }

      // Check if it's a valid DFU package structure
      return packageInfo.manifestContent.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Save firmware package to temporary directory for OTA process
  static Future<String> saveFirmwareToTemp(Uint8List firmwareData) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/$_firmwareFileName');
      await tempFile.writeAsBytes(firmwareData);
      return tempFile.path;
    } catch (e) {
      throw Exception('Failed to save firmware to temporary directory: $e');
    }
  }

  /// Clean up temporary firmware files
  static Future<void> cleanupTempFiles() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/$_firmwareFileName');
      if (await tempFile.exists()) {
        await tempFile.delete();
      }
    } catch (e) {
      // Ignore cleanup errors
    }
  }

  /// Get firmware file size in bytes
  static Future<int> getFirmwareSize() async {
    try {
      final packageData = await loadFirmwarePackage();
      return packageData.length;
    } catch (e) {
      return 0;
    }
  }

  /// Calculate firmware checksum (SHA-256)
  static Future<String> calculateFirmwareChecksum(
      Uint8List firmwareData) async {
    try {
      // In a real implementation, you'd use crypto package for SHA-256
      // For now, return a mock checksum
      return 'sha256:${firmwareData.length.toRadixString(16)}';
    } catch (e) {
      return 'unknown';
    }
  }

  /// Load firmware from Firebase Storage using OTA firmware model
  /// This is the new method that replaces asset-based loading
  static Future<Uint8List> loadFirmwareFromFirebase(
    OtaFirmwareModel firmware,
  ) async {
    if (_otaFirmwareFacade == null) {
      throw Exception('FirmwareManager not initialized with Firebase facade');
    }

    print('🔍 [FIRMWARE DEBUG] Loading firmware from Firebase Storage...');
    print('   Firmware ID: ${firmware.firmwareId}');
    print('   Version: ${firmware.version}');
    print('   Size: ${firmware.fileSize} bytes');

    final result = await _otaFirmwareFacade!.downloadFirmware(firmware);

    // Handle the result using fold
    Uint8List? firmwareData;
    result.fold(
      onFailure: (failure) => throw Exception(
          'Failed to download firmware from Firebase Storage: $failure'),
      onSuccess: (data) => firmwareData = data,
    );

    if (firmwareData == null) {
      throw Exception('Firmware data is null after download');
    }

    print(
        '✅ [FIRMWARE DEBUG] Successfully loaded ${firmwareData!.length} bytes from Firebase Storage');

    return firmwareData!;
  }

  /// Get latest firmware information from Firebase
  /// Returns null if no firmware is available
  static Future<OtaFirmwareModel?> getLatestFirmwareFromFirebase({
    String deviceType = 'juno_v1',
    bool includeUnstable = false,
  }) async {
    if (_otaFirmwareFacade == null) {
      throw Exception('FirmwareManager not initialized with Firebase facade');
    }

    print('🔍 [FIRMWARE DEBUG] Getting latest firmware from Firebase...');
    print('   Device Type: $deviceType');
    print('   Include Unstable: $includeUnstable');

    final result = await _otaFirmwareFacade!.getLatestFirmware(
      deviceType: deviceType,
      includeUnstable: includeUnstable,
    );

    // Handle the result using fold
    OtaFirmwareModel? firmware;
    result.fold(
      onFailure: (failure) {
        print(
            '❌ [FIRMWARE DEBUG] Error getting latest firmware from Firebase: $failure');
        firmware = null;
      },
      onSuccess: (data) => firmware = data,
    );
    if (firmware != null) {
      print('✅ [FIRMWARE DEBUG] Found latest firmware: ${firmware!.version}');
    } else {
      print('ℹ️ [FIRMWARE DEBUG] No firmware found in Firebase');
    }

    return firmware;
  }
}

/// Information extracted from a DFU package
class DfuPackageInfo {
  final String manifestContent;
  final Uint8List applicationData;
  final int packageSize;

  const DfuPackageInfo({
    required this.manifestContent,
    required this.applicationData,
    required this.packageSize,
  });

  /// Get application data size
  int get applicationSize => applicationData.length;

  /// Check if package has valid structure
  bool get isValid => manifestContent.isNotEmpty && applicationData.isNotEmpty;
}
