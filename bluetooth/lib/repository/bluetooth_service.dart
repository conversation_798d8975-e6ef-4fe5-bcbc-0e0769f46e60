import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:bluetooth/domain/facade/bluetooth_facade.dart';
import 'package:bluetooth/domain/failure/bluetooth_failure.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:doso/doso.dart';
import '../domain/core/unit.dart';
import 'package:injectable/injectable.dart';
// removed unused imports
import 'package:remote/domain/model/battery_level_model.dart';
import 'package:remote/domain/model/commands_model.dart';
import 'package:remote/domain/model/device_info_model.dart';
import 'package:remote/domain/model/device_model.dart';
import 'package:remote/domain/model/heat_level.dart';
import 'package:remote/domain/model/tens_level_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'bluetooth_constants.dart';

/// This class implements the IBluetoothFacade interface
@LazySingleton(as: IBluetoothFacade)
class BluetoothServiceRepository implements IBluetoothFacade {
  final String _deviceName = 'Juno Lilly';
  late BluetoothDevice bluetoothDevice;
  DeviceModel _deviceModel = DeviceModel(
    deviceInfo: DeviceInfoModel(
        deviceName: 'Juno Lilly',
        deviceAddress: '',
        deviceType: 'Juno Lilly',
        deviceId: '',
        batteryLevel: BatteryLevelModel(batteryLevel: 0)),
    isDeviceOn: true,
    heatLevel: HeatLevelModel(selectedHeatLevel: 0, actualHeatLevel: 0),
    tensLevel:
        TensLevelModel(selectedTensLevel: 0, actualTensLevel: 0, mode: 1),
    recentCommands: [],
  );
  bool _shouldStopScan = false;
  late List<BluetoothService> bluetoothService;
  List<BluetoothDevice> bluetoothDevices = [];
  List<DeviceModel> savedDevices = [];

  // Note: controllers were removed because they were unused; add back if needed
  // Method to get paired devices
  @override
  Future<Do<BluetoothFailure, List<BluetoothDevice>>> getPairedDevices() {
    throw UnimplementedError();
  }

  // Method to pair a device
  @override
  Future<Do<BluetoothFailure, BluetoothDevice>> pairDevice(
      String device, bool initial) async {
    try {
      // Check if device is in unpaired list - only prevent AUTOMATIC reconnection to explicitly unpaired devices
      // Allow manual connection attempts (initial: true) even for unpaired devices
      final prefs = await SharedPreferences.getInstance();
      final unpairedDevices = prefs.getStringList('unpairedDevices') ?? [];
      if (unpairedDevices.contains(device) && !initial) {
        print(
            "🚫 Device $device is in unpaired list - preventing automatic reconnection");
        return Do.failure(BluetoothFailure.deviceConnectionFailed());
      }

      // If this is a manual connection attempt to an unpaired device, remove it from unpaired list
      if (unpairedDevices.contains(device) && initial) {
        print(
            "✅ Manual connection attempt to unpaired device $device - removing from unpaired list");
        unpairedDevices.remove(device);
        await prefs.setStringList('unpairedDevices', unpairedDevices);
      }
    } catch (e) {
      print("❌ Error handling unpaired devices list: $e");
      // Continue with connection attempt even if unpaired list handling fails
    }

    int retryCount = 0;
    const int maxRetries = 1;
    _shouldStopScan = initial;
    while (retryCount < maxRetries) {
      debugPrint('Retrying to connect to device: $retryCount');
      try {
        bluetoothDevice = bluetoothDevices.isEmpty
            ? BluetoothDevice.fromId(device)
            : bluetoothDevices.firstWhere(
                (element) =>
                    element.remoteId.str.toLowerCase() == device.toLowerCase(),
              );

        final isConnected = bluetoothDevice.isConnected;
        if (isConnected) {
          bluetoothService = await bluetoothDevice.discoverServices();
          return Do.success(bluetoothDevice);
        } else {
          try {
            await bluetoothDevice.connect(mtu: null);
            bluetoothService = await bluetoothDevice.discoverServices();
            await saveDevice(bluetoothDevice);
            bluetoothService.forEach((service) {
              debugPrint('Service UUID: ${service.uuid}');
              service.characteristics.forEach((characteristic) {
                debugPrint('Characteristic UUID: ${characteristic.uuid}');
              });
            });
            return Do.success(bluetoothDevice);
            //print characteristics
          } on TimeoutException {
            retryCount++;
            if (retryCount >= maxRetries) {
              return Do.failure(BluetoothFailure.searchTimeout());
            }
          } on FlutterBluePlusException {
            retryCount++;
            if (retryCount >= maxRetries) {
              return Do.failure(BluetoothFailure.deviceConnectionFailed());
            }
          }
        }
      } catch (e) {
        retryCount++;
        if (retryCount >= maxRetries) {
          return Do.failure(const BluetoothFailure.deviceConnectionFailed());
        }
      }
    }
    return Do.failure(BluetoothFailure.deviceConnectionFailed());
  }

  // Method to unpair a device
  @override
  Future<Do<BluetoothFailure, void>> unpairDevice(
      BluetoothDevice device) async {
    try {
      print("🔄 Starting unpair process for device: ${device.remoteId.str}");

      // Keep device in saved devices but mark as unpaired to prevent auto-reconnection
      final prefs = await SharedPreferences.getInstance();

      // Add device to unpaired devices list to prevent auto-reconnection
      final unpairedDevices = prefs.getStringList('unpairedDevices') ?? [];
      if (unpairedDevices.contains(device.remoteId.str)) {
        print(
            "🚫 Device ${device.remoteId.str} is in unpaired list - preventing reconnection");
        return Do.failure(const BluetoothFailure.deviceConnectionFailed());
      }

      // Clear any device-specific preferences
      await prefs.remove('lastConnectedDevice_${device.remoteId.str}');
      await prefs.remove('deviceSettings_${device.remoteId.str}');

      // Clear recent device if it matches the unpaired device
      final recentDeviceJson = prefs.getString('recentDevice');
      if (recentDeviceJson != null) {
        final recentDevice = DeviceModel.fromJson(jsonDecode(recentDeviceJson));
        if (recentDevice.deviceInfo.deviceId == device.remoteId.str) {
          await prefs.remove('recentDevice');
          print("✅ Cleared recent device reference");
        }
      }

      // Now disconnect the device
      if (device.isConnected) {
        print("🔌 Disconnecting device...");
        await device.disconnect();
        print("✅ Device disconnected");

        // Wait a bit to ensure disconnection is complete
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Stop any ongoing scanning to prevent immediate reconnection
      _shouldStopScan = true;
      await FlutterBluePlus.stopScan();
      print("🛑 Stopped scanning to prevent auto-reconnection");

      // Additional delay to ensure all systems have processed the disconnection
      await Future.delayed(const Duration(milliseconds: 1000));
      print("⏱️ Unpair process completed with delay");

      return Do.success(Unit);
    } catch (e) {
      return Do.failure(BluetoothFailure.deviceUnpairFailed());
    }
  }

  // Method to check if Bluetooth is on
  @override
  Stream<bool> isBluetoothOn() {
    FlutterBluePlus.setLogLevel(LogLevel.none, color: false);
    return FlutterBluePlus.adapterState
        .map((state) => state == BluetoothAdapterState.on);
  }

  // Method to search for devices
  @override
  Future<Do<BluetoothFailure, List<BluetoothDevice?>?>>
      searchForDevices() async {
    try {
      debugPrint('Searching for devices..........................');

      // Check if already scanning to prevent "scanning too frequently" error
      if (await FlutterBluePlus.isScanning.first) {
        debugPrint('Already scanning, waiting for current scan to complete...');
        await FlutterBluePlus.isScanning.where((val) => val == false).first;
        await Future.delayed(const Duration(
            milliseconds: 500)); // Brief delay after scan completes
      }

      late List<ScanResult> scanResult;
      bluetoothDevices.clear();
      final subscription = FlutterBluePlus.scanResults.listen((results) {
        scanResult = results;
      });

      try {
        await FlutterBluePlus.startScan(withKeywords: [
          'Lily',
          'lily',
          'Juno Lilly',
          'juno lilly',
          'Juno',
          'juno',
          'Snaider'
        ], timeout: const Duration(seconds: 4));
        FlutterBluePlus.cancelWhenScanComplete(subscription);
        await FlutterBluePlus.isScanning.where((val) => val == false).first;
      } catch (scanError) {
        debugPrint('Scan error (possibly too frequent): $scanError');
        await subscription.cancel();
        // Return empty list instead of failing completely
        return Do.success([]);
      }

      for (ScanResult result in scanResult) {
        debugPrint(result.device.platformName);
        // Include all devices in scan results, even those with empty platform names
        // This is important for saved device detection
        bluetoothDevices.addOrUpdate(result.device);
      }

      return Do.success(bluetoothDevices.toList());
    } catch (e) {
      debugPrint('General search error: $e');
      return Do.failure(BluetoothFailure.searchTimeout());
    }
  }

  // Method to listen to a device
  @override
  Stream<BluetoothConnectionState> listenToDevice(BluetoothDevice device) {
    return device.connectionState.asBroadcastStream();
  }

  //function to print all services and characteristics of a device
  Future<Do<BluetoothFailure, List<BluetoothService>>> getDeviceServices(
      BluetoothDevice device) async {
    try {
      bluetoothService = await device.discoverServices();
      bluetoothService.forEach((service) {
        debugPrint('Service UUID: ${service.uuid}');
        service.characteristics.forEach((characteristic) {
          debugPrint('Characteristic UUID: ${characteristic.uuid}');
        });
      });
      return Do.success(bluetoothService);
    } catch (e) {
      return Do.failure(BluetoothFailure.getCommandFailed());
    }
  }

  // Method to listen to device indications
  @override
  Stream<Do<BluetoothFailure, List<int>>> listenToIndication(
      Commands command) async* {
    try {
      debugPrint('Setting up indication listener for: ${command.commandType}');
      final String serviceUuid;
      final String characteristicUuid;
      getDeviceServices(bluetoothDevice);

      // Determine which service and characteristic to use based on command type
      if (command.commandType == 'measuredIntensity') {
        serviceUuid = BluetoothConstants.tensControlServiceUUID;
        characteristicUuid = BluetoothConstants.measuredIntensityUUID;
      } else if (command.commandType == 'currentTemp') {
        serviceUuid = BluetoothConstants.temperatureControlServiceUUID;
        characteristicUuid = BluetoothConstants.currentTemperatureUUID;
      } else if (command.commandType == 'activeTens') {
        serviceUuid = BluetoothConstants.tensControlServiceUUID;
        characteristicUuid = BluetoothConstants.activeTensUUID;
      } else if (command.commandType == 'setTensMode') {
        serviceUuid = BluetoothConstants.tensControlServiceUUID;
        characteristicUuid = BluetoothConstants.tensModeUUID;
      } else if (command.commandType == 'batteryLevel') {
        serviceUuid = BluetoothConstants.batteryServiceUUID;
        characteristicUuid = BluetoothConstants.batteryLevelUUID;
      } else {
        debugPrint('Unknown indication type: ${command.commandType}');
        yield Do.failure(BluetoothFailure.getCommandFailed());
        return;
      }

      final service = await _getService(serviceUuid);
      final characteristic =
          await _getCharacteristic(service, characteristicUuid);

      // Log characteristic properties for battery debugging
      if (command.commandType == 'batteryLevel') {
        debugPrint(
            'Battery characteristic properties: ${characteristic.properties}');
        debugPrint('Battery characteristic UUID: ${characteristic.uuid}');
        debugPrint('Battery service UUID: ${service.uuid}');
      }

      // Enable notifications (indications)
      await characteristic.setNotifyValue(true);
      debugPrint(
          'Notifications enabled for ${command.commandType} characteristic');

      // Listen to characteristic value changes
      yield* characteristic.lastValueStream
          .map<Do<BluetoothFailure, List<int>>>((value) {
        if (value.isNotEmpty) {
          if (command.commandType == 'tensIndication') {
            debugPrint('Received TENS indication value: $value');
          } else {
            //debugPrint('Received HEAT indication value: $value');
          }
          return Do.success(value);
        } else {
          debugPrint('Empty indication value received');
          return Do.failure(BluetoothFailure.getCommandFailed());
        }
      }).handleError((error) {
        print('Error in indication stream: $error');
        return Do.failure(BluetoothFailure.getCommandFailed());
      });
    } catch (e) {
      print('Exception in listenToIndication: $e');
      yield Do.failure(BluetoothFailure.getCommandFailed());
    }
  }

  // Method to check if a device is connected
  @override
  Future<Do<BluetoothFailure, BluetoothDevice?>> deviceConnected() async {
    try {
      List<BluetoothDevice> connectedDevices = Platform.isIOS
          ? List<BluetoothDevice>.from(
              await FlutterBluePlus.systemDevices(<Guid>[]))
          : List<BluetoothDevice>.from(await FlutterBluePlus.bondedDevices);
      connectedDevices.addAll(await FlutterBluePlus.connectedDevices);

      print(connectedDevices);

      if (connectedDevices.isNotEmpty) {
        BluetoothDevice? device = connectedDevices.firstWhere(
          (element) => element.platformName.toLowerCase().contains(_deviceName),
          orElse: () => BluetoothDevice(remoteId: const DeviceIdentifier('')),
        );

        if (device.platformName == 'Lily') {
          // Check if device is in unpaired list - prevent reconnection to explicitly unpaired devices
          final prefs = await SharedPreferences.getInstance();
          final unpairedDevices = prefs.getStringList('unpairedDevices') ?? [];
          if (unpairedDevices.contains(device.remoteId.str)) {
            print(
                "🚫 Device ${device.remoteId.str} is in unpaired list - preventing reconnection");
            return Do.failure(const BluetoothFailure.deviceConnectionFailed());
          }

          if (device.isConnected) {
            return Do.success(device);
          } else {
            try {
              pairDevice(device.remoteId.str, false);
              return Do.success(device);
            } catch (e) {
              return Do.failure(
                  const BluetoothFailure.deviceConnectionFailed());
            }
          }
        } else {
          return Do.failure(BluetoothFailure.deviceConnectionFailed());
        }
      } else {
        return Do.failure(BluetoothFailure.noDevicesFound());
      }
    } catch (e) {
      print(e);
      return Do.failure(BluetoothFailure.unexpected());
    }
  }

  @override
  Future<Do<BluetoothFailure, bool>> saveDevice(BluetoothDevice device) async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final savedDevicesJson = prefs.getStringList('savedDevices') ?? [];

      // Remove device from unpaired list if it exists (user is explicitly pairing again)
      final unpairedDevices = prefs.getStringList('unpairedDevices') ?? [];
      if (unpairedDevices.contains(device.remoteId.str)) {
        unpairedDevices.remove(device.remoteId.str);
        await prefs.setStringList('unpairedDevices', unpairedDevices);
        print("✅ Removed device from unpaired list - user is pairing again");
      }

      // Check if the device is already saved
      for (var deviceJson in savedDevicesJson) {
        final savedDevice = DeviceModel.fromJson(jsonDecode(deviceJson));
        if (savedDevice.deviceInfo.deviceId == device.remoteId.str) {
          // Update lastConnected time
          savedDevicesJson.remove(deviceJson);
          final updatedDevice =
              savedDevice.copyWith(lastConnected: DateTime.now());
          savedDevicesJson.add(jsonEncode(updatedDevice.toJson()));
          await prefs.setStringList('savedDevices', savedDevicesJson);

          // Save as recent device
          await prefs.setString(
              'recentDevice', jsonEncode(updatedDevice.toJson()));

          return Do.success(false); // Device already saved
        }
      }

      final deviceModel = DeviceModel(
        deviceInfo: DeviceInfoModel(
            deviceName: device.platformName,
            deviceAddress: device.remoteId.str,
            deviceType: device.platformName,
            deviceId: device.remoteId.str,
            batteryLevel: BatteryLevelModel(batteryLevel: 0)),
        isDeviceOn: true, // Assuming the device is on when saving
        // Assuming full battery
        heatLevel: HeatLevelModel(selectedHeatLevel: 0, actualHeatLevel: 0),
        tensLevel:
            TensLevelModel(selectedTensLevel: 0, actualTensLevel: 0, mode: 1),
        recentCommands: [],
        lastConnected: DateTime.now(),
      );

      final deviceJson = jsonEncode(deviceModel.toJson());
      savedDevicesJson.add(deviceJson);

      await prefs.setStringList('savedDevices', savedDevicesJson);

      // Save as recent device
      await prefs.setString('recentDevice', deviceJson);

      return Do.success(true);
    } catch (e) {
      return Do.failure(BluetoothFailure.saveDeviceFailed());
    }
  }

  // Method to stop background scanning
  @override
  void stopBackgroundScanning() {
    _shouldStopScan = true;
  }

  // Method to get a saved device
  @override
  Stream<Do<BluetoothFailure, List<DeviceModel>>> getSavedDevicesList() async* {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final savedDevicesJson = prefs.getStringList('savedDevices') ?? [];
      final unpairedDevices = prefs.getStringList('unpairedDevices') ?? [];

      final savedDevices = savedDevicesJson.map((deviceJson) {
        return DeviceModel.fromJson(jsonDecode(deviceJson));
      }).toList();

      // Return saved devices first
      yield Do.success(savedDevices);
      _shouldStopScan = false;
      int consecutiveFailures = 0;
      const int maxConsecutiveFailures = 3;

      while (!_shouldStopScan) {
        print('Scanning for devices');
        final result = await searchForDevices();

        result.fold(
          onFailure: (failure) {
            print('Error scanning for devices: $failure');
            consecutiveFailures++;

            // If we have too many consecutive failures, increase delay to prevent spam
            if (consecutiveFailures >= maxConsecutiveFailures) {
              print('Too many consecutive scan failures, extending delay...');
            }
          },
          onSuccess: (scannedDevices) {
            print('Found ${scannedDevices?.length ?? 0} devices in scan');
            consecutiveFailures = 0; // Reset failure counter on success

            for (var savedDevice in savedDevices) {
              print(
                  'Checking saved device: ${savedDevice.deviceInfo.deviceId}');

              bool wasFound = scannedDevices!.any((device) =>
                  device!.remoteId.str == savedDevice.deviceInfo.deviceId);

              // For unpaired devices, show them as available for manual connection if they're found in scan
              if (unpairedDevices.contains(savedDevice.deviceInfo.deviceId)) {
                print(
                    '🚫 Unpaired device ${savedDevice.deviceInfo.deviceId} - available for manual connection: $wasFound');
                savedDevice.isDeviceReady =
                    wasFound; // Allow manual connection if device is found
              } else {
                savedDevice.isDeviceReady = wasFound;
                print(
                    'Device ${savedDevice.deviceInfo.deviceId} availability: $wasFound');
              }
            }
          },
        );

        yield Do.success(savedDevices);

        // Dynamic delay based on scan success/failure
        Duration delay;
        if (consecutiveFailures >= maxConsecutiveFailures) {
          delay = const Duration(
              seconds: 15); // Longer delay after repeated failures
        } else if (consecutiveFailures > 0) {
          delay =
              const Duration(seconds: 8); // Moderate delay after single failure
        } else {
          delay = const Duration(seconds: 5); // Normal delay on success
        }

        print('Waiting ${delay.inSeconds} seconds before next scan...');
        await Future.delayed(delay);
      }
    } catch (e) {
      yield Do.failure(BluetoothFailure.getSavedDeviceFailed());
    }
  }

  // // Method to connect to saved devices
  // @override
  // Future<Either<BluetoothFailure, BluetoothDevice>> connectToSavedDevices(
  //     String? savedDeviceIds) async {
  //   try {
  //     late List<ScanResult> scanResult;
  //     final subscription = FlutterBluePlus.scanResults.listen((results) {
  //       scanResult = results;
  //     });
  //     await FlutterBluePlus.startScan(
  //         withKeywords: [_deviceName], timeout: const Duration(seconds: 4));
  //     await Future.delayed(const Duration(seconds: 4));
  //     FlutterBluePlus.cancelWhenScanComplete(subscription);
  //     for (ScanResult result in scanResult) {
  //       if (savedDeviceIds == result.device.remoteId.str) {
  //         try {
  //           await result.device.connect(
  //               timeout: const Duration(seconds: 60),
  //               autoConnect: false,
  //               mtu: null);
  //           await Future.delayed(const Duration(seconds: 10));
  //           result.device.connectionState.listen((event) {});
  //           return Right(result.device);
  //         } catch (e) {
  //           return const Left(BluetoothFailure.deviceConnectionFailed());
  //         }
  //       }
  //     }
  //     return const Left(BluetoothFailure.noDevicesFound());
  //   } catch (e) {
  //     return const Left(BluetoothFailure.noDevicesFound());
  //   }
  // }

  // Method to get information from device every 2 seconds
  @override
  Stream<Do<BluetoothFailure, DeviceModel>> getDeviceInformation() {
    // Read a single characteristic periodically and update the in-memory device model.
    // Previous implementation created a nested Timer.periodic per tick which leaked timers
    // and performed non-mutating copyWith calls. This implementation performs a single
    // read per tick and updates `_deviceModel` using copyWith so changes are applied.
    return Stream.periodic(const Duration(milliseconds: 500), (_) => null)
        .asyncMap((_) async {
      try {
        // Defensive: ensure services have been discovered
        if (bluetoothService.isEmpty) {
          return Do.success(_deviceModel);
        }

        // Find the expected service and characteristic UUIDs
        final matchingServices = bluetoothService
            .where(
                (s) => s.uuid == Guid('00002a46-0000-1000-8000-00805f9b34f9'))
            .toList();
        if (matchingServices.isEmpty) {
          return Do.success(_deviceModel);
        }
        final service = matchingServices.first;

        final matchingCharacteristics = service.characteristics
            .where(
                (c) => c.uuid == Guid('00002a47-0000-1000-8000-00805f9b34f8'))
            .toList();
        if (matchingCharacteristics.isEmpty) {
          return Do.success(_deviceModel);
        }
        final characteristic = matchingCharacteristics.first;

        // Perform a single read and update the model
        final value = await characteristic.read();
        if (value.isNotEmpty) {
          final int tensActual = value[0];

          // ensure tensLevel exists before copying
          final updatedTens = _deviceModel.tensLevel?.copyWith(
                actualTensLevel: tensActual,
              ) ??
              // create minimal TensLevelModel if missing
              TensLevelModel(
                  selectedTensLevel: 0, actualTensLevel: tensActual, mode: 1);

          _deviceModel = _deviceModel.copyWith(tensLevel: updatedTens);
        }

        return Do.success(_deviceModel);
      } catch (e) {
        return Do.failure(BluetoothFailure.getCommandFailed());
      }
    });
  }

  //method to get current heat level and target heat level from device

  /// **Read characteristic once**
  @override
  Future<Do<BluetoothFailure, List<int>>> readDeviceCharacteristics(
      Commands command) async {
    try {
      final serviceUuid = _mapActionToServiceUuid(command.commandType);
      final characteristicUuid =
          _mapActionToCharacteristicUuid(command.commandType);

      final service = await _getService(serviceUuid);
      final characteristic =
          await _getCharacteristic(service, characteristicUuid);

      final value = await characteristic.read();
      return Do.success(value); // Assuming value is a single byte
    } catch (e) {
      return Do.failure(BluetoothFailure.getCommandFailed());
    }
  }

  Future<Do<BluetoothFailure, Unit>> sendCommand(Commands command) async {
    try {
      if (bluetoothDevice.isConnected) {
        // print(
        //     'Sending command: ${command.commandType} with +++++++++++++++++++++++++++++++++   value: ${command.value}');
        final serviceUuid = _mapActionToServiceUuid(command.commandType);
        final characteristicUuid =
            _mapActionToCharacteristicUuid(command.commandType);

        // Get the service first
        final service = await _getService(serviceUuid);
        // Then get the characteristic within the service
        final characteristic =
            await _getCharacteristic(service, characteristicUuid);
        // If there's a value to write, pass it to the characteristic

        await characteristic.write([command.value], withoutResponse: false);
      }
      return Do.success(unit);
    } catch (e) {
      print("************************************");
      print(e.toString());
      return Do.failure(BluetoothFailure.sendCommandFailed());
    }
  }

  // Map action to its corresponding service UUID
  String _mapActionToServiceUuid(String action) {
    switch (action) {
      case 'powerOff':
      case 'startTherapy':
      case 'pauseTherapy':
      case 'therapyState':
        return BluetoothConstants.deviceControlServiceUUID;
      case 'increaseHeat':
      case 'decreaseHeat':
      case 'targetTemp':
      case 'currentTemp':
      case 'heatStatus':
      case 'heatingElementControl':
        return BluetoothConstants.temperatureControlServiceUUID;
      case 'increaseTens':
      case 'decreaseTens':
      case 'targetTens':
      case 'currentTens':
      case 'setTensMode':
      case 'measuredIntensity':
      case 'tensStatus':
      case 'activeTens':
      case 'tensControl':
        return BluetoothConstants.tensControlServiceUUID;
      case 'batteryLevel':
        return BluetoothConstants.batteryServiceUUID;
      default:
        throw ArgumentError('Invalid action');
    }
  }

  // Map action to its corresponding characteristic UUID
  String _mapActionToCharacteristicUuid(String action) {
    switch (action) {
      case 'powerOff':
        return BluetoothConstants.powerStateUUID;
      case 'startTherapy':
      case 'pauseTherapy':
      case 'therapyState':
        return BluetoothConstants.therapyStateUUID;
      case 'increaseHeat':
        return BluetoothConstants.increaseTemperatureUUID;
      case 'decreaseHeat':
        return BluetoothConstants.decreaseTemperatureUUID;
      case 'increaseTens':
        return BluetoothConstants.increaseTensIntensityUUID;
      case 'decreaseTens':
        return BluetoothConstants.decreaseTensIntensityUUID;
      case 'activeTens':
        return BluetoothConstants.activeTensUUID;
      case 'setTensMode':
        return BluetoothConstants.tensModeUUID;
      case 'targetTemp':
        return BluetoothConstants.targetTemperatureUUID;
      case 'currentTemp':
        return BluetoothConstants.currentTemperatureUUID;
      case 'targetTens':
        return BluetoothConstants.targetTensIntensityUUID;
      case 'currentTens':
        return BluetoothConstants.currentTensIntensityUUID;
      case 'measuredIntensity':
        return BluetoothConstants.measuredIntensityUUID;
      case 'tensStatus':
        return BluetoothConstants.tensStatus;
      case 'heatStatus':
        return BluetoothConstants.heatStatus;
      case 'tensControl':
        return BluetoothConstants.tensControlUUID;
      case 'batteryLevel':
        return BluetoothConstants.batteryLevelUUID;
      default:
        throw ArgumentError('Invalid action');
    }
  }

  // Retrieve the service by UUID
  Future<BluetoothService> _getService(String uuid) async {
    return bluetoothService
        .where((service) => service.uuid == Guid(uuid))
        .first;
  }

  // Retrieve the characteristic within the service by UUID
  Future<BluetoothCharacteristic> _getCharacteristic(
      BluetoothService service, String uuid) async {
    service.characteristics.forEach((element) {});

    return service.characteristics
        .where((characteristic) => characteristic.uuid == Guid(uuid))
        .first;
  }

// Method to run reconnect process
  @override
  Future<Do<BluetoothFailure, BluetoothDevice>> reconnectToDevice(
      BluetoothDevice device) async {
    try {
      // Check if device is in unpaired list - prevent reconnection to explicitly unpaired devices
      final prefs = await SharedPreferences.getInstance();
      final unpairedDevices = prefs.getStringList('unpairedDevices') ?? [];
      if (unpairedDevices.contains(device.remoteId.str)) {
        print(
            "🚫 Device ${device.remoteId.str} is in unpaired list - preventing reconnection");
        return Do.failure(BluetoothFailure.reconnectFailed());
      }

      await device.connect(
          timeout: const Duration(seconds: 10), autoConnect: false, mtu: null);
      bluetoothService = await device.discoverServices();
      await saveDevice(device); // Update saved device information
      return Do.success(device);
    } catch (e) {
      return Do.failure(BluetoothFailure.reconnectFailed());
    }
  }

  @override
  Future<Do<BluetoothFailure, DeviceModel>> getRecentDevice() async {
    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final recentDeviceJson = prefs.getString('recentDevice');
      if (recentDeviceJson != null) {
        final recentDevice = DeviceModel.fromJson(jsonDecode(recentDeviceJson));

        // Check if recent device is in unpaired list - prevent reconnection to explicitly unpaired devices
        final unpairedDevices = prefs.getStringList('unpairedDevices') ?? [];
        if (unpairedDevices.contains(recentDevice.deviceInfo.deviceId)) {
          print(
              "🚫 Recent device ${recentDevice.deviceInfo.deviceId} is in unpaired list - preventing reconnection");
          return Do.failure(BluetoothFailure.getSavedDeviceFailed());
        }

        return Do.success(recentDevice);
      } else {
        return Do.failure(BluetoothFailure.getSavedDeviceFailed());
      }
    } catch (e) {
      return Do.failure(BluetoothFailure.getSavedDeviceFailed());
    }
  }

  @override
  Future<Do<BluetoothFailure, void>> stopScanning() async {
    try {
      _shouldStopScan = true;
      await FlutterBluePlus.stopScan();
      return Do.success(Unit);
    } catch (e) {
      return Do.failure(BluetoothFailure.stopScanningFailed());
    }
  }

  @override
  Future<Do<BluetoothFailure, void>> clearUnpairedDevices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('unpairedDevices');
      print("✅ Cleared unpaired devices list");
      return Do.success(Unit);
    } catch (e) {
      return Do.failure(BluetoothFailure.unexpected());
    }
  }

  @override
  Future<Do<BluetoothFailure, BluetoothDevice>> getConnectedDevice() async {
    try {
      return Do.success(bluetoothDevice);
    } catch (e) {
      return Do.failure(BluetoothFailure.getSavedDeviceFailed());
    }
  }
}
