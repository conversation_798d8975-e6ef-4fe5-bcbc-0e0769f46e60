import 'dart:typed_data';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:doso/doso.dart';
import '../core/unit.dart';

import '../failure/bluetooth_failure.dart';

/// OTA Update Progress Model
class OtaProgress {
  final int bytesTransferred;
  final int totalBytes;
  final double percentage;
  final String status;
  final String? currentStep;

  const OtaProgress({
    required this.bytesTransferred,
    required this.totalBytes,
    required this.percentage,
    required this.status,
    this.currentStep,
  });

  OtaProgress copyWith({
    int? bytesTransferred,
    int? totalBytes,
    double? percentage,
    String? status,
    String? currentStep,
  }) {
    return OtaProgress(
      bytesTransferred: bytesTransferred ?? this.bytesTransferred,
      totalBytes: totalBytes ?? this.totalBytes,
      percentage: percentage ?? this.percentage,
      status: status ?? this.status,
      currentStep: currentStep ?? this.currentStep,
    );
  }
}

/// Firmware Information Model
class FirmwareInfo {
  final String version;
  final String buildDate;
  final int size;
  final String checksum;
  final bool isCompatible;

  const FirmwareInfo({
    required this.version,
    required this.buildDate,
    required this.size,
    required this.checksum,
    required this.isCompatible,
  });
}

/// Device Firmware Information
class DeviceFirmwareInfo {
  final String currentVersion;
  final String hardwareVersion;
  final String bootloaderVersion;
  final bool supportsOta;
  final int availableSpace;

  const DeviceFirmwareInfo({
    required this.currentVersion,
    required this.hardwareVersion,
    required this.bootloaderVersion,
    required this.supportsOta,
    required this.availableSpace,
  });
}

/// IOtaFacade defines the contract for OTA (Over-The-Air) update operations.
/// It provides methods for checking firmware versions, downloading updates,
/// and managing the secure update process using MCUboot and BLE transport.
abstract class IOtaFacade {
  /// Get current device firmware information
  /// Returns device version, hardware info, and OTA capabilities
  Future<Do<BluetoothFailure, DeviceFirmwareInfo>> getDeviceFirmwareInfo(
    BluetoothDevice device,
  );

  /// Check for available firmware updates
  /// Compares current device version with latest available firmware
  Future<Do<BluetoothFailure, FirmwareInfo?>> checkForUpdates(
    BluetoothDevice device,
  );

  /// Start OTA update process
  /// Initiates secure firmware transfer using BLE DFU service
  /// Returns a stream of progress updates
  Stream<Do<BluetoothFailure, OtaProgress>> startOtaUpdate(
    BluetoothDevice device,
    Uint8List firmwareData,
  );

  /// Verify firmware file integrity
  /// Validates firmware signature and compatibility before transfer
  Future<Do<BluetoothFailure, bool>> verifyFirmwareFile(
    Uint8List firmwareData,
    BluetoothDevice device,
  );

  /// Cancel ongoing OTA update
  /// Safely stops the update process and cleans up resources
  Future<Do<BluetoothFailure, Unit>> cancelOtaUpdate(
    BluetoothDevice device,
  );

  /// Get OTA update history
  /// Returns list of previous update attempts and their status
  Future<Do<BluetoothFailure, List<OtaUpdateRecord>>> getUpdateHistory();

  /// Prepare device for OTA update
  /// Ensures device is in proper state for firmware update
  Future<Do<BluetoothFailure, Unit>> prepareDeviceForOta(
    BluetoothDevice device,
  );

  /// Finalize OTA update
  /// Completes the update process and verifies installation
  Future<Do<BluetoothFailure, Unit>> finalizeOtaUpdate(
    BluetoothDevice device,
  );
}

/// OTA Update Record for history tracking
class OtaUpdateRecord {
  final String deviceId;
  final String fromVersion;
  final String toVersion;
  final DateTime timestamp;
  final OtaUpdateStatus status;
  final String? errorMessage;
  final Duration? duration;

  const OtaUpdateRecord({
    required this.deviceId,
    required this.fromVersion,
    required this.toVersion,
    required this.timestamp,
    required this.status,
    this.errorMessage,
    this.duration,
  });
}

/// OTA Update Status enumeration
enum OtaUpdateStatus {
  pending,
  inProgress,
  completed,
  failed,
  cancelled,
}
