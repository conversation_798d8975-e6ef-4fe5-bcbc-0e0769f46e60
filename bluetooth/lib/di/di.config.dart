// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:bluetooth/application/bluetooth_service_bloc/bluetooth_service_bloc.dart'
    as _i527;
import 'package:bluetooth/application/ota_update_bloc/ota_update_bloc.dart'
    as _i613;
import 'package:bluetooth/di/ota_injectable_module.dart' as _i692;
import 'package:bluetooth/domain/facade/bluetooth_facade.dart' as _i909;
import 'package:bluetooth/domain/facade/ota_facade.dart' as _i357;
import 'package:bluetooth/domain/facade/ota_firmware_facade.dart' as _i857;
import 'package:bluetooth/repository/bluetooth_service.dart' as _i966;
import 'package:cloud_firestore/cloud_firestore.dart' as _i974;
import 'package:firebase_storage/firebase_storage.dart' as _i457;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final otaInjectableModule = _$OtaInjectableModule();
    gh.lazySingleton<_i457.FirebaseStorage>(() => otaInjectableModule.storage);
    gh.lazySingleton<_i909.IBluetoothFacade>(
        () => _i966.BluetoothServiceRepository());
    gh.lazySingleton<_i857.IOtaFirmwareFacade>(
        () => otaInjectableModule.otaFirmwareFacade(
              gh<_i974.FirebaseFirestore>(),
              gh<_i457.FirebaseStorage>(),
            ));
    gh.factory<_i613.OtaUpdateBloc>(
        () => _i613.OtaUpdateBloc(gh<_i357.IOtaFacade>()));
    gh.factory<_i527.BluetoothServiceBloc>(
        () => _i527.BluetoothServiceBloc(gh<_i909.IBluetoothFacade>()));
    return this;
  }
}

class _$OtaInjectableModule extends _i692.OtaInjectableModule {}
